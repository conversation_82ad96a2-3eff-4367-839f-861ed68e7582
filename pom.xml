<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bluefocus</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.0</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <artifactId>kol-monitor</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>kol-monitor</name>
    <packaging>pom</packaging>
    <description>kol-monitor project for Spring Boot</description>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <modules>
        <module>kol-monitor-service</module>
        <module>kol-monitor-interface</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>


</project>
