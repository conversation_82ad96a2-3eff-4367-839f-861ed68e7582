package com.bluefocus.kolmonitorinterface.dto.ppt;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2023/3/21 11:13
 * @Description:
 */
@NoArgsConstructor
@Getter
public class PPTemplate implements Serializable {
    private static final long serialVersionUID = 1L;
    private String pageKey;
    private String filePath;

    /**
     * 初始双数据
     * @param pageKey ppt模板key
     */
    public PPTemplate(String pageKey){
        this.pageKey = pageKey;
        this.filePath = pageKey +".pptx";
    }

}
