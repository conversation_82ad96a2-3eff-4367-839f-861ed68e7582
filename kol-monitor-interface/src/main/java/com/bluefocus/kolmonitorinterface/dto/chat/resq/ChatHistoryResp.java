package com.bluefocus.kolmonitorinterface.dto.chat.resq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yj<PERSON>iu
 * @date: 0009 2024/10/9 10:43
 * @description:
 */
@Data
@ApiModel("聊天历史消息列表响应对象")
public class ChatHistoryResp {

    @ApiModelProperty("会话id")
    String conversationId;

    @ApiModelProperty("聊天历史")
    List<ChatResp> chatRespList;

}
