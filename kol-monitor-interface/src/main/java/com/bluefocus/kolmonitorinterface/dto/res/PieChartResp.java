package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/14 15:18
 * @Description:
 */
@Setter
@Getter
public class PieChartResp {

    @ApiModelProperty("平台名称")
    private String platName;
    @ApiModelProperty("平台类型")
    private Integer platType;
    @ApiModelProperty("值")
    private Long value;
    @ApiModelProperty("占比")
    private BigDecimal rate;
}
