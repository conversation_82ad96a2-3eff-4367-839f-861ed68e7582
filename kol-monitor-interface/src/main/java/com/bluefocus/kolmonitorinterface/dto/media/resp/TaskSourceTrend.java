package com.bluefocus.kolmonitorinterface.dto.media.resp;

import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: yj<PERSON><PERSON>
 * @date: 0021 2024/5/21 18:54
 * @description:
 */
@Getter
@Setter
public class TaskSourceTrend {

    @ApiModelProperty("趋势线: date(日期) value(数量)")
    private List<KeyValue<String, Long>> data;

    @ApiModelProperty("指标名：互动量、声量等")
    private String indexName;
}
