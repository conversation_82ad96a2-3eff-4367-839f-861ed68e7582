package com.bluefocus.kolmonitorinterface.dto.excel;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class ExcelTrendValue {

    @ApiModelProperty("趋势数据名称")
    @JSONField(name = "name")
    private String name;

    @ApiModelProperty("趋势数据列表")
    @JSONField(name = "data_arr")
    private List<KeyValue<String, Number>> dataArr;

}
