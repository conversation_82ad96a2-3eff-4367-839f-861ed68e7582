package com.bluefocus.kolmonitorinterface.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 媒体笔记
 */
@Data
public class ExcelMediaNote implements Serializable {

//    @ExcelProperty("媒体对象ID")
//    private Long mediaObjectsId;
//
//    @ExcelProperty("处理id")
//    private Long handleId;

    @ExcelProperty("平台")
    private String sourceName;

    @ExcelProperty("头像")
    private String headImg;

    @ExcelProperty("作者")
    private String author;

    @ExcelProperty("标题")
    private String title;

    @ExcelProperty("内容")
    private String content;

    @ExcelProperty("笔记链接")
    private String noteUrl;

    @ExcelProperty("发布时间")
    private String publishTime;

    @ExcelProperty("互动量")
    private Integer interactionCnt;

    @ExcelProperty("点赞量")
    private Integer likeCnt;

    @ExcelProperty("收藏量")
    private Integer collectionCnt;

    @ExcelProperty("转发量")
    private Integer repostsCnt;

    @ExcelProperty("评论量")
    private Integer reviewCnt;

    @ExcelProperty("笔记类型")
    private String isOriginal;
//
//    @ExcelProperty("搜索关键词")
//    private String searchKeyword;

    @ExcelProperty("封面命中内容")
    private String coverOcrContent;

    @ExcelProperty("音频命中内容")
    private String audioOcrContent;

    @ExcelProperty("花字命中内容")
    private String highlightOcrContent;

    @ExcelProperty("视频文字识别内容")
    private String videoContent;


}