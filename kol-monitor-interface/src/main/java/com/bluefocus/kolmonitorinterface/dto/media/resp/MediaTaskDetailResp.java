package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value="任务详情响应对象", description="任务详情响应对象")
public class MediaTaskDetailResp implements Serializable {

    @ApiModelProperty("任务ID")
    private Long id;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("任务对象响应列表")
    private List<MediaObjectsResp> objList;

}