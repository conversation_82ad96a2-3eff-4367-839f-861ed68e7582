package com.bluefocus.kolmonitorinterface.dto.media.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Setter
@Getter
@ApiModel(value="任务保存对象", description="任务保存对象")
public class MediaTaskSaveReq implements Serializable {

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("任务对象保存列表")
    private List<MediaObjectsReq> objReqList;

}