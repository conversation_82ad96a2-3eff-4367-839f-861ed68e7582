package com.bluefocus.kolmonitorinterface.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value="博文监测请求对象", description="博文监测请求对象")
public class ArticleMonitorRequest {

    @ApiModelProperty(value = "projectId（新建项目时不传）")
    private Long projectId;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("监测周期(监测频次 1,7,15)")
    private Integer frequency;

}
