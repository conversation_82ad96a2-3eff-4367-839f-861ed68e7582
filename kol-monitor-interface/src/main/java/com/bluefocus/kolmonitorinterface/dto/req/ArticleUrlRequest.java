package com.bluefocus.kolmonitorinterface.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descirption 添加文章链接请求体
 * @date 2023/3/7 2:27 下午
 */

@Data
@ApiModel("添加文章链接请求体")
public class ArticleUrlRequest {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty(value = "文章链接", required = true)
    private String articleUrl;
}
