package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: yjLiu
 * @date: 0017 2024/6/17 12:05
 * @description:
 */
@Getter
@Setter
@ApiModel("词云图返回对象体")
public class WordCloudResp {

    @ApiModelProperty("0完成，1没有")
    Integer code = 0;
    @ApiModelProperty("词云图返回对象list")
    List<WordCloudImg> wordCloudImgList;

}
