package com.bluefocus.kolmonitorinterface.dto.media.req;

import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: yjLiu
 * @date: 0021 2024/5/21 20:13
 * @description:
 */
@Setter
@Getter
@ApiModel(value = "修改词云图", description = "修改词云图")
public class UpdateWordCloudReq extends GenWordCloudReq {

    @ApiModelProperty("数据源 1小红书 2抖音 3小红书话题 4抖音话题")
    private Integer sourceCode;

    @ApiModelProperty("词云")
    private List<KeyValue> words;
}
