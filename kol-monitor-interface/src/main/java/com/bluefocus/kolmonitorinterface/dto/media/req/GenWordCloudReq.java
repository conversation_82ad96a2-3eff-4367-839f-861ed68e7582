package com.bluefocus.kolmonitorinterface.dto.media.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yjLiu
 * @date: 0021 2024/5/21 20:13
 * @description:
 */
@Setter
@Getter
@ApiModel(value="生成词云图", description="生成词云图")
public class GenWordCloudReq {

    @ApiModelProperty("任务对象Id")
    private Long mediaObjectsId;

    @ApiModelProperty("图片地址")
    private String imgUrl;

}
