package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: yjLiu
 * @date: 0024 2024/5/24 10:33
 * @description:
 */
@Data
@ApiModel("热点分析结论")
public class AnalyzeValue {

    @ApiModelProperty("热点内容")
    private String hotContent;

    @ApiModelProperty("热点日期")
    private String date;

    @ApiModelProperty("热点日期关键词")
    private List<String> keyword;

    @ApiModelProperty("热点笔记")
    private List<HotNote> hotNotes;

    @ApiModelProperty("错误信息")
    private String message;
}
