package com.bluefocus.kolmonitorinterface.dto.media.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "任务对象保存dto", description = "任务对象保存dto")
public class MediaObjectsReq implements Serializable {

    @ApiModelProperty("对象ID")
    private Long mediaObjectsId;

    @ApiModelProperty("对象名称")
    private String name;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("截至时间")
    private Long endTime;

    @ApiModelProperty("数据源：用','链接")
    private String sourceCodes;

    @ApiModelProperty("过滤词")
    private List<String> filterword;

    @ApiModelProperty("且关键词")
    private List<List<String>> andKeywords;
    @ApiModelProperty("或关键词")
    private List<String> orKeywords;
}