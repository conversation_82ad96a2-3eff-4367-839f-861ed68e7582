package com.bluefocus.kolmonitorinterface.dto.media.resp;

import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "任务对象响应dto", description = "任务对象响应dto")
public class MediaObjectsResp implements Serializable {

    @ApiModelProperty("状态 0 有 1无数据")
    private Integer code = 0;

    @ApiModelProperty("0 未完成，1 完成")
    private Integer finish;

    @ApiModelProperty("失败描述")
    private String failDesc;

    @ApiModelProperty("对象id")
    private Long mediaObjectsId;

    @ApiModelProperty("对象名称")
    private String name;

    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("截至时间")
    private Long endTime;

    @ApiModelProperty("数据源：用','链接(1小红书、2抖音)")
    private String sourceCodes;

    @ApiModelProperty("数据源名称数组")
    private List<String> sourceNameList;

    @ApiModelProperty("过滤词")
    private List<String> filterword;

    @ApiModelProperty("且关键词")
    private List<List<String>> andKeywords;
    @ApiModelProperty("或关键词")
    private List<String> orKeywords;

    @ApiModelProperty("总互动量")
    private Long allInteraction;
    @ApiModelProperty("互动量比例:key(数据源类型) value(数量) rate(比例)")
    private List<KeyValue> interactionList;

    @ApiModelProperty("总情感量")
    private BigDecimal allSentiments;
    @ApiModelProperty("情感量比例:key(数据源类型) value(数量) rate(比例)")
    private List<KeyValue> sentimentsList;

    @ApiModelProperty("总声量")
    private Long allVolume;
    @ApiModelProperty("声量比例:key(数据源类型) rate(比例)")
    private List<KeyValue> volumeList;

    @ApiModelProperty("趋势数据")
    private List<TaskTrend> trendData;

    @ApiModelProperty("词云轮廓图")
    private String imgUrl;
    @ApiModelProperty("词云数据")
    private List<WordCloudImg> wordCloud;
}