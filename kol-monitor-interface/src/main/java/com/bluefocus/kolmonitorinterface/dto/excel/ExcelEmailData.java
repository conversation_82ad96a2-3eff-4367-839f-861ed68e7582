package com.bluefocus.kolmonitorinterface.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/9 14:25
 * @Description:
 */
@Getter
@Setter
@EqualsAndHashCode
public class ExcelEmailData {

    @ExcelProperty("项目id")
    private Long projectId;

    @ExcelProperty("文章id")
    private Long articleId;

    @ExcelProperty("状态")
    private String status;
    @ExcelProperty("状态描述")
    private String statusDesc;

    @ExcelProperty("用户输入链接")
    private String articleUrl;

    @ExcelProperty("正式链接")
    private String longUrl;


}
