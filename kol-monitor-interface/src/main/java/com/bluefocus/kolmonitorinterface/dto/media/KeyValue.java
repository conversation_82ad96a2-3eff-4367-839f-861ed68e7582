package com.bluefocus.kolmonitorinterface.dto.media;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: yj<PERSON><PERSON>
 * @date: 0021 2024/5/21 18:42
 * @description:
 */

@NoArgsConstructor
@AllArgsConstructor
@Data
public class KeyValue<T, V> implements Serializable {
    private String key;
    private String label;
    private T date;
    private V value;
    private BigDecimal rate;
    private Boolean enable;

    public KeyValue(T date,V value){
        this.date =date;
        this.value =value;
    }
}

