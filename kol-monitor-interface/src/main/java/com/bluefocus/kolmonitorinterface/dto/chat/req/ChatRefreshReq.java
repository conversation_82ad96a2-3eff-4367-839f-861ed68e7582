package com.bluefocus.kolmonitorinterface.dto.chat.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: yjLiu
 * @date: 0009 2024/10/9 10:38
 * @description:
 */
@Data
@ApiModel("刷新聊天内容请求对象dto")
public class ChatRefreshReq {

    @ApiModelProperty("消息id")
    private Long messageId;

    @ApiModelProperty("对话Id")
    private Long chatId;

    @ApiModelProperty("会话id")
    private Long conversationId;
}
