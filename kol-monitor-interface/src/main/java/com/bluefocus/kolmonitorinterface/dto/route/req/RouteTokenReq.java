package com.bluefocus.kolmonitorinterface.dto.route.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 14:18
 * @description:
 */
@Getter
@Setter
@ApiModel(value = "数据路由token基本信息", description = "数据路由token基本信息")
public class RouteTokenReq {

    @JsonProperty("bizId")
    String bizId;

}
