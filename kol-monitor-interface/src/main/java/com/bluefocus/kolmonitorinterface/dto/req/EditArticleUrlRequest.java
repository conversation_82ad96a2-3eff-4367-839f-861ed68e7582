package com.bluefocus.kolmonitorinterface.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descirption 修改文章链接请求体
 * @date 2023/3/7 2:27 下午
 */

@Data
@ApiModel("修改文章链接请求体")
public class EditArticleUrlRequest {

    @ApiModelProperty("文章ID")
    private Long articleId;

    @ApiModelProperty("文章链接")
    private String articleUrl;
}
