package com.bluefocus.kolmonitorinterface.dto.chat.resq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: yjLiu
 * @date: 0009 2024/10/9 10:43
 * @description:
 */
@Data
@ApiModel("chat流式对话响应对象")
public class ChatStreamResp {

    @ApiModelProperty("消息id")
    private Long messageId;

    @ApiModelProperty("对话Id")
    private Long chatId;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("chat内容")
    private String content;

    @ApiModelProperty("对话状态 created in_progress completed")
    private String status;

}
