package com.bluefocus.kolmonitorinterface.dto.route.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 14:15
 * @description:
 */
@Getter
@Setter
@ApiModel(value="数据路由token基本信息", description="数据路由token基本信息")
public class RouteTokenResp {

    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty("失效时间")
    private Long expireTime;

}
