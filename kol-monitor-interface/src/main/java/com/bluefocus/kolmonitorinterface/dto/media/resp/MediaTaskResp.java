package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
@ApiModel(value="任务响应对象", description="任务响应对象")
public class MediaTaskResp implements Serializable {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("任务名称")
    private String name;

    @ApiModelProperty("项目状态 0:分析中 1:已完成")
    private Integer status;

    @ApiModelProperty("创建时间")
    private Long createTime;

}