package com.bluefocus.kolmonitorinterface.dto.chat.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: yjLiu
 * @date: 0009 2024/10/9 10:38
 * @description:
 */
@Data
@ApiModel("用户聊天请求对象dto")
public class ChatReq {

    @ApiModelProperty("分析对象ID")
    Long mediaObjectsId;

    @ApiModelProperty("会话id，为null时自动创建会话id")
    Long conversationId;

    @ApiModelProperty("对话id，为null时自动创建对话id")
    Long chatId;

    @ApiModelProperty(value = "角色：user,system", required = true)
    String role;

    @ApiModelProperty("用户输入内容")
    String content;

    @ApiModelProperty("是否刷新回答，默认false")
    Boolean refresh = false;

}
