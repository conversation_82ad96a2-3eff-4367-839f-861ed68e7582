package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @Descirption 文章详情
 * @date 2022/1/12 3:21 下午
 */
@Setter
@Getter
@ApiModel("文章详情")
public class ArticleDetailResponse extends ArticleUrlResp {

    @ApiModelProperty("达人名称")
    private String kolName;

    @ApiModelProperty("文章名称")
    private String articleTitle;

    @ApiModelProperty("文章封面")
    private String imagesCover;

    @ApiModelProperty("作者头像")
    private String headUrl;

    @ApiModelProperty("发布时间")
    private String postTime;

    @ApiModelProperty("数据更新周期")
    private String monitorPeriod;

    @ApiModelProperty("阅读量")
    private Integer readNum;

    @ApiModelProperty("点赞数")
    private Integer likeNum;

    @ApiModelProperty("评论数")
    private Integer commentNum;

    @ApiModelProperty("收藏数")
    private Integer collectionNum;

    @ApiModelProperty("转发数")
    private Integer shareNum;

    @ApiModelProperty("互动数")
    private Integer interactionNum;

    @ApiModelProperty(value = "数据最近一次更新时间:到日")
    private Long updateDate;

    @ApiModelProperty("曝光量")
    private Integer exposure;

    @ApiModelProperty("粉丝数")
    private Integer fansNum;

    @ApiModelProperty("关注数")
    private Integer followNum;

    @ApiModelProperty("互动率")
    private BigDecimal interactionRate;

    @ApiModelProperty("完播率")
    private BigDecimal finishRate;

    @ApiModelProperty("弹幕数")
    private Integer bulletNum;

    @ApiModelProperty("投币数")
    private Integer coinNum;

    @ApiModelProperty("看后回搜次数")
    private Integer searchAfterViewPv;

    @ApiModelProperty("看后回搜人数")
    private Integer searchAfterViewUv;

    @ApiModelProperty("回搜次数")
    private Integer searchAfterPv;

    @ApiModelProperty("回搜人数")
    private Integer searchAfterUv;

    @ApiModelProperty("组件点击率")
    private BigDecimal pluginClickRate;

    @ApiModelProperty("组件展示数")
    private Integer pluginShowNum;

    @ApiModelProperty("组件点击数")
    private Integer pluginClickNum;

}
