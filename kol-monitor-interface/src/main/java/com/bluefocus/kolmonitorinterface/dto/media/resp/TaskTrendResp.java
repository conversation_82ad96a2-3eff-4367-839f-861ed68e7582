package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: yjLiu
 * @date: 0017 2024/6/17 12:05
 * @description:
 */
@Getter
@Setter
@ApiModel("趋势图返回对象")
public class TaskTrendResp {

    @ApiModelProperty("0完成，1没有，2分析时间小于N天")
    Integer code = 0;
    @ApiModelProperty("趋势图list")
    List<TaskTrend> taskTrendList;

}
