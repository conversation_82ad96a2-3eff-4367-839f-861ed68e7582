package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yj<PERSON>iu
 * @date: 0024 2024/5/24 19:45
 * @description:
 */
@Setter
@Getter
@ApiModel(value="热点笔记", description="热点笔记")
public class HotNote {

    @ApiModelProperty("笔记链接")
    private String url;

    @ApiModelProperty("笔记标题")
    private String title;
}
