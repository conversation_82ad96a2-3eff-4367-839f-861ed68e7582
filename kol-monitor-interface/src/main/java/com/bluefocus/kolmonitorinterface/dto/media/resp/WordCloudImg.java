package com.bluefocus.kolmonitorinterface.dto.media.resp;

import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: yj<PERSON><PERSON>
 * @date: 0021 2024/5/21 19:39
 * @description:
 */
@Getter
@Setter
public class WordCloudImg {

    @ApiModelProperty("各平台词云状态")
    Integer code = 0;

    @ApiModelProperty("词云: key词、value出现次数、enable是否启用")
    private List<KeyValue> words;

    @ApiModelProperty("数据源")
    private Integer sourceCode;

    @ApiModelProperty("数据源名称")
    private String sourceName;

    @ApiModelProperty("词云图")
    private String imgUrl;

    @ApiModelProperty("高")
    Integer height;

    @ApiModelProperty("宽")
    Integer width;
}
