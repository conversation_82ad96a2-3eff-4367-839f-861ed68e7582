package com.bluefocus.kolmonitorinterface.dto.route.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 14:15
 * @description:
 */
@Getter
@Setter
@ApiModel(value="数据路由任务基本信息", description="数据路由任务基本信息")
public class RouteTaskResp {

    @ApiModelProperty("jobId")
    private String jobId;

    @ApiModelProperty("创建时间")
    private Long creatTime;

    @ApiModelProperty("三方平台")
    private String platform;

    @ApiModelProperty("任务数据量")
    private Long dataCount;

    @ApiModelProperty("任务计费")
    private Double dataCost;

}
