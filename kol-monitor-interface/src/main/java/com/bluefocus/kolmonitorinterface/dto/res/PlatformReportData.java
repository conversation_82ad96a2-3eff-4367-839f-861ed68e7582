package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Descirption 平台报告数据
 * @date 2023/3/8 15:31
 */
@Getter
@Setter
public class PlatformReportData {

    @ApiModelProperty("文章数")
    private Integer articleNum;

    @ApiModelProperty("总阅读量")
    private Long totalReadNum;

    @ApiModelProperty("总点赞数")
    private Long totalLikeNum;

    @ApiModelProperty("总评论数")
    private Long totalCommentNum;

    @ApiModelProperty("总收藏数")
    private Long totalCollectionNum;

    @ApiModelProperty("总互动数")
    private Long totalInteractionNum;

    @ApiModelProperty("总互动率")
    private BigDecimal interactionRate;

    @ApiModelProperty("总转发数")
    private Long totalShareNum;

    @ApiModelProperty("总曝光数")
    private Long totalExposure;

    @ApiModelProperty("总关注数")
    private Long totalFollowNum;

    @ApiModelProperty("总弹幕数")
    private Long totalBulletNum;

    @ApiModelProperty("总投币数")
    private Long totalCoinNum;

    @ApiModelProperty("平均阅读")
    private Integer avgReadNum;

    @ApiModelProperty("平均曝光")
    private Integer avgExposureNum;

    @ApiModelProperty("平均点赞")
    private Integer avgLikeNum;

    @ApiModelProperty("平均评论")
    private Integer avgCommentNum;

    @ApiModelProperty("平均互动数")
    private Integer avgInteractionNum;

    @ApiModelProperty("平均收藏")
    private Integer avgCollectionNum;

    @ApiModelProperty("平均转发")
    private Integer avgShareNum;

    @ApiModelProperty("平均关注")
    private Integer avgFollowNum;

    @ApiModelProperty("平均弹幕")
    private Integer avgBulletNum;

    @ApiModelProperty("平均投币")
    private Integer avgCoinNum;

    @ApiModelProperty("阅读量趋势")
    private ArticleTrend readTrend;

    @ApiModelProperty("点赞量趋势")
    private ArticleTrend likeTrend;

    @ApiModelProperty("评论量趋势")
    private ArticleTrend commentTrend;

    @ApiModelProperty("收藏量趋势")
    private ArticleTrend collectionTrend;

    @ApiModelProperty("互动量趋势")
    private ArticleTrend interactionTrend;

    @ApiModelProperty("转发量趋势")
    private ArticleTrend shareTrend;

    @ApiModelProperty("弹幕数趋势")
    private ArticleTrend bulletTrend;

    @ApiModelProperty("关注数趋势")
    private ArticleTrend followTrend;

    @ApiModelProperty("投币数趋势")
    private ArticleTrend coinTrend;

}
