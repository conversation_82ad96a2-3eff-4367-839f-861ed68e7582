package com.bluefocus.kolmonitorinterface.dto.route.req;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yj<PERSON>iu
 * @date: 0002 2024/8/2 14:18
 * @description:
 */
@Getter
@Setter
@ApiModel(value = "数据路由token基本信息", description = "数据路由token基本信息")
public class RouteTaskReq {

    @ApiModelProperty("token")
    String token;
    @ApiModelProperty("platform")
    String platform;
    @ApiModelProperty("请求方式")
    String method;
    /**
     * application/json
     * application/x-www-form-urlencoded
     */
    @ApiModelProperty("请求格式")
    String type;
    @ApiModelProperty("请求地址")
    String api;
    @ApiModelProperty("第三方接口参数")
    JSONObject data;

}
