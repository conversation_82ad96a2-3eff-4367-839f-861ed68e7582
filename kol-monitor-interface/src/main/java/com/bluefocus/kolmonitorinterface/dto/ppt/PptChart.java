package com.bluefocus.kolmonitorinterface.dto.ppt;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/20 11:50
 * @Description:
 */
@Getter
@Setter
public class PptChart implements Serializable {
    private static final long serialVersionUID = 1L;
    private String rename;
    private List<String> series;
    private List<PptChartData> chartData;

}
