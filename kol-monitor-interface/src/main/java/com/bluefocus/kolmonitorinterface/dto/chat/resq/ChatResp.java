package com.bluefocus.kolmonitorinterface.dto.chat.resq;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: yjLiu
 * @date: 0009 2024/10/9 10:43
 * @description:
 */
@Data
@ApiModel("chat对话响应对象")
public class ChatResp {

    @ApiModelProperty("消息id")
    private Long messageId;

    @ApiModelProperty("会话id")
    private String conversationId;

    @ApiModelProperty("对话Id")
    private String chatId;

    @ApiModelProperty("chat内容")
    private String content;

    @ApiModelProperty("对话状态 0未完成，1已完成，2失败，4取消")
    private Integer status;

    @ApiModelProperty("对话角色 user(用户) assistant(智能体)")
    private String role;

    @ApiModelProperty("对话时间")
    private Long chatTime;

}
