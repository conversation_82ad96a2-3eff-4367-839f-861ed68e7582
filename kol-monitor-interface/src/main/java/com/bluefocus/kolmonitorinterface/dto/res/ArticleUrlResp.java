package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yjLiu
 * @date: 0027 2025/5/27 11:33
 * @description:
 */
@Setter
@Getter
@ApiModel(value="笔记链接URL响应对象", description="笔记链接URL响应对象")
public class ArticleUrlResp {

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("文章链接")
    private String articleUrl;

    @ApiModelProperty("平台")
    private String platformName;

    @ApiModelProperty(value = "00:待监测,01:监测中,02:监测完成 10:链接识别异常,11:链接监测中异常,12:链接无效,13:数据获取异常")
    private String status;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;
}
