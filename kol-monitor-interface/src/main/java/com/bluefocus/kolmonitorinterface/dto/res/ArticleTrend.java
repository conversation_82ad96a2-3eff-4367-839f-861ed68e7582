package com.bluefocus.kolmonitorinterface.dto.res;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Descirption 博文数据变化趋势
 * @date 2022/1/12 3:20 下午
 */

@Data
public class ArticleTrend {

    @ApiModelProperty("数据类型")
    @JSONField(name = "name")
    private String name;
    @ApiModelProperty("数据列表")
    @JSONField(name = "data_arr")
    private List<TrendValue> dataArr;

}
