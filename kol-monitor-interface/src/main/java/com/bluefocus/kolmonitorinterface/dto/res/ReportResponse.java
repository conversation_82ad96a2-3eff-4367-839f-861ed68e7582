package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/8 15:17
 */
@Setter
@Getter
@ApiModel("报告返回")
public class ReportResponse {

    @ApiModelProperty("项目id")
    private  Long projectId;

    @ApiModelProperty("项目id")
    private  String projectName;

    @ApiModelProperty("监测周期")
    private String monitorPeriod;

    @ApiModelProperty("项目总计数据")
    private PlatformReportData totalReportData;

    @ApiModelProperty("抖音数据")
    private PlatformReportData dyReportData;

    @ApiModelProperty("快手数据")
    private PlatformReportData ksReportData;

    @ApiModelProperty("小红书数据")
    private PlatformReportData xhsReportData;

    @ApiModelProperty("B站数据")
    private PlatformReportData bzReportData;

    @ApiModelProperty("饼图-种草数占比")
    private List<PieChartResp> pieNotesRate;

    @ApiModelProperty("饼图-阅读量占比")
    private List<PieChartResp> pieReadRates;
}
