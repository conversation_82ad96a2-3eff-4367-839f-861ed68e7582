package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: yjLiu
 * @Date: 2023/3/14 17:26
 * @Description:
 */
@Setter
@Getter
@ApiModel("监测列表分页对象")
public class PageArticleDetail {

    @ApiModelProperty("总达人总数")
    private Integer totalKolNum;

    @ApiModelProperty("总文章地址数")
    private Integer totalUrlNum;

    @ApiModelProperty("总阅读量")
    private Long totalReadNum;

    @ApiModelProperty("总点赞数")
    private Long totalLikeNum;

    @ApiModelProperty("总评论数")
    private Long totalCommentNum;

    @ApiModelProperty("总收藏数")
    private Long totalCollectionNum;

    @ApiModelProperty("总互动数")
    private Long totalInteractionNum;

    @ApiModelProperty("分页对象")
    List<ArticleDetailResponse> pages;
}
