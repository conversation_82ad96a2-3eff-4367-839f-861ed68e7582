package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @author: yjLiu
 * @date: 0021 2024/5/21 18:54
 * @description:
 */
@Getter
@Setter
public class TaskTrend {

    @ApiModelProperty("趋势天")
    private List<TaskSourceTrend> trendDay;
    @ApiModelProperty("趋势周")
    private List<TaskSourceTrend> trendWeek;
    @ApiModelProperty("趋势月")
    private List<TaskSourceTrend> trendMonth;

    @ApiModelProperty("数据源 0全部 1小红书 2抖音")
    private Integer sourceCode;

    @ApiModelProperty("数据源")
    private String sourceName;

    @ApiModelProperty("热点结论")
    private List<AnalyzeValue> hotConclusions;
}
