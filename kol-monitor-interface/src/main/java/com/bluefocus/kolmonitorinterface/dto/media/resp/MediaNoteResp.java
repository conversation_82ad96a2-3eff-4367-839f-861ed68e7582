package com.bluefocus.kolmonitorinterface.dto.media.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value="笔记响应对象", description="笔记响应对象")
public class MediaNoteResp implements Serializable {

    @ApiModelProperty("笔记主键")
    private Long id;

    @ApiModelProperty("头像")
    private String headImg;

    @ApiModelProperty("作者")
    private String author;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("内容")
    private String content;

    @ApiModelProperty("笔记链接")
    private String noteUrl;

    @ApiModelProperty("发布时间")
    private Long publishTime;

    @ApiModelProperty("互动量")
    private Integer interactionCnt;

    @ApiModelProperty("点赞量")
    private Integer likeCnt;

    @ApiModelProperty("收藏量")
    private Integer collectionCnt;

    @ApiModelProperty("转发量")
    private Integer repostsCnt;

    @ApiModelProperty("评论量")
    private Integer reviewCnt;

    @ApiModelProperty("笔记类型")
    private String noteType;

    @ApiModelProperty("命中关键词")
    private String keywords;

    @ApiModelProperty("命中标签")
    private String hitTag;

    @ApiModelProperty("平台名称")
    private String sourceName;

    @ApiModelProperty("封面命中内容")
    private String coverOcrContent;

    @ApiModelProperty("音频命中内容")
    private String audioOcrContent;

    @ApiModelProperty("花字命中内容")
    private String highlightOcrContent;

    @ApiModelProperty("文字命中内容")
    private String videoContent;

}