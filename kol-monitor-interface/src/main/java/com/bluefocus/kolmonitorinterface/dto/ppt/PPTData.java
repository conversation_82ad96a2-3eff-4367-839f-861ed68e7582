package com.bluefocus.kolmonitorinterface.dto.ppt;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/20 11:50
 * @Description:
 */
@Getter
@Setter
public class PPTData<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private String type;
    private T data;

    public PPTData() {
    }

    public PPTData(String type,T data) {
        this.type = type;
        this.data = data;
    }

}
