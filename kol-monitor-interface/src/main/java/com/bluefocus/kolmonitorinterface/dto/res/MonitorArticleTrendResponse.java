package com.bluefocus.kolmonitorinterface.dto.res;

import com.bluefocus.kolmonitorinterface.dto.excel.ExcelTrendValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Descirption 监测文章趋势数据
 * @date 2022/1/12 3:21 下午
 */
@Setter
@Getter
@ApiModel("监测文章趋势数据")
public class MonitorArticleTrendResponse {

    @ApiModelProperty("趋势数据日期")
    private ArticleDetailResponse detailResponse;

    @ApiModelProperty("趋势数据")
    private List<ExcelTrendValue> trends;
}
