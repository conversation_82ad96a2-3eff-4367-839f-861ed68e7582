package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Descirption 博文数据变化趋势
 * @date 2022/1/12 3:20 下午
 */

@Data
public class ArticleTrendOld {

    @ApiModelProperty("数据类型")
    private String types;
    @ApiModelProperty("数据列表")
    private List<TrendValueOld> dataArr;
    @ApiModelProperty("日期列表")
    private List<String> keys;
    @ApiModelProperty("最大值")
    private Long topValue;
    @ApiModelProperty("最大值kol")
    private String topKol;

}
