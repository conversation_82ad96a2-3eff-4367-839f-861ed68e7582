package com.bluefocus.kolmonitorinterface.dto.res;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Descirption 项目列表返回
 * @date 2023/3/7 2:41 下午
 */
@Data
@ApiModel("项目列表返回体")
public class ProjectResponse {

    @ApiModelProperty("项目id")
    private Long projectId;

    @ApiModelProperty("项目id")
    private String projectName;

    @ApiModelProperty("监测平台，多个，分割")
    private String monitorPlatform;

    @ApiModelProperty("项目状态")
    private Integer projectStatus;

    @ApiModelProperty("项目状态描述")
    private String projectStatusDesc;

    @ApiModelProperty("创建时间")
    private Long createTime;

    @ApiModelProperty("监测周期")
    private String monitorPeriod;

}
