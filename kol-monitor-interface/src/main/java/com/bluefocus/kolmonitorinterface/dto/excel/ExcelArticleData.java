package com.bluefocus.kolmonitorinterface.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/9 14:25
 * @Description:
 */
@Getter
@Setter
@EqualsAndHashCode
public class ExcelArticleData {

    @ExcelProperty("链接id")
    private Long id;

    @ExcelProperty("达人名称")
    private String kolName;

    @ExcelProperty("达人主页链接")
    private String kolHomePageUrl;

    @ExcelProperty("粉丝量")
    private String followerCount;

    @ExcelProperty("文章名称")
    private String articleTitle;

    @ExcelProperty("笔记链接")
    private String articleUrl;

    @ExcelProperty("发布日期")
    private String publishDate;

    @ExcelProperty("平台")
    private String platformName;

    @ExcelProperty("监测日期")
    private String monitorDate;

    @ExcelProperty("播放量")
    private String playCount;

    @ExcelProperty("点赞数")
    private String likeNum;

    @ExcelProperty("收藏数")
    private String collectionNum;

    @ExcelProperty("评论数")
    private String commentNum;

    @ExcelProperty("转发量")
    private String forwardCount;

    @ExcelProperty("互动数")
    private String interactionNum;

    @ExcelProperty("互动率")
    private String interactionRate;

    @ExcelProperty("完播率")
    private String finishRate;

}
