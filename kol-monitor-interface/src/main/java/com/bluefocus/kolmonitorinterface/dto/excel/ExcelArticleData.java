package com.bluefocus.kolmonitorinterface.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/9 14:25
 * @Description:
 */
@Getter
@Setter
@EqualsAndHashCode
public class ExcelArticleData {

    @ExcelProperty("链接id")
    private Long id;

    @ExcelProperty("达人名称")
    private String kolName;

    @ExcelProperty("达人主页链接")
    private String kolHomePageUrl;

    @ExcelProperty("粉丝量")
    private String followerCount;

    @ExcelProperty("文章名称")
    private String articleTitle;

    @ExcelProperty("笔记链接")
    private String articleUrl;

    @ExcelProperty("发布日期")
    private String publishDate;

    @ExcelProperty("平台")
    private String platformName;

    @ExcelProperty("监测日期")
    private String monitorDate;

    @ExcelProperty("粉丝量")
    private String fansNum;

    @ExcelProperty("曝光量")
    private String exposure;

    @ExcelProperty({"播放量", "阅读量"})
    private String playCount;

    @ExcelProperty("点赞量")
    private String likeNum;

    @ExcelProperty("收藏量")
    private String collectionNum;

    @ExcelProperty("评论量")
    private String commentNum;

    @ExcelProperty("转发量")
    private String forwardCount;

    @ExcelProperty("互动量")
    private String interactionNum;

    @ExcelProperty("互动率")
    private String interactionRate;

    @ExcelProperty("完播率")
    private String finishRate;

    @ExcelProperty("关注量")
    private String followNum;

    @ExcelProperty("弹幕量")
    private Integer bulletNum;

    @ExcelProperty("投币量")
    private Integer coinNum;

    @ExcelProperty("看后回搜次数")
    private Integer searchAfterViewPv;

    @ExcelProperty("看后回搜人数")
    private Integer searchAfterViewUv;

    @ExcelProperty("回搜次数")
    private Integer searchAfterPv;

    @ExcelProperty("回搜人数")
    private Integer searchAfterUv;

    @ExcelProperty("组件点击率")
    private String pluginClickRate;

    @ExcelProperty("组件展现量")
    private Integer pluginShowNum;

    @ExcelProperty("组件点击量")
    private Integer pluginClickNum;
}
