package com.bluefocus.kolmonitorinterface;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleMonitorRequest;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.req.EditArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleUrlResp;
import com.bluefocus.kolmonitorinterface.dto.res.MonitorArticleTrendResponse;
import com.bluefocus.kolmonitorinterface.dto.res.PageArticleDetail;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/8 18:40
 * @Description:
 */
@Api(value = "文章服务", tags = "文章服务")
public interface IArticleService {

    @ApiOperation(value = "新增单条文章", notes = "新增单条文章（无项目时不传projectId）")
    ResponseBean<ArticleUrlResp> save(ArticleUrlRequest request);

    @ApiOperation(value = "批量文章导入", notes = "批量文章导入（无项目时不传projectId）")
    BaseResponseBean saveBatch(Long projectId, MultipartFile file) throws Exception;

    @ApiOperation(value = "修改单条文章", notes = "修改单条文章")
    ResponseBean<ArticleUrlResp> update(EditArticleUrlRequest request);

    @ApiOperation(value = "删除文章", notes = "删除文章")
    BaseResponseBean delete(Long id);

    @ApiOperation(value = "开始监测", notes = "开始监测")
    BaseResponseBean startMonitor(ArticleMonitorRequest request);

    @ApiOperation(value = "上传中-笔记链接列表", notes = "上传中-笔记链接列表")
    @ApiImplicitParam(name = "projectId", value = "项目ID 未提交监测时项目ID会不存在", dataType = "Integer")
    ResponseBean<List<ArticleUrlResp>> findArticleList(Long projectId);

    @ApiOperation(value = "监测中-文章分页查询", notes = "监测中-文章分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "projectId", dataType = "Integer"),
            @ApiImplicitParam(name = "platform", value = "1.小红书，2.B站，3.快手，4.抖音", dataType = "Integer")})
    ResponseBean<PageArticleDetail> findArticlePage(Long projectId, Integer platform);

    @ApiOperation(value = "文章监测详情", notes = "文章监测详情")
    @ApiImplicitParam(name = "id", value = "文章id", dataType = "Integer")
    ResponseBean<MonitorArticleTrendResponse> findArticleTrend(Long id);
}
