package com.bluefocus.kolmonitorinterface;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.kolmonitorinterface.dto.res.ReportResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/8 15:12
 */
@Api(value = "监测报告管理", tags = "监测报告管理")
public interface IReportService {

    @ApiOperation(value = "项目报告查询", notes = "项目报告查询")
    @ApiImplicitParam(name = "projectId", value = "项目ID", dataType = "Integer")
    ResponseBean<ReportResponse> findProjectReport(Long projectId);

    @ApiOperation(value = "项目报告数据导出", notes = "项目报告数据导出")
    @ApiImplicitParam(name = "projectId", value = "项目ID", dataType = "Integer")
    void exportReport(Long projectId);

    @ApiOperation(value = "生成ppt报告", notes = "生成ppt报告")
    @ApiImplicitParam(name = "projectId", value = "项目ID", dataType = "Integer")
    void exportReportForPPT(Long projectId);
}
