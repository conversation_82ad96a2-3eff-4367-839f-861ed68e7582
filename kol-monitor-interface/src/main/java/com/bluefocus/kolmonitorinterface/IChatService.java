package com.bluefocus.kolmonitorinterface;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatRefreshReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatStopReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ConversationReq;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatHistoryResp;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 * @Date: 2024/8/1 18:40
 * @Description:
 */
@Api(value = "AI社媒chatbot服务", tags = "AI社媒chatbot服务")
public interface IChatService {

    @ApiOperation(value = "最新会话消息列表", notes = "最新会话消息列表")
    ResponseBean<ChatHistoryResp> chatList(Long mediaObjectsId);

    /**
     * 流式对话
     */
    @ApiOperation(value = "聊天对话(流式)", notes = "聊天对话(流式)")
    Flux<ChatResp> chatStream(ChatReq chatReq);

    @ApiOperation(value = "聊天对话", notes = "聊天对话")
    ResponseBean<ChatResp> chatCompletion(ChatReq chatReq);

    /**
     * 清理对话 失效前会话id
     */
    @ApiOperation(value = "清理对话", notes = "清理对话")
    BaseResponseBean clean(ConversationReq request);

    /**
     * 刷新对话内容 失效前对话id 自动创建新对话
     */
    @ApiOperation(value = "刷新对话", notes = "刷新对话")
    ResponseBean<ChatResp> refreshChat(ChatRefreshReq request);

    /**
     * 停止对话 停止前对话id
     */
    @ApiOperation(value = "停止对话", notes = "停止对话")
    BaseResponseBean stopChat(ChatStopReq request);

}
