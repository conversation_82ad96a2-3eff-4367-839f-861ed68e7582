package com.bluefocus.kolmonitorinterface;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.kolmonitorinterface.dto.media.req.*;
import com.bluefocus.kolmonitorinterface.dto.media.resp.*;
import io.swagger.annotations.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/8 18:40
 * @Description:
 */
@Api(value = "媒体声量服务", tags = "媒体声量服务")
public interface IMediaService {

    @ApiOperation(value = "数据源列表", notes = "数据源列表")
    ResponseBean<List<SourceResp>> sourceList();

    @ApiOperation(value = "新增任务", notes = "新增任务")
    BaseResponseBean save(MediaTaskSaveReq request);

    @ApiOperation(value = "任务分页查询", notes = "任务分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskName", value = "任务名称", dataType = "String"),
            @ApiImplicitParam(name = "startTime", value = "开始时间戳", dataType = "Integer"),
            @ApiImplicitParam(name = "endTime", value = "结束时间戳", dataType = "Integer"),
            @ApiImplicitParam(name = "page", value = "当前页码,从1开始", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "每页显示的条数", dataType = "Integer")})
    ResponseBean<MediaTaskPageBean<MediaTaskResp>> findMediaTaskPage(String taskName, Long startTime, Long endTime, Integer page, Integer limit);

    @ApiOperation(value = "声量任务报告详情", notes = "声量任务报告详情")
    @ApiImplicitParam(name = "id", value = "任务id", dataType = "Integer")
    ResponseBean<MediaTaskDetailResp> findTaskDetail(Long id);

    @ApiOperation(value = "分析对象详情", notes = "分析对象详情")
    @ApiImplicitParam(name = "id", value = "分析对象id", dataType = "Integer")
    ResponseBean<MediaObjectsResp> findObjDetail(Long mediaObjectsId);

    @ApiOperation(value = "笔记分页查询", notes = "笔记分页查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mediaObjectsId", value = "任务对象id", dataType = "Integer"),
            @ApiImplicitParam(name = "sourceCode", value = "数据源code：1小红书 2抖音等", dataType = "Integer"),
            @ApiImplicitParam(name = "sort：interactionCnt互动量 publishTime发布时间 likeCnt点赞 collectionCnt收藏 reviewCnt评论 repostsCnt转发", value = "排序字段", dataType = "String"),
            @ApiImplicitParam(name = "sortType", value = "排序类型 0升 1降序", dataType = "Integer"),
            @ApiImplicitParam(name = "page", value = "当前页码,从1开始", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "每页显示的条数", dataType = "Integer")})
    ResponseBean<PageBean<MediaNoteResp>> findMediaNotePage(Long mediaObjectsId, String sort, Integer sortType, Integer sourceCode, Integer page, Integer limit);

    @ApiOperation(value = "上传图片", notes = "上传图片")
    ResponseBean<String> uploadPicture(MultipartFile file, Long mediaObjectsId);

    @ApiOperation(value = "ai生图", notes = "ai生图")
    ResponseBean<List<String>> genPicture(String content, Long mediaObjectsId);

    @ApiOperation(value = "生成词云图", notes = "文生图")
    ResponseBean<WordCloudResp> genWordCloud(GenWordCloudReq req);

    @ApiOperation(value = "修改词云图", notes = "修改词云图")
    ResponseBean<WordCloudImg> updateWordCloud(UpdateWordCloudReq req);

    @ApiOperation(value = "热点分析", notes = "热点分析")
    ResponseBean<TaskTrendResp> hotAnaly(Long mediaObjectsId);

    @ApiOperation(value = "编辑分析对象", notes = "编辑分析对象")
    BaseResponseBean edit(MediaObjectsReq request);

    @ApiOperation(value = "重置词云图", notes = "词云图")
    ResponseBean<WordCloudResp> resetWordCloud(ResetWordCloudReq req);

    @ApiOperation(value = "获取词云图", notes = "获取词云图")
    ResponseBean<WordCloudResp> getWordCloud(Long mediaObjectsId);

    void taskInit(Long mediaObjectsId);

    @ApiOperation(value = "下载笔记数据", notes = "下载笔记数据")
    @ApiImplicitParam(name = "mediaObjectsId", value = "任务对象id", dataType = "Integer")
    void downloadNoteData(Long mediaObjectsId);

}
