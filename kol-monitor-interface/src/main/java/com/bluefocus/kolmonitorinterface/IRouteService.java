package com.bluefocus.kolmonitorinterface;

import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.kolmonitorinterface.dto.route.req.RouteTaskReq;
import com.bluefocus.kolmonitorinterface.dto.route.req.RouteTokenReq;
import com.bluefocus.kolmonitorinterface.dto.route.resp.RouteTaskResp;
import com.bluefocus.kolmonitorinterface.dto.route.resp.RouteTokenResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @Date: 2024/8/1 18:40
 * @Description:
 */
@Api(value = "数据路由服务", tags = "数据路由服务")
public interface IRouteService {

    @ApiOperation(value = "token", notes = "token")
    ResponseBean<RouteTokenResp> token(RouteTokenReq req);

    @ApiOperation(value = "routeTaskList", notes = "任务列表")
    ResponseBean<PageBean<RouteTaskResp>> routeTaskList(String bizId, Long startTime, Long endTime, int page, int limit);

    @ApiOperation(value = "路由任务创建", notes = "路由任务创建、重启、停止")
    ResponseBean<Object> routeTaskCreat(RouteTaskReq request);

    @ApiOperation(value = "路由任务查询", notes = "路由任务查询")
    ResponseBean<Object> routeTaskQuery(RouteTaskReq request);

}
