package com.bluefocus.kolmonitorinterface;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.kolmonitorinterface.dto.res.ProjectResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

/**
 * <AUTHOR>
 * @Descirption 项目服务
 * @date 2023/3/6 10:10 上午
 */
@Api(value = "项目服务", tags = "项目服务")
public interface IProjectService {

    @ApiOperation(value = "项目列表查询", notes = "项目列表查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页码,从1开始", dataType = "Integer"),
            @ApiImplicitParam(name = "limit", value = "每页显示的条数", dataType = "Integer")})
    ResponseBean<PageBean<ProjectResponse>> findProjectList(Integer page, Integer limit);

    @ApiOperation(value = "删除项目", notes = "删除项目")
    BaseResponseBean deleteProject(Long id);

}
