eureka:
  client:
#    register-with-eureka: false
#    fetch-registry: false
    serviceUrl:
      defaultZone: http://**********:7001/eureka/

spring:
  datasource:
    url: *******************************************************************************************************************************
    username: bftest
    password: bf@test2020
    driver-class-name: com.mysql.jdbc.Driver
    druid:
      stat-view-servlet:
        login-username: bluefocue
        login-password: blue@2020

  redisson:
    address: redis://**********:6379
    password: blue@test2021
    database: 10

  kafka:
    consumer:
      group-id: monitor-dev
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    bootstrap-servers: blueview-s61:9092,blueview-s63:9092,blueview-s64:9092
    topics: dws-fs-notes\
      dws-fs-note-info\
      dws-fs-report\
      dws_dy_cloud_videos


lark:
  appid: cli_a37293ddb47a500e
  secret: 88swWpOePCpKlPaUvGomeeaU4ugqnrgN

kimi:
  serverUrl: https://api.moonshot.cn/v1/chat/completions

aliyun:
  endpoint: https://oss-cn-beijing.aliyuncs.com
  accessKeyId: LTAI4GCx3e8N5Ffnh7WfwhwN
  accessKeySecret: ******************************
  bucketName: bluefocus-test
  media:
    root-path: https://bluefocus-test.oss-cn-beijing.aliyuncs.com/
    picture-path: media/{userId}/

media:
  gpt:
    img-retry: 3
    serverUrl1: https://xiaoboteopenai.openai.azure.com/openai/deployments/
    api-key1: ********************************
    serverUrl2: https://xiaoboteopenai-ae.openai.azure.com/openai/deployments/
    api-key2: ********************************
    serverUrl3: https://xiaoboteopenai-se.openai.azure.com/openai/deployments/
    api-key3: ********************************

robot:
  url: https://open.feishu.cn/open-apis/bot/v2/hook/29bb2a43-c6ee-4a46-90a7-34f127248856
  developer: https://open.feishu.cn/open-apis/bot/v2/hook/f9636765-698f-41d4-95bd-2bc476c45af6
  developer2: https://open.feishu.cn/open-apis/bot/v2/hook/bebe1495-9ab7-4977-82a7-48028545af2e
  task: https://open.feishu.cn/open-apis/bot/v2/hook/29bb2a43-c6ee-4a46-90a7-34f127248856
  kol: https://open.feishu.cn/open-apis/bot/v2/hook/f9636765-698f-41d4-95bd-2bc476c45af6

dataStory:
  login:
    # 登录
    serverUrl: https://dc.datastory.com.cn/auth/obtain
    username: <EMAIL>
    password: lzK6yaU0
    # redis key
    redisKey: DATA:STORY:LOGIN:TOKEN
  task:
    serverUrl: https://dc.datastory.com.cn/
    # 创建数据超市任务
    marketJob: application/market/job/v1/run
    # 任务配置
    jobDetail: project/job/detail
    # 任务状态
    jobStatus: project/job/status/detail
    # 重启子任务
    reRun: job/schedule/rerun
  api:
    token: a935217ef38df028524f77f3c727166a
    serverUrl: http://api.dc.datastory.com.cn/api/
    # 总声量接口
    totalVolume: socialmedia/totalVolume
    # 总互动量接口
    totalInteraction: socialmedia/totalInteraction
    # 总情感度接口
    totalSentiments: socialmedia/totalSentiments
    # 情感分布接口
    sentimentDistribute: socialmedia/sentimentDistribute
    # 声量趋势接口
    volumeTread: socialmedia/volumeTread
    # 互动量趋势接口
    interactionTread: socialmedia/interactionTread
    #词云接口
    cloudWord: socialmedia/cloudWord
    # 原文接口
    text: socialmedia/text
    # 计数接口
    count: count
    # 取数接口
    search: search
  alg:
    # 算法服务
    serverUrl: http://***********:1033/get_hot_events
  clawl:
    # 爬虫服务
    crawlerRedisKey: DATA:STORY:CRAWLER:REDIS:KEY
    crawlerRedisCount: DATA:STORY:CRAWLER:REDIS:COUNT
    serverUrls: http://10.0.10.136:9980/juheMain
    crawlerLoseKey: DATA:STORY:CRAWLER:REDIS:LOSE:%s

azure:
  serverUrl: https://xiaoboteopenai-se.openai.azure.com/openai/deployments/
  api-key: ********************************
  model-chat: gpt-4/chat/completions?api-version=2024-02-15-preview
  model-dalle3: Dalle3/images/generations?api-version=2024-04-01-preview

word-art:
  serverUrl: https://wordart.com/api/
  generate: generate
  key: K:zlmooQSQO#~%V&s*~?m1GTd5e=^E

word-cloud:
  serverUrl: http://***********:1034/
  generate: generate

coze:
  api:
    host: https://api.coze.cn
    botid: 7428126245823168523
    # 此处为产品经理崔博文的coze应用
    key-path: /home/<USER>/kol-monitor-service/key/private_key.pem
    app-id: 1122926775811
    key-public: cg9eAMH_YGdF6J9sM0_SZuJIkhuyulOVRTVokPdd71A