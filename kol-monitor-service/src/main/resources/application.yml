server:
  port: 8013

spring:
  application:
    name: kol-monitor-service
  output:
    ansi:
      enabled: always
  jmx:
    enabled: true
  servlet:
    multipart:
      enabled: true
      max-file-size: 20MB
      max-request-size: 50MB
  profiles:
    active: dev

  mail:
    host: smtp.partner.outlook.cn
    port: 587
    username: <EMAIL>
    password: Bfg@755693
    retry: 3
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
    protocol: smtp

error-report:
  name: ${spring.application.name}
  version: project.version
  env: ${spring.profiles.active}
  app-package: com.bluefocus

eureka:
  instance:
    prefer-ip-address: true
    leaseRenewalIntervalInSeconds: 10
    health-check-url-path: /actuator/health
  client:
    registryFetchIntervalSeconds: 10

management:
  endpoints:
    web:
      exposure:
        include: "*"

  endpoint:
    health:
      show-details: ALWAYS
    shutdown:
      enabled: true
  trace:
    http:
      enabled: true
  health:
    mail:
      enabled: false

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 30000


logging:
  file: /data/web/log/${spring.application.name}/${spring.application.name}.log
  pattern:
    file: '%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID}){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx'
  level:
    com:
      bluefocus: info

swagger:
  base-package: com.bluefocus
  base-path: /**      # 文档扫描路径
  exclude-path: /actuator/**, /error  # 排除路径
  enabled: true
  title: kol-monitor接口服务
  description: 项目描述
  contact:
    name: 王瑞锋  # 维护人
    url: 维护人URL # 维护人url
    email: <EMAIL>  # 维护人邮箱