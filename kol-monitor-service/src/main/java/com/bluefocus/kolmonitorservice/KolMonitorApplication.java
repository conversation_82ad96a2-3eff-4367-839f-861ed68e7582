package com.bluefocus.kolmonitorservice;

import com.bluefocus.usercenterinterface.client.UserCenterClient;
import com.spring4all.swagger.EnableSwagger2Doc;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@EnableSwagger2Doc
@SpringBootApplication
@EnableFeignClients(clients = {UserCenterClient.class})
public class KolMonitorApplication {

    public static void main(String[] args) {
        SpringApplication.run(KolMonitorApplication.class, args);
    }

}
