package com.bluefocus.kolmonitorservice.application.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleMonitorRequest;
import com.bluefocus.kolmonitorinterface.dto.res.ProjectResponse;
import com.bluefocus.kolmonitorservice.base.enums.DeleteStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import com.bluefocus.kolmonitorservice.base.enums.ProjectStatusEnum;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDomainService;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Descirption 项目管理服务
 * @date 2022/1/10 5:36 下午
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ProjectService {

    private final UserCommon userCommon;
    private final ProjectDomainService projectDomainService;
    private final ArticleDomainService articleDomainService;

    private String monitorPeriod(Integer frequency) {
        LocalDate now = LocalTime.now().isAfter(LocalTime.of(17, 0, 0)) ? LocalDate.now().plusDays(1) : LocalDate.now();
        if (null == frequency) {
            frequency = 7;
        }
        return now + "~" + now.plusDays(frequency);
    }

    @Transactional(rollbackFor = Exception.class)
    public BaseResponseBean startMonitor(ArticleMonitorRequest request) {
        Long userId = userCommon.getUserId(true);
        Project project;
        if (null == request.getProjectId()) {
            List<Project> projects = projectDomainService.selectByName(request.getProjectName(), userId);
            Assert.isTrue(CollectionUtil.isEmpty(projects), "项目名称已存在!");
            List<Article> articleList = articleDomainService.findUnRunListByUser(userId);
            Assert.isTrue(CollectionUtil.isNotEmpty(articleList), "不存在有效链接!");

            Set<String> collect = articleList.stream().map(article -> PlatformEnum.codeOf(article.getPlatType())).collect(Collectors.toSet());
            String plats = String.join(",", collect);
            Integer frequency = request.getFrequency();
            project = projectDomainService.saveProject(request.getProjectName(), userId, frequency, monitorPeriod(frequency), plats);

        } else {
            project = projectDomainService.getById(request.getProjectId());
            Assert.isFalse(project.getStatus().equals(ProjectStatusEnum.MONITORED.getIndex()), "项目已完成，不能新增链接");
            List<Article> list = articleDomainService.findArticleList(project.getId(), false);
            Set<String> collect = list.stream().map(article -> PlatformEnum.codeOf(article.getPlatType())).collect(Collectors.toSet());
            project.setMonitorPlat(String.join(",", collect));
            project.setMonitorPeriod(monitorPeriod(project.getPeriodTime()));
            projectDomainService.updateById(project);
        }
        articleDomainService.deleteAndMonitor(project.getId(), userId);
        return BaseResponseBean.SUCCESS;
    }

    public ResponseBean<PageBean<ProjectResponse>> findProjectList(Integer page, Integer limit) {

        Long userId = userCommon.getUserId(true);
        IPage<Project> projectPages = projectDomainService.findProjectList(userId, page, limit);

        List<ProjectResponse> responses = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(projectPages.getRecords())) {
            for (Project project : projectPages.getRecords()) {
                ProjectResponse response = new ProjectResponse();
                response.setProjectId(project.getId());
                response.setProjectName(project.getName());
                response.setMonitorPlatform(project.getMonitorPlat());
                response.setProjectStatus(project.getStatus());
                response.setProjectStatusDesc(ProjectStatusEnum.codeOf(project.getStatus()));
                response.setCreateTime(Times.toEpochMilli(project.getCreateTime()));
                response.setMonitorPeriod(ObjectUtil.isNotEmpty(project.getMonitorPeriod()) ? project.getMonitorPeriod() : "~");
                responses.add(response);
            }
        }

        return ResponseBean.createPage(page, limit, projectPages.getTotal(), responses);
    }

    public BaseResponseBean deleteProject(Long id) {
        Long userId = userCommon.getUserId(true);
        projectDomainService.deleteProjectById(id, userId);
        articleDomainService.lambdaUpdate()
                .set(Article::getStatus, DeleteStatusEnum.DELETE.getCode()).eq(Article::getProjectId, id).update();
        return BaseResponseBean.SUCCESS;
    }
}
