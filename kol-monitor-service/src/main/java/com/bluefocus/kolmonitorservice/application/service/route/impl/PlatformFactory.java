package com.bluefocus.kolmonitorservice.application.service.route.impl;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 17:44
 * @description:
 */
@Component
public class PlatformFactory implements InitializingBean, ApplicationContextAware {
    private static final Map<String, IPlatform> PLAT_MAP = new ConcurrentHashMap<>();

    private ApplicationContext appContext;
    public static IPlatform getPlatform(String platform) {
        if (platform == null) {
            throw new IllegalArgumentException("platform is empty.");
        }
        if (!PLAT_MAP.containsKey(platform)) {
            throw new IllegalArgumentException("platform not supported.");
        }
        return PLAT_MAP.get(platform);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        appContext.getBeansOfType(IPlatform.class)
                .values()
                .forEach(platform -> PLAT_MAP.put(platform.getPlatform(), platform));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }
}
