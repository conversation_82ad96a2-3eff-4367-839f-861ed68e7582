package com.bluefocus.kolmonitorservice.application.valid;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.IArticleService;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleMonitorRequest;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.req.EditArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleUrlResp;
import com.bluefocus.kolmonitorinterface.dto.res.MonitorArticleTrendResponse;
import com.bluefocus.kolmonitorinterface.dto.res.PageArticleDetail;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/8 16:18
 */
public class ArticleValidHandler implements IArticleService {

    @Override
    public ResponseBean<ArticleUrlResp> save(ArticleUrlRequest request) {
        Assert.notEmpty(request.getArticleUrl(), "文章链接不能为空！");
        request.setArticleUrl(request.getArticleUrl().trim());
        Assert.isTrue(request.getArticleUrl().startsWith("http"), "输入非法链接");
        int count = pattern(request.getArticleUrl(), "http");
        Assert.isFalse(count != 1, "输入非法链接");
        return null;
    }

    @Override
    public BaseResponseBean saveBatch(Long projectId, MultipartFile file) {
        Assert.notNull(file, "文件不能为空！");
        return null;
    }

    @Override
    public ResponseBean<ArticleUrlResp> update(EditArticleUrlRequest request) {
        Assert.notNull(request.getArticleId(), "文章id不能为空！");
        Assert.notEmpty(request.getArticleUrl(), "文章链接不能为空！");
        request.setArticleUrl(request.getArticleUrl().trim());
        Assert.isTrue(request.getArticleUrl().startsWith("http"), "输入非法链接");
        int count = pattern(request.getArticleUrl(), "http");
        Assert.isFalse(count != 1, "输入非法链接");
        return null;
    }

    @Override
    public BaseResponseBean delete(Long id) {
        Assert.notNull(id, "文章id不能为空！");
        return null;
    }

    @Override
    public BaseResponseBean startMonitor(ArticleMonitorRequest request) {
        if (request.getProjectId() == null) {
            Assert.notEmpty(request.getProjectName(), "无项目ID时，项目名称不能为空！");
        }
        return null;
    }

    @Override
    public ResponseBean<List<ArticleUrlResp>> findArticleList(Long projectId) {
        return null;
    }

    @Override
    public ResponseBean<PageArticleDetail> findArticlePage(Long projectId, Integer platform) {
        Assert.notNull(projectId, "项目id不能为空！");
        Assert.notNull(platform, "platform不能为空！");
        return null;
    }

    @Override
    public ResponseBean<MonitorArticleTrendResponse> findArticleTrend(Long id) {
        Assert.notNull(id, "文章id不能为空！");
        return null;
    }

    private int pattern(String text, String target) {
        Pattern pattern = Pattern.compile(target);
        Matcher matcher = pattern.matcher(text);
        int count = 0;
        while (matcher.find()) {
            count++;
        }
        return count;
    }
}
