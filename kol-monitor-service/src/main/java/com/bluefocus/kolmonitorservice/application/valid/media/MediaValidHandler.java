package com.bluefocus.kolmonitorservice.application.valid.media;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.basebean.exception.InvalidParameterException;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.IMediaService;
import com.bluefocus.kolmonitorinterface.dto.media.req.*;
import com.bluefocus.kolmonitorinterface.dto.media.resp.*;
import com.bluefocus.kolmonitorservice.base.util.Times;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0021 2024/5/21 20:24
 * @description:
 */
@Slf4j
public class MediaValidHandler implements IMediaService {

    @Override
    public ResponseBean<List<SourceResp>> sourceList() {
        return null;
    }

    @Override
    public BaseResponseBean save(MediaTaskSaveReq request) {
        Assert.isTrue(request.getName().length() < 50, "任务名称过长");
        Assert.isTrue(request.getObjReqList().size() <= 4, "分析对象最多支持增加4个");
        List<MediaObjectsReq> collect = request.getObjReqList().stream().filter(obj -> {
            if (CollectionUtil.isEmpty(obj.getAndKeywords()) && CollectionUtil.isEmpty(obj.getOrKeywords())) {
                return true;
            }
            if (CollectionUtil.isNotEmpty(obj.getFilterword()) && obj.getFilterword().size() >= 200) {
                return true;
            }
            if (obj.getStartTime() == null || obj.getEndTime() == null) {
                return true;
            }
            LocalDateTime endTime = Times.toLocalDateTime(obj.getEndTime()).minusDays(366L);
            return endTime.compareTo(Times.toLocalDateTime(obj.getStartTime())) >= 0;
        }).collect(Collectors.toList());

        if (collect.size() > 0) {
            log.error("请求异常，请求对象内容=【{}】，涉及错误对象={}", JSON.toJSONString(request), JSON.toJSONString(collect));
            throw new InvalidParameterException("创建任务参数异常,任务名称" + request.getName());
        }

        return null;
    }

    @Override
    public ResponseBean<MediaTaskPageBean<MediaTaskResp>> findMediaTaskPage(String taskName, Long startTime, Long endTime, Integer page, Integer limit) {
        return null;
    }

    @Override
    public ResponseBean<MediaTaskDetailResp> findTaskDetail(Long id) {
        Assert.notNull(id, "分析对象为null");
        return null;
    }

    @Override
    public ResponseBean<MediaObjectsResp> findObjDetail(Long mediaObjectsId) {
        Assert.notNull(mediaObjectsId, "分析对象为null");
        return null;
    }

    @Override
    public ResponseBean<PageBean<MediaNoteResp>> findMediaNotePage(Long mediaObjectsId, String sort, Integer sortType, Integer sourceCode, Integer page, Integer limit) {
        Assert.notNull(mediaObjectsId, "分析对象为null");
        return null;
    }

    @Override
    public ResponseBean<String> uploadPicture(MultipartFile file, Long mediaObjectsId) {
        Assert.notNull(mediaObjectsId, "分析对象为null");
        return null;
    }

    @Override
    public ResponseBean<List<String>> genPicture(String content, Long mediaObjectsId) {
        Assert.notNull(mediaObjectsId, "分析对象为null");
        return null;
    }

    @Override
    public ResponseBean<WordCloudResp> genWordCloud(GenWordCloudReq req) {
        Assert.notNull(req.getImgUrl(), "未选择图片");
        Assert.notNull(req.getMediaObjectsId(), "分析对象为空");
        return null;
    }

    @Override
    public ResponseBean<WordCloudImg> updateWordCloud(UpdateWordCloudReq req) {
        Assert.notNull(req.getSourceCode(), "数据源类型为空");
        Assert.notNull(req.getMediaObjectsId(), "对象为空");
//        Assert.notNull(req.getImgUrl(), "轮廓图为空");
        Assert.notNull(req.getWords(), "词云为空");
        return null;
    }

    @Override
    public ResponseBean<TaskTrendResp> hotAnaly(Long mediaObjectsId) {
        Assert.notNull(mediaObjectsId, "分析对象为null");
        return null;
    }

    @Override
    public BaseResponseBean edit(MediaObjectsReq request) {
        Assert.notNull(request.getMediaObjectsId(), "分析对象为null");
        if (CollectionUtil.isEmpty(request.getAndKeywords()) && CollectionUtil.isEmpty(request.getOrKeywords())) {
            throw new InvalidParameterException("关键字数量不符合要求");
        }
        if (request.getFilterword().size() >= 200) {
            throw new InvalidParameterException("过滤词数量不符合要求");
        }
        if (request.getStartTime() == null || request.getEndTime() == null) {
            throw new InvalidParameterException("分析时间未输入");
        }
        if (Times.toLocalDateTime(request.getEndTime()).minusYears(1L).compareTo(Times.toLocalDateTime(request.getStartTime())) > 0) {
            throw new InvalidParameterException("分析时间不符合要求");
        }
        return null;
    }

    @Override
    public ResponseBean<WordCloudResp> resetWordCloud(ResetWordCloudReq req) {
        Assert.notNull(req.getMediaObjectsId(), "分析对象为null");
        return null;
    }

    @Override
    public ResponseBean<WordCloudResp> getWordCloud(Long mediaObjectsId) {
        Assert.notNull(mediaObjectsId, "分析对象为null");
        return null;
    }

    @Override
    public void taskInit(Long mediaObjectsId) {
    }

    @Override
    public void downloadNoteData(Long mediaObjectsId) {
        Assert.notNull(mediaObjectsId, "分析对象为null");
    }
}
