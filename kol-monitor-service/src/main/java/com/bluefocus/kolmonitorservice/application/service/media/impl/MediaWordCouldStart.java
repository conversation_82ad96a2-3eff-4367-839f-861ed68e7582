package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.domain.datastory.DataStoryDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryCloudWordResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaWord;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaWordDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 2024/5/22 20:18
 * @description:
 */
@Slf4j
@Order(4)
@Service
@RequiredArgsConstructor
public class MediaWordCouldStart extends IMediaStart {

    private final DataStoryDomainService dataStoryService;
    private final MediaWordDomainService mediaWordDomainService;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {
        DataHandleState dataHandleState = new DataHandleState();

        MediaWord mediaWord = mediaWordDomainService.findByHandleIdAndSource(handle.getId(), sourceCode);

        if (mediaWord != null && mediaWord.getIsInit().equals(FinishStatusEnum.ANALY.getCode())) {
            log.info("api当词云数据已经落库，直接跳过，handle={}，sourceCode={}", handle.getId(), sourceCode);
            dataHandleState.setCloudWord(true);
            return dataHandleState;
        }

        DataStoryEntity req = getDataStoryFixedParam(handle);
        String source = ExtraConditionEnum.codeOf(sourceCode);
        req.setSource(source);

        if (mediaWord == null) {
            mediaWord = new MediaWord();
            mediaWord.setCreateTime(LocalDateTime.now());
            mediaWord.setMediaObjectsId(handle.getMediaObjectsId());
            mediaWord.setHandleId(handle.getId());
            mediaWord.setSourceCode(sourceCode);

        }

        DataStoryCloudWordResponse.ResultDTO word = retryReq(req, 6, handle.getId());
        if (word == null || word.getTotal() == null) {
            log.error("api当词云数据异常，直接跳过，handle={}，sourceCode={}", handle.getId(), sourceCode);
            dataHandleState.setCloudWord(false);
            mediaWord.setAllWord("[]");
            mediaWord.setIsInit(FinishStatusEnum.FAIL.getCode());
            mediaWordDomainService.saveOrUpdate(mediaWord);

            MediaObjects obj = mediaObjectsDomainService.getById(handle.getId());
            objFail(handle, obj, BusinessException.KEY_OTHER, null);
            return dataHandleState;
        }

        mediaWord.setIsInit(FinishStatusEnum.ANALY.getCode());
        mediaWord.setAllWord(JSON.toJSONString(wordMap2KvLabel(word.getTotal().get(source.toLowerCase(Locale.ROOT)))));
        mediaWordDomainService.saveOrUpdate(mediaWord);

        dataHandleState.setCloudWord(true);

        return dataHandleState;
    }

    private DataStoryCloudWordResponse.ResultDTO retryReq(DataStoryEntity req, int i, Long handleId) {
        DataStoryCloudWordResponse cloudWord = null;
        try {
            i--;
            if (i < 0) {
                return null;
            }
            cloudWord = dataStoryService.getCloudWord(req);
            if (cloudWord.isSuccess()) {
                return cloudWord.getData();
            } else {
                log.warn("请求数说词频接口报错，第{}次,处理id={}", 3 - i, handleId);
            }

        } catch (Exception e) {
            log.error("请求数说词频接口报错，第{}次", 3 - i);
        } finally {
            if ((null == cloudWord || !cloudWord.isSuccess()) && i == 0) {
                String con = cloudWord != null ? cloudWord.getMsg() : null;
                handleMsg(null, String.format("请求数说词频接口异常,处理id=%s，内容：%s", handleId, con));
                log.error("请求数说词频接口重试次数耗尽：{}", JSON.toJSONString(cloudWord));
            }
        }
        return retryReq(req, --i, handleId);
    }

    public void drawMediaWordData(Map<String, Map<String, Long>> cloudWordMap, MediaTaskHandle handle) {
        List<MediaWord> mediaWordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(handle.getId()));
        Map<Integer, MediaWord> map = mediaWordList.stream().collect(Collectors.toMap(MediaWord::getSourceCode, m -> m));
        List<String> sourceNames = Arrays.stream(handle.getSources().split(",")).map(k -> ExtraConditionEnum.codeOf(Integer.valueOf(k))).collect(Collectors.toList());

        sourceNames.forEach(source -> {
            Map<String, Long> cloudWord = cloudWordMap.get(source);
            if (CollectionUtil.isEmpty(cloudWord)) {
                log.warn("爬虫数说聚合接口缺失cloudWord数据,跳过,平台={},handle={}", source, handle.getId());
                return;
            }
            List<KeyValue<Long, Long>> keyValues = super.wordMap2KvLabel(cloudWord);
            Integer code = ExtraConditionEnum.keyOf(source);
            MediaWord mediaWord = map.get(ExtraConditionEnum.keyOf(source));
            if (null == mediaWord) {
                mediaWord = new MediaWord();
                mediaWord.setCreateTime(LocalDateTime.now());
            }
            mediaWord.setMediaObjectsId(handle.getMediaObjectsId());
            mediaWord.setHandleId(handle.getId());
            mediaWord.setSourceCode(code);
            mediaWord.setAllWord(JSON.toJSONString(keyValues));
            mediaWord.setIsInit(FinishStatusEnum.ANALY.getCode());
            mediaWordDomainService.saveOrUpdate(mediaWord);
        });
    }

    public void handleDefault(MediaTaskHandle handle, Integer sourceCode) {
        MediaWord mediaWord = mediaWordDomainService.findByHandleIdAndSource(handle.getId(), sourceCode);

        if (ObjectUtil.isEmpty(mediaWord)) {
            mediaWord = new MediaWord();
            mediaWord.setCreateTime(LocalDateTime.now());
        }
        mediaWord.setMediaObjectsId(handle.getMediaObjectsId());
        mediaWord.setHandleId(handle.getId());
        mediaWord.setSourceCode(sourceCode);
        mediaWord.setAllWord("[]");
        mediaWord.setIsInit(FinishStatusEnum.ANALY.getCode());
        mediaWordDomainService.saveOrUpdate(mediaWord);
    }
}
