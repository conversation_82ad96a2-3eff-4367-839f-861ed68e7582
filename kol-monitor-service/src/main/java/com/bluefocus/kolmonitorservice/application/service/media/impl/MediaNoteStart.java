package com.bluefocus.kolmonitorservice.application.service.media.impl;

import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.domain.datastory.DataStoryDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTextResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaNoteDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

/**
 * @author: yjLiu
 * @date: 2024/5/22 20:18
 * @description: 笔记
 */
@Slf4j
@Order(6)
@Service
@RequiredArgsConstructor
public class MediaNoteStart extends IMediaStart {

    private final DataStoryDomainService dataStoryService;
    private final MediaNoteDomainService mediaNoteDomainService;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {
        DataHandleState dataHandleState = new DataHandleState();

        DataStoryEntity req = getDataStoryFixedParam(handle);
        int count = mediaNoteDomainService.findCountByHandle(handle.getMediaObjectsId(), handle.getId(), sourceCode);
        if (count > 0) {
            log.info("api当前对象笔记已经存在:{}, 数据源={}, 跳过", handle.getMediaObjectsId(), sourceCode);
            dataHandleState.setYuanwen(true);
            return dataHandleState;
        }
        req.setSource(ExtraConditionEnum.codeOf(sourceCode));

        DataStoryTextResponse.ResultDTO textList = retryReq(req, 3, handle.getMediaObjectsId());
        if (null == textList) {
            log.error("api当前对象笔记接口异常:{}, 数据源={}, 跳过", handle.getMediaObjectsId(), sourceCode);
            dataHandleState.setYuanwen(false);
            MediaObjects obj = mediaObjectsDomainService.getById(handle.getId());
            objFail(handle, obj, BusinessException.KEY_OTHER, null);
            return dataHandleState;
        }

        mediaNoteDomainService.saveNotes(textList, handle.getMediaObjectsId(), handle.getId(), sourceCode);
        log.info("MediaNoteDay插入基本笔记成功,当前对象[{}]平台[{}]，笔记数量{}", handle.getId(), sourceCode, textList.getTotal().size());

        dataHandleState.setYuanwen(true);
        return dataHandleState;
    }

    @SuppressWarnings("UN")
    private DataStoryTextResponse.ResultDTO retryReq(DataStoryEntity req, int i, Long id) {
        DataStoryTextResponse text = null;
        try {
            text = dataStoryService.getText(req);
            if (text.isSuccess()) {
                return text.getData();
            } else if (i > 0) {
                return retryReq(req, --i, id);
            }

        } catch (ApiException e) {
            log.error("请求数说原帖接口报错，第{}次", 3 - i);
            return retryReq(req, i, id);
        } finally {
            if ((null == text || !text.isSuccess()) && i == 0) {
                log.error("请求数说原帖接口重试次数耗尽：{}", text);
                String con = text != null ? text.getMsg() : req.getSource() + ":" + req.getKeyword();
                handleMsg(null, String.format("请求数说原帖接口异常,对象id=%s，内容：%s", id, con));
            }
        }
        return null;
    }


}
