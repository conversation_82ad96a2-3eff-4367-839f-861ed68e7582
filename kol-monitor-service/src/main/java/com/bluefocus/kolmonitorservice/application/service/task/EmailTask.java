package com.bluefocus.kolmonitorservice.application.service.task;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.bluefocus.kolmonitorinterface.dto.excel.ExcelEmailData;
import com.bluefocus.kolmonitorservice.base.common.EmailCommon;
import com.bluefocus.kolmonitorservice.base.common.EmailConstant;
import com.bluefocus.kolmonitorservice.base.common.OSSUploadCommon;
import com.bluefocus.kolmonitorservice.base.enums.MimeEnum;
import com.bluefocus.kolmonitorservice.base.util.EmailUtils;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDomainService;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RSet;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/31 09:06
 * @Description:
 */
@EnableScheduling
@Configuration
@Log4j2
@RequiredArgsConstructor
public class EmailTask {

    @Value("${spring.profiles.active}")
    private String env;
    private static final String EMAIL_TASK_LOCK = "email_task_lock";
    private static final String EMAIL_TASK_FLAG = "email_task_flag";
    private static final String EMAIL_RECIPIENTS = "kol_monitor_service_email_recipients";
    private final OSSUploadCommon ossUploadCommon;
    private final FsRobotUtil fsRobotUtil;
    @Value("${robot.kol}")
    private String robotUrl;

    private final ArticleDomainService articleDomainService;
    private final ProjectDomainService projectDomainService;
    private final EmailUtils emailUtils;
    private final RedisUtils redisUtils;

    @Scheduled(cron = " 0 0 8 * * ?")
    public void emailTask() {
        RLock lock = redisUtils.getLock(EMAIL_TASK_LOCK);
        try {
            lock.lock(60, TimeUnit.SECONDS);
            RBucket<Object> flag = redisUtils.getString(EMAIL_TASK_FLAG);
            if (flag.isExists()) {
                return;
            }
            List<Project> unFinish = projectDomainService.findUnFinish();
            List<Article> errList = articleDomainService.findAllErrDataByProjectId(unFinish.stream().map(Project::getId).collect(Collectors.toSet()));
            List<ExcelEmailData> errArticleDataList = new ArrayList<>();

            for (Project finish : unFinish) {
                // 找到当日应有但是没有的笔记数据
                List<Article> articleList = articleDomainService.findByProjectTime(finish.getId(), LocalDate.now().minusDays(1));
                if (CollectionUtil.isEmpty(articleList)) {
                    for (Article article : articleList) {
                        ExcelEmailData excelEmailData = new ExcelEmailData();
                        excelEmailData.setArticleId(article.getId());
                        excelEmailData.setProjectId(article.getProjectId());
                        excelEmailData.setStatus(article.getStatus());
                        excelEmailData.setStatusDesc("最近一天应有数据不存在");
                        excelEmailData.setArticleUrl(article.getArticleUrl());
                        excelEmailData.setLongUrl(article.getArticleFormalUrl());
                        errArticleDataList.add(excelEmailData);
                    }
                }
            }

            for (Article article : errList) {
                ExcelEmailData excelEmailData = new ExcelEmailData();
                excelEmailData.setArticleId(article.getId());
                excelEmailData.setProjectId(article.getProjectId());
                excelEmailData.setStatus(article.getStatus());
                excelEmailData.setStatusDesc(article.getCrawlerStateDesc());
                excelEmailData.setArticleUrl(article.getArticleUrl());
                excelEmailData.setLongUrl(article.getArticleFormalUrl());
                errArticleDataList.add(excelEmailData);
            }
            if (CollectionUtil.isEmpty(errArticleDataList)) {
                log.info("新媒体监测未发现异常数据");
                return;
            }
            String[] recipients = EmailConstant.emails;
            RSet<String> recipientsKey = redisUtils.getSet(EMAIL_RECIPIENTS);
            if (recipientsKey.isExists()) {
                recipients = recipientsKey.toArray(new String[0]);
            }
            sendHtmlEmail(errArticleDataList, recipients);
            sendExcelUrlFsRobot(errArticleDataList, EmailConstant.content);
            flag.set("SEND_SUCCESS", 5, TimeUnit.MINUTES);
        } finally {
            lock.unlock();
        }

    }

    private void sendExcelUrlFsRobot(List<ExcelEmailData> errArticleDataList, String content) {
        ByteArrayOutputStream response = generateExcel(errArticleDataList, ExcelEmailData.class);
        ByteArrayInputStream inputStream = new ByteArrayInputStream(response.toByteArray());
        String fileName = EmailConstant.ERR_EXCEL_NAME + LocalDate.now() + "." + MimeEnum.XLSX.getExt();
        String url = ossUploadCommon.uploadImg2Oss(inputStream, "kol-monitor" + fileName, fileName);
        fsRobotUtil.sendKolRobotMsg(robotUrl, content + "\n地址：" + url);
    }

    public static <T> ByteArrayOutputStream generateExcel(List<T> dataList, Class<T> clazz) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        EasyExcel.write(out, clazz).sheet().doWrite(dataList);
        return out;
    }

    private void sendHtmlEmail(List<ExcelEmailData> errArticleDataList, String[] recipients) {
        EmailCommon emailCommon = new EmailCommon();
        emailCommon.setSubject(EmailConstant.subject + "-" + env);
        emailCommon.setReceiveMailbox(recipients);
        emailCommon.setContent(createTableHtml(errArticleDataList, EmailConstant.content));
        log.info(emailCommon.getContent());
        emailUtils.sendHtmlEmail(emailCommon);
    }

    private String createTableHtml(List<ExcelEmailData> dataList, String content) {
        StringBuilder builder = new StringBuilder();
        // Table Header
        builder.append("<h3>").append(content).append("：</span>")
                .append("<table border='1'>")
                .append("<tr>")
                .append("<th>项目id</th>")
                .append("<th>文章id</th>")
                .append("<th>状态</th>")
                .append("<th>状态描述</th>")
                .append("<th>用户输入链接</th>")
                .append("<th>正式链接</th>")
                .append("</tr>");

        // Table Data
        for (ExcelEmailData data : dataList) {
            builder.append("<tr>")
                    .append("<td>").append(data.getProjectId()).append("</td>")
                    .append("<td>").append(data.getArticleId()).append("</td>")
                    .append("<td>").append(data.getStatus()).append("</td>")
                    .append("<td>").append(data.getStatusDesc()).append("</td>")
                    .append("<td>").append(data.getArticleUrl()).append("</td>")
                    .append("<td>").append(data.getLongUrl()).append("</td>")
                    .append("</tr>");
        }

        builder.append("</table>");

        return builder.toString();
    }

}
