package com.bluefocus.kolmonitorservice.application.controller;

import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.paramvalid.ParamValidHandler;
import com.bluefocus.kolmonitorinterface.IReportService;
import com.bluefocus.kolmonitorinterface.dto.res.ReportResponse;
import com.bluefocus.kolmonitorservice.application.service.ReportService;
import com.bluefocus.kolmonitorservice.application.valid.ReportValidHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/8 16:11
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/lark/reports")
@ParamValidHandler(ReportValidHandler.class)
public class ReportController implements IReportService {

    private final ReportService reportService;

    @Override
    @GetMapping("/{projectId}/info")
    public ResponseBean<ReportResponse> findProjectReport(@PathVariable Long projectId) {
        return reportService.findProjectReport(projectId);
    }

    @Override
    @GetMapping("/excel/export")
    public void exportReport(@RequestParam("projectId") Long projectId) {
        reportService.exportReport(projectId);
    }

    @Override
    @GetMapping("/{projectId}/ppt/create")
    public void exportReportForPPT(@PathVariable Long projectId) {
        reportService.exportReportForPpt(projectId);
    }
}
