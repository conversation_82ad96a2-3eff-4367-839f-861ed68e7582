package com.bluefocus.kolmonitorservice.application.service.media;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.basebean.exception.InvalidParameterException;
import com.bluefocus.basebean.exception.ResponseException;
import com.bluefocus.common.Assert;
import com.bluefocus.common.SpringContext;
import com.bluefocus.kolmonitorinterface.IMediaService;
import com.bluefocus.kolmonitorinterface.dto.excel.ExcelMediaNote;
import com.bluefocus.kolmonitorinterface.dto.media.ImgValue;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorinterface.dto.media.req.*;
import com.bluefocus.kolmonitorinterface.dto.media.resp.*;
import com.bluefocus.kolmonitorservice.application.service.media.impl.CrawlerMediaService;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAlgorithmStart;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaWordArtStart;
import com.bluefocus.kolmonitorservice.application.service.media.impl.StrategyFactory;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.*;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleCrawlerListener;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import com.bluefocus.kolmonitorservice.domain.chat.coze.CozeDomainService;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.ConversationResp;
import com.bluefocus.kolmonitorservice.domain.chat.service.MediaConversationDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.*;
import com.bluefocus.kolmonitorservice.domain.media.service.*;
import com.bluefocus.kolmonitorservice.domain.wordart.entity.WordArtWordEntity;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordArtGenerateRequest;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordCloudGenRequest;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordArtGenerateResponse;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0021 2024/5/21 20:25
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaService implements IMediaService {

    private final MediaTaskDomainService mediaTaskDomainService;
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final GptPictureDomainService gptPictureDomainService;
    private final MediaWordDomainService mediaWordDomainService;
    private final MediaTrendDomainService mediaTrendDomainService;
    private final MediaNoteDomainService mediaNoteDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final UserCommon userCommon;
    private final StrategyFactory strategyFactory;
    private final CrawlerMediaService crawlerMediaService;
    private final MediaAlgorithmStart mediaAlgorithmStart;
    private final MediaWordArtStart wordArtStart;
    private final ThreadPoolExecutor wordArtThreadPoolExecutor;
    private final MediaConversationDomainService mediaConversationDomainService;
    private final CozeDomainService cozeDomainService;
    private final FsRobotUtil fsRobotUtil;

    @Value("${media.gpt.img-retry}")
    private int gptImgRetry;
    @Value("${robot.url}")
    private String robotUrl;

    @Override
    public ResponseBean<List<SourceResp>> sourceList() {
        return ResponseBean.create(Arrays.stream(ExtraConditionEnum.values()).filter(e -> e.getCode() != 0).map(e -> {
            SourceResp sourceResp = new SourceResp();
            sourceResp.setCode(e.getCode());
            sourceResp.setName(e.getName());
            return sourceResp;
        }).collect(Collectors.toList()));
    }

    @Override
    public BaseResponseBean save(MediaTaskSaveReq request) {

        List<MediaTask> mediaTaskByName = mediaTaskDomainService.findMediaTaskByName(request.getName(), userCommon.getUserId(true));

        Assert.isFalse(mediaTaskByName.size() > 0, "任务名称重复");

        MediaService bean = SpringContext.getApplicationContext().getBean(MediaService.class);
        List<MediaTaskHandle> mediaTaskHandleList = bean.handleBatchSave(request);

        strategyFactory.handleMqTactics(mediaTaskHandleList);

        strategyFactory.noticeFs(mediaTaskHandleList);

        return BaseResponseBean.SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<MediaTaskHandle> handleBatchSave(MediaTaskSaveReq request) {
        Long userId = userCommon.getUserId(true);
        Long taskId = mediaTaskDomainService.saveOne(request.getName(), userId);
        mediaObjectsDomainService.saveObjBatch(request.getObjReqList(), taskId
                , gptPictureDomainService.defaultWordImg());
        List<MediaObjects> mediaObjectsList = mediaObjectsDomainService.findByTaskId(taskId);

        List<MediaTaskHandle> mediaTaskHandleList = mediaTaskHandleDomainService.initialHandleBatch(mediaObjectsList, HandleTypeEnum.CREAT);

        mediaTrendDomainService.saveAll(mediaTaskHandleList);

        for (MediaObjects mediaObjects : mediaObjectsList) {
            handleMediaConversation(mediaObjects, userId);
        }

        return mediaTaskHandleList;
    }

    @Override
    public ResponseBean<MediaTaskPageBean<MediaTaskResp>> findMediaTaskPage(String taskName, Long startTime, Long endTime, Integer page, Integer limit) {
        IPage<MediaTask> mediaTaskPage = mediaTaskDomainService.findMediaTaskPage(taskName, startTime, endTime, userCommon.getUserId(true), page, limit);
        List<MediaTask> mediaTaskByName = mediaTaskDomainService.findMediaTaskByName(null, userCommon.getUserId(true));

        MediaTaskPageBean<MediaTaskResp> pageBean = new MediaTaskPageBean<>();
        pageBean.setAllFinish(mediaTaskByName.stream().noneMatch(mediaTask -> mediaTask.getStatus() == 0));
        pageBean.setPage(page);
        pageBean.setTotalCount((int) mediaTaskPage.getTotal());
        pageBean.setLimit(limit);
        pageBean.setItems(mediaTaskPage.getRecords().stream()
                .map(po -> {
                    MediaTaskResp mediaTaskResp = new MediaTaskResp();
                    mediaTaskResp.setCreateTime(Times.toEpochMilli(po.getCreateTime()));
                    mediaTaskResp.setName(po.getName());
                    mediaTaskResp.setStatus(po.getStatus());
                    mediaTaskResp.setId(po.getId());
                    return mediaTaskResp;
                }).collect(Collectors.toList()));
        return ResponseBean.create(pageBean);
    }

    @Override
    public ResponseBean<MediaTaskDetailResp> findTaskDetail(Long id) {
        MediaTask task = mediaTaskDomainService.getById(id);
        Assert.notNull(task, "任务不存在");

        MediaTaskDetailResp mediaTaskDetailResp = new MediaTaskDetailResp();
        mediaTaskDetailResp.setId(id);
        mediaTaskDetailResp.setName(task.getName());
        Map<Long, MediaObjects> objMap = mediaObjectsDomainService.findByTaskId(id).stream().collect(Collectors.toMap(MediaObjects::getId, mediaObjects -> mediaObjects));
        List<MediaTaskHandle> handleList = mediaTaskHandleDomainService.getRunningTaskHandleByTask(id, null);

        mediaTaskDetailResp.setObjList(handleList.stream().sorted(Comparator.comparing(MediaTaskHandle::getMediaObjectsId))
                .map(handle -> {
                    MediaObjectsResp resp = new MediaObjectsResp();
                    if (handle.getStatus().equals(FinishStatusEnum.FAIL.getCode())) {
                        resp.setCode(FinishStatusEnum.FAIL.getCode());
                        resp.setFailDesc(failDesc(handle));
                    }
                    MediaObjects objPo = objMap.get(handle.getMediaObjectsId());
                    resp.setMediaObjectsId(handle.getMediaObjectsId());
                    resp.setName(objPo.getName());
                    resp.setStartTime(handle.getStartTime());
                    resp.setEndTime(handle.getEndTime());
                    if (null != objPo.getAndKeywords()) {
                        List<List<String>> ks = JSONArray.parseObject(objPo.getAndKeywords(), List.class);
                        resp.setAndKeywords(ks);
                    }
                    resp.setOrKeywords(JSONArray.parseArray(objPo.getOrKeywords(), String.class));
                    resp.setFilterword(JSONArray.parseArray(handle.getFilterword(), String.class));
                    resp.setSourceCodes(handle.getSources());
                    resp.setSourceNameList(Arrays.stream(handle.getSources().split(","))
                            .map(s -> ExtraConditionEnum.nameOf(Integer.valueOf(s))).collect(Collectors.toList()));
                    return resp;
                })
                .collect(Collectors.toList()));

        return ResponseBean.create(mediaTaskDetailResp);
    }

    private String failDesc(MediaTaskHandle handle) {
        String content = BusinessException.KEY_OTHER;
        String errMsg = handle.getApiErrMsg() == null ? handle.getErrMsg() : handle.getApiErrMsg();
        if (errMsg == null) {
            content = BusinessException.KEY_OTHER;
        } else {
            if (errMsg.contains(BusinessException.KEY_SENSITIVE)) {
                int i = errMsg.indexOf("关键词");
                content = errMsg.substring(i);
            }
            if (errMsg.equals(BusinessException.KEY_TIME_OUT)) {
                content = BusinessException.KEY_TIME_OUT;
            }
        }

        content = content.replace(",", "，");
        content = content.replace(".", "。");
        content = content.replace("[", "(");
        content = content.replace("]", ")");
        return content;
    }

    @Override
    public ResponseBean<MediaObjectsResp> findObjDetail(Long mediaObjectsId) {
        MediaObjects mediaObjects = mediaObjectsDomainService.getById(mediaObjectsId);
        MediaTaskHandle mediaTaskHandle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(mediaObjects.getMediaTaskId(), mediaObjectsId);
        boolean mediaFail = MediaEditStatusEnum.failCheck(mediaObjects.getStatus());

        // 弥补历史数据
        boolean handleFail = false;
        if (null == mediaTaskHandle) {
            if (mediaFail) {
                // 未采集
                mediaTaskHandle = strategyFactory.asyncTaskHandle(mediaObjectsId, mediaObjects.getMediaTaskId(), HandleTypeEnum.CREAT.getType(), FinishStatusEnum.CRAWLER);
                handleFail = true;
            } else {
                mediaTaskHandle = strategyFactory.asyncTaskHandle(mediaObjectsId, mediaObjects.getMediaTaskId(), HandleTypeEnum.CREAT.getType(), FinishStatusEnum.ANALY);
            }
        } else {
            handleFail = mediaTaskHandle.getStatus().equals(FinishStatusEnum.CRAWLER.getCode());
        }

        // 失败可重新编辑
        if (mediaTaskHandle.getStatus().equals(FinishStatusEnum.FAIL.getCode())) {
            MediaObjectsResp objectsResp = baseData(mediaObjects);
            objectsResp.setFinish(1);
            objectsResp.setCode(FinishStatusEnum.FAIL.getCode());
            objectsResp.setFailDesc(failDesc(mediaTaskHandle));
            return ResponseBean.create(objectsResp);
        }

        // 处理失败 编辑中
        if (handleFail || mediaFail) {
            MediaObjectsResp objectsResp = new MediaObjectsResp();
            objectsResp.setFinish(0);
            if (handleFail) {
                objectsResp.setCode(1);
            } else {
                objectsResp.setCode(FinishStatusEnum.FAIL.getCode());
                objectsResp.setFailDesc(failDesc(mediaTaskHandle));
            }
            try {
                Thread.sleep(5000L);
            } catch (InterruptedException ignored) {
            }
            return ResponseBean.create(objectsResp);
        }
        Map<String, GptPicture> imgListMap = gptPictureDomainService.findByObjIds(Collections.singletonList(mediaObjectsId))
                .stream().collect(Collectors.toMap(GptPicture::getImg, Function.identity(), (img, img2) -> img2));

        List<MediaWord> mediaWordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(mediaTaskHandle.getId()));
        List<MediaTrend> mediaTrendList = mediaTrendDomainService.findByHandleIds(Collections.singletonList(mediaTaskHandle.getId()));

        MediaObjectsResp objectsResp = buildBaseMediaData(mediaObjects, getTaskWordCloudImg(mediaWordList, imgListMap), getTaskTrend(mediaTrendList));
        objectsResp.setFinish(mediaTaskHandle.getStatus().equals(FinishStatusEnum.FINISH.getCode()) ? 1 : 0);
        return ResponseBean.create(objectsResp);
    }

    private MediaObjectsResp baseData(MediaObjects mediaObjects) {
        MediaObjectsResp objectsResp = new MediaObjectsResp();
        objectsResp.setMediaObjectsId(mediaObjects.getId());
        objectsResp.setName(mediaObjects.getName());
        objectsResp.setAllInteraction(mediaObjects.getTotalInteraction());
        objectsResp.setAllSentiments(mediaObjects.getTotalSentiments());
        objectsResp.setAllVolume(mediaObjects.getTotalVolume());
        objectsResp.setEndTime(mediaObjects.getEndTime());
        objectsResp.setStartTime(mediaObjects.getStartTime());
        objectsResp.setOrKeywords(JSONArray.parseArray(mediaObjects.getOrKeywords(), String.class));
        if (null != mediaObjects.getAndKeywords()) {
            List<List<String>> ks = JSONArray.parseObject(mediaObjects.getAndKeywords(), List.class);
            objectsResp.setAndKeywords(ks);
        }
        if (null != mediaObjects.getFilterword()) {
            objectsResp.setFilterword(JSONArray.parseArray(mediaObjects.getFilterword(), String.class));
        }
        objectsResp.setImgUrl(mediaObjects.getImg());
        objectsResp.setSourceCodes(mediaObjects.getSources());
        ArrayList<String> list = new ArrayList<>();
        for (String s : mediaObjects.getSources().split(",")) {
            list.add(ExtraConditionEnum.nameOf(Integer.valueOf(s)));
        }
        objectsResp.setSourceNameList(list);
        return objectsResp;
    }

    private MediaObjectsResp buildBaseMediaData(MediaObjects obj, List<WordCloudImg> taskWordCloudImg, List<TaskTrend> taskTrend) {
        MediaObjectsResp objectsResp = baseData(obj);
        objectsResp.setVolumeList(kvJsonConvert(obj.getRateVolume()));
        objectsResp.setInteractionList(kvJsonConvert(obj.getRateInteraction()));
        if (null != obj.getRateSentiments()) {
            List<KeyValue> keyValues = new Gson().fromJson(obj.getRateSentiments(), new TypeToken<List<KeyValue>>() {
            }.getType());
            keyValues.sort(Comparator.comparing(KeyValue::getKey));
            ArrayList<KeyValue> sentimentsList = new ArrayList<>();
            for (int i = keyValues.size() - 1; i >= 0; i--) {
                sentimentsList.add(keyValues.get(i));
            }
            objectsResp.setSentimentsList(sentimentsList);
        }
        objectsResp.setWordCloud(taskWordCloudImg.stream().sorted(Comparator.comparing(WordCloudImg::getSourceCode)).collect(Collectors.toList()));
        taskTrend.sort(Comparator.comparing(TaskTrend::getSourceCode));
        objectsResp.setTrendData(taskTrend);
        return objectsResp;
    }

    private List<AnalyzeValue> getHotConclusions(MediaTrend mediaTrend) {
        if (null == mediaTrend || StringUtils.isEmpty(mediaTrend.getHotConclusions())
                || "[]".equals(mediaTrend.getHotConclusions())
                || "[null,null,null]".equals(mediaTrend.getHotConclusions())) {
            return null;
        }
        List<AnalyzeValue> analyzeValues = JSON.parseArray(mediaTrend.getHotConclusions(), AnalyzeValue.class);
        return analyzeValues.stream()
                .filter(analyzeValue -> ObjectUtil.isNotEmpty(analyzeValue) && StringUtils.isNotEmpty(analyzeValue.getDate()))
                .sorted(Comparator.comparing(AnalyzeValue::getDate))
                .collect(Collectors.toList());
    }

    private List<KeyValue> kvJsonConvert(String kv) {
        if (null == kv) {
            return new ArrayList<>();
        }
        List<KeyValue> list = new Gson().fromJson(kv, new TypeToken<List<KeyValue>>() {
        }.getType());
        return list.stream().peek(keyValue -> keyValue.setKey(ExtraConditionEnum.nameOf(Integer.valueOf(keyValue.getKey())))).collect(Collectors.toList());
    }

    private List<TaskSourceTrend> convertDataList(String volumeTrend, String interactionTrend) {
        ArrayList<TaskSourceTrend> taskSourceTrends = new ArrayList<>();
        taskSourceTrends.add(convertData(volumeTrend, "声量"));
        taskSourceTrends.add(convertData(interactionTrend, "互动量"));
        return taskSourceTrends;
    }

    private TaskSourceTrend convertData(String trend, String name) {
        TaskSourceTrend sourceTrend = new TaskSourceTrend();
        sourceTrend.setIndexName(name);
        List<KeyValue<String, Long>> kvList = new Gson().fromJson(trend, new TypeToken<List<KeyValue<String, Long>>>() {
        }.getType());
        kvList.sort(Comparator.comparing(KeyValue::getDate));
        sourceTrend.setData(kvList);
        return sourceTrend;
    }

    private List<WordCloudImg> getTaskWordCloudImg(List<MediaWord> mediaWords, Map<String, GptPicture> imgListMap) {

        if (CollectionUtil.isEmpty(mediaWords)) {
            return new ArrayList<>();
        }
        return mediaWords.stream().map(word -> {
            WordCloudImg wordCloudImg = new WordCloudImg();
            wordCloudImg.setCode(word.getArtStatus());
            wordCloudImg.setImgUrl(word.getGenImg());
            wordCloudImg.setSourceCode(word.getSourceCode());
            wordCloudImg.setSourceName(ExtraConditionEnum.nameOf(word.getSourceCode()));
            wordCloudImg.setWords(sortWords(word.getAllWord()));
            GptPicture gptPicture = imgListMap.get(word.getGenImg());
            if (null != gptPicture) {
                wordCloudImg.setHeight(gptPicture.getHeight());
                wordCloudImg.setWidth(gptPicture.getWidth());
            }

            return wordCloudImg;
        }).collect(Collectors.toList());
    }

    private List<KeyValue> sortWords(String allWord) {
        List<KeyValue> wordCloudList = new Gson().fromJson(allWord, new TypeToken<List<KeyValue<String, Long>>>() {
        }.getType());
        wordCloudList.sort(Comparator.comparing(w1 -> {
            KeyValue w = (KeyValue) w1;
            return Long.parseLong(w.getValue().toString());
        }).reversed());
        return wordCloudList;
    }

    @Override
    public ResponseBean<PageBean<MediaNoteResp>> findMediaNotePage(Long mediaObjectsId, String sort, Integer sortType, Integer sourceCode, Integer page, Integer limit) {

        Optional.ofNullable(SortEnum.getFieldByName(sort)).orElseThrow(() -> new ResponseException("排序字段错误"));
        MediaTaskHandle handle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(null, mediaObjectsId);
        Page<MediaNote> servicePage = mediaNoteDomainService.findPage(mediaObjectsId, handle.getId(), sort, sortType, sourceCode, page, limit);
        List<MediaNoteResp> mediaNoteResps = new ArrayList<>();
        servicePage.getRecords().forEach(po -> {
            MediaNoteResp mediaNoteResp = new MediaNoteResp();
            mediaNoteResp.setAuthor(po.getAuthor());
            if (po.getTitle() == null) {
                mediaNoteResp.setTitle(po.getContent());
            } else {
                mediaNoteResp.setTitle(po.getTitle());
            }

            mediaNoteResp.setHeadImg(po.getHeadImg());
            if (po.getCoverOcrContent() != null) {
                mediaNoteResp.setHitTag("图片识别内容");
            } else if (po.getAudioOcrContent() != null) {
                mediaNoteResp.setHitTag("音频识别内容");
            } else if (po.getHighlightOcrContent() != null) {
                mediaNoteResp.setHitTag("花字识别内容");
            } else if (po.getVideoContent() != null) {
                mediaNoteResp.setHitTag("文字识别内容");
            }
            mediaNoteResp.setSourceName(ExtraConditionEnum.nameOf(po.getSourceCode()));
            mediaNoteResp.setCoverOcrContent(po.getCoverOcrContent());
            mediaNoteResp.setAudioOcrContent(po.getAudioOcrContent());
            mediaNoteResp.setHighlightOcrContent(po.getHighlightOcrContent());
            mediaNoteResp.setVideoContent(po.getVideoContent());
            mediaNoteResp.setInteractionCnt(po.getInteractionCnt());
            mediaNoteResp.setCollectionCnt(po.getCollectionCnt());
            mediaNoteResp.setLikeCnt(po.getLikeCnt());
            mediaNoteResp.setRepostsCnt(po.getRepostsCnt());
            mediaNoteResp.setReviewCnt(po.getReviewCnt());
            mediaNoteResp.setNoteUrl(po.getNoteUrl());
            mediaNoteResp.setContent(po.getContent());
            mediaNoteResp.setNoteType(po.getIsOriginal());

            mediaNoteResp.setPublishTime(po.getPublishTime());
            mediaNoteResp.setKeywords(po.getSearchKeyword());
            mediaNoteResps.add(mediaNoteResp);
        });
        PageBean<MediaNoteResp> mediaNoteRespPageBean = new PageBean<>();
        mediaNoteRespPageBean.setPage(page);
        mediaNoteRespPageBean.setLimit(limit);
        mediaNoteRespPageBean.setTotalCount((int) servicePage.getTotal());
        mediaNoteRespPageBean.setItems(mediaNoteResps);

        return ResponseBean.create(mediaNoteRespPageBean);
    }

    @Override
    public ResponseBean<String> uploadPicture(MultipartFile file, Long mediaObjectsId) {

        MediaObjects obj = mediaObjectsDomainService.getById(mediaObjectsId);
        Assert.notNull(obj, "分析对象不存在");

        String fileName = file.getOriginalFilename();
        if (null == fileName || !fileName.contains(StrUtil.DOT)) {
            try {
                fileName = DigestUtils.md5Hex(file.getInputStream()) + StrUtil.DOT + MimeEnum.PNG.getExt();
            } catch (IOException ioException) {
                throw new InvalidParameterException("上传文件异常");
            }
        } else {
            fileName = UUID.randomUUID() + StrUtil.DOT + MimeEnum.JPG.getExt();
        }

        try {
            String url = gptPictureDomainService.uploadAndSave(file, fileName, mediaObjectsId);
            return ResponseBean.create(url);
        } catch (Exception e) {
            throw new InvalidParameterException("图片受损严重，请更换图片!");
        }
    }

    @Override
    public ResponseBean<List<String>> genPicture(String content, Long mediaObjectsId) {
        String prompt = gptPictureDomainService.getPromptConstant();
        int i = gptImgRetry;
        List<String> promptList = new ArrayList<>();
        while (i > 0) {
            i--;
            promptList.add(prompt + content);
        }
        List<String> fileUrlList = gptPictureDomainService.genGptPictureList(promptList, mediaObjectsId).stream()
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());

        fileUrlList.removeIf(StringUtils::isEmpty);
        if (CollectionUtil.isEmpty(fileUrlList)) {
            throw new BusinessException("当前请求人数过多，请稍后再试");
        }
        return ResponseBean.create(fileUrlList);
    }

    @Override
    public ResponseBean<WordCloudResp> genWordCloud(GenWordCloudReq req) {
        WordCloudResp wordCloudResp = new WordCloudResp();

        MediaTaskHandle handle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(null, req.getMediaObjectsId());
        List<MediaWord> mediaWordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(handle.getId()));

        if (CollectionUtil.isEmpty(mediaWordList)) {
            wordCloudResp.setCode(WordArtEnum.ONE.getCode());
            return ResponseBean.create(wordCloudResp);
        }
        CopyOnWriteArrayList<WordCloudImg> respList = new CopyOnWriteArrayList<>();

        String defaultWordImg = null;
        String imgBase64 = null;
        if (req.getImgUrl().equals(gptPictureDomainService.defaultWordImg())) {
            defaultWordImg = req.getImgUrl();
        } else {
            try {
                // 校验图片
                imgBase64 = gptPictureDomainService.defaultImgBase64(req.getImgUrl());
                if (ObjectUtil.isEmpty(imgBase64)) {
                    throw new InvalidParameterException(WordArtEnum.TWO.getDesc());
                }
            } catch (Exception e) {
                log.error("用户批量生成词云图，原始图片转换异常，img={},e={}", req.getImgUrl(), e);
                for (MediaWord mediaWord : mediaWordList) {
                    respList.add(buildWordCloudImg(mediaWord, WordArtEnum.ONE.getCode()));
                }
                wordCloudResp.setCode(WordArtEnum.ONE.getCode());
                wordCloudResp.setWordCloudImgList(respList);
                return ResponseBean.create(wordCloudResp);
            }
        }

        for (MediaWord mediaWord : mediaWordList) {
            if (CollectionUtil.isEmpty(JSON.parseArray(mediaWord.getAllWord(), KeyValue.class))) {
                MediaTrend trend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), mediaWord.getSourceCode());
                if (trend.getAllVolume() != null && trend.getAllVolume() == 0L) {
                    mediaWord.setArtStatus(WordArtEnum.ONE.getCode());
                    respList.add(buildWordCloudImg(mediaWord, WordArtEnum.ONE.getCode()));
                }
            }
        }

        List<ImgValue> imgValues = batchGenWordImg(mediaWordList, respList, defaultWordImg, imgBase64, handle, req.getImgUrl());
        MediaService bean = SpringContext.getApplicationContext().getBean(MediaService.class);
        bean.savePictureAndWord(mediaWordList, imgValues, req.getMediaObjectsId(), req.getImgUrl());
        if (CollectionUtil.isNotEmpty(respList)) {
            wordCloudResp.setCode(WordArtEnum.ZERO.getCode());
            wordCloudResp.setWordCloudImgList(respList.stream().sorted(Comparator.comparing(WordCloudImg::getSourceCode)).collect(Collectors.toList()));
            return ResponseBean.create(wordCloudResp);
        }
        wordCloudResp.setCode(WordArtEnum.ONE.getCode());
        return ResponseBean.create(wordCloudResp);
    }

    private WordCloudImg buildWordCloudImg(MediaWord mediaWord, Integer code) {
        WordCloudImg resp = new WordCloudImg();
        resp.setSourceCode(mediaWord.getSourceCode());
        resp.setSourceName(ExtraConditionEnum.nameOf(mediaWord.getSourceCode()));
        resp.setImgUrl(mediaWord.getGenImg());
        resp.setSourceName(ExtraConditionEnum.nameOf(mediaWord.getSourceCode()));
        resp.setCode(code);
        return resp;
    }

    private List<ImgValue> batchGenWordImg(List<MediaWord> mediaWordList, CopyOnWriteArrayList<WordCloudImg> respList, String defaultWordImg, String imgBase64
            , MediaTaskHandle handle, String reqImgUrl) {
        List<CompletableFuture<ImgValue>> wordList = mediaWordList.stream()
                .filter(w -> respList.stream().noneMatch(resp -> resp.getSourceCode().equals(w.getSourceCode())))
                .map(mediaWord -> singleGenWordImg(mediaWord, respList, defaultWordImg, imgBase64, handle, reqImgUrl))
                .collect(Collectors.toList());

        CompletableFuture.allOf(wordList.toArray(new CompletableFuture[0]));
        List<ImgValue> imgValues = wordList.stream().map(img -> {
            try {
                return img.get();
            } catch (InterruptedException | ExecutionException ignored) {
            }
            return null;
        }).collect(Collectors.toList());
        imgValues.removeIf(imgValue -> null == imgValue || imgValue.getUrl() == null);
        return imgValues;
    }

    private CompletableFuture<ImgValue> singleGenWordImg(MediaWord mediaWord, CopyOnWriteArrayList<WordCloudImg> respList
            , String defaultWordImg, String imgBase64, MediaTaskHandle handle, String reqImgUrl) {
        return CompletableFuture.supplyAsync(() -> {
            WordCloudImg resp = new WordCloudImg();
            List<KeyValue> wordCloudList = sortWords(mediaWord.getAllWord());
            ImgValue imgUrl = null;
            long start = System.currentTimeMillis();
            try {
                // 路径分发
                if (null != defaultWordImg) {
                    WordCloudGenRequest request = buildWordCloudGenRequest(wordCloudList, defaultWordImg);
                    imgUrl = gptPictureDomainService.genWordCloudImg(request, mediaWord.getHandleId(), mediaWord.getMediaObjectsId());
                    if (null != imgUrl && imgUrl.getWidth() != null) {
                        mediaWord.setArtStatus(WordArtEnum.ZERO.getCode());
                    } else {
                        mediaWord.setArtStatus(WordArtEnum.FOUR.getCode());
                    }
                } else if (null != imgBase64) {
                    WordArtGenerateRequest request = buildWordArtGenerateRequest(wordCloudList, imgBase64);
                    WordArtGenerateResponse execute = gptPictureDomainService.genWordArtImgTime(request, mediaWord.getHandleId());
                    if (execute.isSuccess()) {
                        int count = 3;
                        while (imgUrl == null || count <= 0) {
                            imgUrl = gptPictureDomainService.upload2Oss(handle.getMediaObjectsId(), execute.getBody());
                            count--;
                        }
                        mediaWord.setArtStatus(WordArtEnum.ZERO.getCode());
                    } else if (execute.getBody().contains("The shape is empty!")) {
                        mediaWord.setArtStatus(WordArtEnum.THREE.getCode());
                    } else {
                        log.warn("用户生成词云异常,结果={}", JSON.toJSONString(execute));
                        mediaWord.setArtStatus(WordArtEnum.FOUR.getCode());
                    }
                } else {
                    throw new InvalidParameterException(WordArtEnum.TWO.getDesc());
                }
            } catch (Exception e) {
                log.error("用户生成词云异常,handleId={},e={}", mediaWord.getHandleId(), e);
                mediaWord.setArtStatus(WordArtEnum.FOUR.getCode());
                MediaTask task = mediaTaskDomainService.getById(handle.getTaskId());
                fsRobotUtil.sendRobotMsg(robotUrl, String.format("当前用户批量生成词云失败：当前任务id=%s,任务名称%s,当前处理id=%s,图片地址=%s"
                        , handle.getTaskId(), task.getName(), handle.getId(), reqImgUrl));
            }
            if (imgUrl != null) {
                mediaWord.setGenImg(imgUrl.getUrl());
                mediaWord.setArtStatus(WordArtEnum.ZERO.getCode());
                resp.setImgUrl(imgUrl.getUrl());
                resp.setHeight(imgUrl.getHeight());
                resp.setWidth(imgUrl.getWidth());
            }
            resp.setCode(mediaWord.getArtStatus());
            resp.setSourceCode(mediaWord.getSourceCode());
            resp.setSourceName(ExtraConditionEnum.nameOf(mediaWord.getSourceCode()));
            resp.setWords(wordCloudList);
            mediaWord.setGenTime(System.currentTimeMillis() - start);
            mediaWord.setUpdateTime(LocalDateTime.now());
            mediaWordDomainService.updateById(mediaWord);
            respList.add(resp);
            return imgUrl;
        }, wordArtThreadPoolExecutor);
    }

    private WordCloudGenRequest buildWordCloudGenRequest(List<KeyValue> wordCloudList, String finalImgUrl) {
        WordCloudGenRequest request = new WordCloudGenRequest();
        Map<String, Long> wordMap = wordCloudList.stream().filter(KeyValue::getEnable)
                .collect(Collectors.toMap(KeyValue::getLabel, keyValue -> Long.valueOf(keyValue.getValue().toString())));
        request.setData(wordMap);
        request.setUrl(finalImgUrl);
        return request;
    }

    private WordArtGenerateRequest buildWordArtGenerateRequest(List<KeyValue> wordCloudList, String finalImgBase6) {

        WordArtGenerateRequest request = new WordArtGenerateRequest();

        Map<String, Long> wordMap = wordCloudList.stream().filter(KeyValue::getEnable)
                .collect(Collectors.toMap(KeyValue::getLabel, keyValue -> Long.valueOf(keyValue.getValue().toString())));
        List<WordArtWordEntity> objects = new ArrayList<>();
        wordMap.forEach((key, value) -> objects.add(new WordArtWordEntity(key, value)));
        request.setWords(objects);
        request.setImageBase64(finalImgBase6);
        return request;
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePictureAndWord(List<MediaWord> words, List<ImgValue> imgValueList, Long mediaObjectsId, String imgUrl) {
        mediaWordDomainService.updateBatchById(words);
        ArrayList<GptPicture> gptPictures = new ArrayList<>();
        for (ImgValue imgValue : imgValueList) {
            GptPicture gptPicture = new GptPicture();
            gptPicture.setCreateTime(LocalDateTime.now());
            gptPicture.setImg(imgValue.getUrl());
            gptPicture.setImgSource(3);
            gptPicture.setMediaObjectsId(mediaObjectsId);
            gptPicture.setHeight(imgValue.getHeight());
            gptPicture.setWidth(imgValue.getWidth());
            gptPictures.add(gptPicture);
        }
        gptPictureDomainService.savePictureBatch(gptPictures);
        if (words.stream().anyMatch(w -> w.getArtStatus().equals(WordArtEnum.ZERO.getCode()))) {
            mediaObjectsDomainService.updateMediaObjImg(mediaObjectsId, imgUrl);
        }
    }

    @Override
    public ResponseBean<WordCloudImg> updateWordCloud(UpdateWordCloudReq req) {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(null, req.getMediaObjectsId());
        MediaWord mediaWord = mediaWordDomainService.findByHandleIdAndSource(handle.getId(), req.getSourceCode());
        MediaObjects obj = mediaObjectsDomainService.getById(handle.getMediaObjectsId());
        String defaultWordImg = null;
        String imgBase64 = null;
        WordCloudImg wordCloudImg = new WordCloudImg();
        if (obj.getImg().equals(gptPictureDomainService.defaultWordImg())) {
            defaultWordImg = obj.getImg();
        } else {
            try {
                // 校验图片
                imgBase64 = gptPictureDomainService.defaultImgBase64(obj.getImg());
                if (ObjectUtil.isEmpty(imgBase64)) {
                    wordCloudImg.setCode(WordArtEnum.TWO.getCode());
                    return ResponseBean.create(wordCloudImg);
                }
            } catch (Exception ignored) {
                wordCloudImg.setCode(WordArtEnum.TWO.getCode());
                return ResponseBean.create(wordCloudImg);
            }
        }

        mediaWord.setAllWord(JSON.toJSONString(req.getWords()));
        CompletableFuture<ImgValue> future = singleGenWordImg(mediaWord, new CopyOnWriteArrayList<>(), defaultWordImg, imgBase64, handle, obj.getImg());
        CompletableFuture.allOf(future);
        try {
            ImgValue imgValue = future.get();
            wordCloudImg.setCode(mediaWord.getArtStatus());
            wordCloudImg.setSourceCode(req.getSourceCode());
            wordCloudImg.setSourceName(ExtraConditionEnum.nameOf(req.getSourceCode()));
            if (ObjectUtil.isNotNull(imgValue)) {
                wordCloudImg.setImgUrl(imgValue.getUrl());
                wordCloudImg.setHeight(imgValue.getHeight());
                wordCloudImg.setWidth(imgValue.getWidth());
            }
            wordCloudImg.setWords(sortWords(mediaWord.getAllWord()));

        } catch (Exception e) {
            log.error("用户编辑词云图失败={}", e);
            wordCloudImg.setCode(WordArtEnum.FOUR.getCode());
        }
        return ResponseBean.create(wordCloudImg);

    }

    @Override
    public BaseResponseBean edit(MediaObjectsReq request) {

        MediaObjects mediaObjects = mediaObjectsDomainService.getById(request.getMediaObjectsId());

        MediaTaskHandle mediaTaskHandle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(mediaObjects.getMediaTaskId(), mediaObjects.getId());
        Assert.notNull(mediaObjects, "分析对象不存在");
        Assert.isTrue(mediaTaskHandle.getStatus().equals(FinishStatusEnum.FINISH.getCode())
                || mediaTaskHandle.getStatus().equals(FinishStatusEnum.FAIL.getCode()), "任务还未完成");

        MediaObjects newObj = new MediaObjects();
        newObj.setId(mediaObjects.getId());
        newObj.setMediaTaskId(mediaObjects.getMediaTaskId());
        newObj.setName(mediaObjects.getName());
        newObj.setCreateTime(LocalDateTime.now());
        newObj.setSources(request.getSourceCodes());
        newObj.setFilterword(JSON.toJSONString(request.getFilterword()));
        newObj.setKeyword(mediaObjectsDomainService.mergeKeyword(request.getOrKeywords(), request.getAndKeywords()));
        newObj.setOrKeywords(JSON.toJSONString(request.getOrKeywords()));
        newObj.setAndKeywords(JSON.toJSONString(request.getAndKeywords()));
        newObj.setStartTime(request.getStartTime());
        newObj.setEndTime(request.getEndTime());
        newObj.setImg(gptPictureDomainService.defaultWordImg());
        newObj.setFinishTime(null);

        MediaService bean = SpringContext.getApplicationContext().getBean(MediaService.class);
        List<MediaTaskHandle> mediaTaskHandleList = bean.handleTransaction(newObj);

        strategyFactory.handleMqTactics(mediaTaskHandleList);

        try {
            // 编辑延时
            Thread.sleep(3000L);
        } catch (InterruptedException e) {
            log.warn("线程中断异常");
        }

        strategyFactory.noticeFs(mediaTaskHandleList);
        bean.handleMediaConversation(newObj, userCommon.getUserId(true));
        return BaseResponseBean.SUCCESS;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void handleMediaConversation(MediaObjects mediaObjects, Long userId) {
        // 失效原会话ID
        Long objectsId = mediaObjects.getId();
        mediaConversationDomainService.clean(objectsId, null);
        // 创建新对话
        ConversationResp.ConversationObject conversationObject = cozeDomainService.conversationCreate(userId);
        if (null == conversationObject) {
            log.error("创建会话失败");
            return;
        }
        Long conversationId = Long.valueOf(conversationObject.getId());
        mediaConversationDomainService.create(objectsId, conversationId, userId);
        log.info("用户{},对象{},创建会话成功[{}]", userId, objectsId, conversationId);
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public List<MediaTaskHandle> handleTransaction(MediaObjects newObj) {
        mediaObjectsDomainService.editObj(newObj);

        // 失效原handle
        mediaTaskHandleDomainService.updateByTaskIdAndMediaObjectsId(newObj.getMediaTaskId(), newObj.getId());

        // 创建新handle
        List<MediaTaskHandle> mediaTaskHandleList = mediaTaskHandleDomainService.initialHandleBatch(Collections.singletonList(newObj), HandleTypeEnum.EDIT);
        mediaTrendDomainService.saveAll(mediaTaskHandleList);

        return mediaTaskHandleList;
    }

    @Override
    public ResponseBean<WordCloudResp> resetWordCloud(ResetWordCloudReq req) {

        GenWordCloudReq genWordCloudReq = new GenWordCloudReq();
        genWordCloudReq.setMediaObjectsId(req.getMediaObjectsId());
        genWordCloudReq.setImgUrl(gptPictureDomainService.defaultWordImg());

        return genWordCloud(genWordCloudReq);
    }

    @Override
    public ResponseBean<WordCloudResp> getWordCloud(Long mediaObjectsId) {
        WordCloudResp wordCloudResp = new WordCloudResp();
        List<WordCloudImg> wordCloudImgList = new ArrayList<>();
        Map<String, GptPicture> imgListMap = gptPictureDomainService.findByObjIds(Collections.singletonList(mediaObjectsId))
                .stream().collect(Collectors.toMap(GptPicture::getImg, Function.identity(), (img, img2) -> img2));
        MediaTaskHandle handle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(null, mediaObjectsId);

        List<MediaWord> wordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(handle.getId()));

        List<MediaWord> mediaWordList = wordList.stream().filter(mediaWord -> {
            List<KeyValue> keyValues = JSON.parseArray(mediaWord.getAllWord(), KeyValue.class);
            if (CollectionUtil.isEmpty(keyValues) && mediaWord.getArtStatus().equals(WordArtEnum.ZERO.getCode())) {
                WordCloudImg wordCloudImg = new WordCloudImg();
                wordCloudImg.setCode(mediaWord.getArtStatus());
                wordCloudImg.setImgUrl(mediaWord.getGenImg());
                wordCloudImg.setSourceCode(mediaWord.getSourceCode());
                wordCloudImg.setSourceName(ExtraConditionEnum.nameOf(mediaWord.getSourceCode()));
                wordCloudImg.setWords(sortWords(mediaWord.getAllWord()));
                GptPicture gptPicture = imgListMap.get(mediaWord.getGenImg());
                if (null != gptPicture) {
                    wordCloudImg.setHeight(gptPicture.getHeight());
                    wordCloudImg.setWidth(gptPicture.getWidth());
                }
                wordCloudImgList.add(wordCloudImg);
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        List<WordCloudImg> taskWordCloudImg = getTaskWordCloudImg(mediaWordList, imgListMap);

        if (taskWordCloudImg.size() == 0 || !wordArtStart.checkTaskWordArtIsInitByHandleIds(Collections.singletonList(handle.getId()))) {
            try {
                wordCloudResp.setCode(1);
                Thread.sleep(3000);
                return ResponseBean.create(wordCloudResp);
            } catch (InterruptedException e) {
                log.warn("线程中断异常");
            }
        }
        wordCloudResp.setCode(0);
        wordCloudImgList.addAll(taskWordCloudImg);
        wordCloudResp.setWordCloudImgList(wordCloudImgList.stream().sorted(Comparator.comparing(WordCloudImg::getSourceCode)).collect(Collectors.toList()));
        return ResponseBean.create(wordCloudResp);
    }

    @Override
    public ResponseBean<TaskTrendResp> hotAnaly(Long mediaObjectsId) {

        MediaTaskHandle handle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(null, mediaObjectsId);
        TaskTrendResp taskTrendResp = retryGetTaskTrend(handle, 2);
        if (taskTrendResp.getCode() == 1) {
            try {
                Thread.sleep(5000L);
            } catch (InterruptedException e) {
                log.error("热点分析线程异常");
            }
        }
        return ResponseBean.create(taskTrendResp);
    }

    private TaskTrendResp retryGetTaskTrend(MediaTaskHandle handle, int count) {
        TaskTrendResp taskTrendResp = new TaskTrendResp();
        count--;

        MediaObjects mediaObjects = mediaObjectsDomainService.getById(handle.getMediaObjectsId());

        if (ObjectUtil.isEmpty(mediaObjects)) {
            taskTrendResp.setCode(-1);
            taskTrendResp.setTaskTrendList(new ArrayList<>());
            return taskTrendResp;
        }
        if (mediaAlgorithmStart.checkAnalyzeDay(handle)) {
            taskTrendResp.setCode(2);
            taskTrendResp.setTaskTrendList(new ArrayList<>());
            return taskTrendResp;
        }

        List<MediaTrend> mediaTrendList = mediaTrendDomainService.findByHandleIds(Collections.singletonList(handle.getId()));

        if (CollectionUtil.isEmpty(mediaTrendList)
                || !mediaAlgorithmStart.checkAlgorithmSuccess(handle)) {
            taskTrendResp.setCode(1);
            taskTrendResp.setTaskTrendList(new ArrayList<>());
            return taskTrendResp;
        }

        ArrayList<TaskTrend> taskTrends = new ArrayList<>();

        if (count <= 0) {
            taskTrendResp.setCode(1);
            taskTrendResp.setTaskTrendList(taskTrends);
            return taskTrendResp;
        }

        mediaTrendList.forEach(mediaTrend -> {
            TaskTrend taskTrend = new TaskTrend();
            taskTrend.setSourceCode(mediaTrend.getSourceCode());
            taskTrend.setSourceName(ExtraConditionEnum.nameOf(mediaTrend.getSourceCode()));
            taskTrend.setHotConclusions(getHotConclusions(mediaTrend));
            taskTrends.add(taskTrend);
        });

        long unFinish = taskTrends.stream()
                .filter(trend -> {
                    if (null == trend.getHotConclusions()) {
                        return true;
                    }
                    return trend.getHotConclusions().stream().filter(hot -> ObjectUtil.isNull(hot) || StringUtils.isEmpty(hot.getDate())).count() >= 3;
                })
                .count();
        if (unFinish == 0) {
            taskTrendResp.setCode(0);
            taskTrendResp.setTaskTrendList(taskTrends);
            return taskTrendResp;
        } else {
            mediaAlgorithmStart.checkAndHotAnalyze(handle);
        }

        return retryGetTaskTrend(handle, count);
    }

    private List<TaskTrend> getTaskTrend(List<MediaTrend> mediaTrends) {
        ArrayList<TaskTrend> taskTrends = new ArrayList<>();
        if (null == mediaTrends) {
            return taskTrends;
        }
        for (MediaTrend mediaTrend : mediaTrends) {
            TaskTrend taskTrend = new TaskTrend();
            taskTrend.setSourceCode(mediaTrend.getSourceCode());
            taskTrend.setSourceName(ExtraConditionEnum.nameOf(mediaTrend.getSourceCode()));
            taskTrend.setTrendDay(convertDataList(mediaTrend.getVolumeTrendDay(), mediaTrend.getInteractionTrendDay()));
            taskTrend.setTrendWeek(convertDataList(mediaTrend.getVolumeTrendWeek(), mediaTrend.getInteractionTrendWeek()));
            taskTrend.setTrendMonth(convertDataList(mediaTrend.getVolumeTrendMonth(), mediaTrend.getInteractionTrendMonth()));

            taskTrend.setHotConclusions(getHotConclusions(mediaTrend));
            taskTrends.add(taskTrend);
        }
        return taskTrends;
    }

    @Override
    public void taskInit(Long mediaObjectsId) {
        List<MediaTaskHandle> list;
        if (mediaObjectsId == null || mediaObjectsId == 0) {
            list = mediaTaskHandleDomainService.findByStatus();
        } else {
            list = mediaTaskHandleDomainService.findByTaskIdAndMediaObjectsId(null, mediaObjectsId);

        }
        list.forEach(e -> {
            MediaMsgBody mediaMsgBody = new MediaMsgBody(e.getMediaObjectsId(), e.getTaskId(), this.getClass().getSimpleName(), e.getId());
            if (e.getStatus() == 0) {
                crawlerMediaService.handleMq(mediaMsgBody, MediaHandleCrawlerListener.TOPIC);
            }

            if (e.getStatus() == 1) {
                wordArtStart.handleMq(mediaMsgBody);
                mediaAlgorithmStart.handleMq(mediaMsgBody);
            }
            log.info("taskInit 打印日志：{}", JSON.toJSONString(mediaMsgBody));
        });
    }

    @Override
    public void downloadNoteData(Long mediaObjectsId) {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(null, mediaObjectsId);
        List<MediaNote> mediaNoteList = mediaNoteDomainService.findByHandleId(handle.getId());
        Assert.isTrue(CollectionUtil.isNotEmpty(mediaNoteList), "笔记数据为空");

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletResponse response = requestAttributes.getResponse();

        ArrayList<ExcelMediaNote> excelMediaNotes = new ArrayList<>();
        for (MediaNote mediaNote : mediaNoteList) {
            ExcelMediaNote excelMediaNote = new ExcelMediaNote();
            excelMediaNote.setAuthor(mediaNote.getAuthor());
            excelMediaNote.setTitle(mediaNote.getTitle());
            excelMediaNote.setSourceName(ExtraConditionEnum.nameOf(mediaNote.getSourceCode()));
            excelMediaNote.setHeadImg(mediaNote.getHeadImg());
            excelMediaNote.setCoverOcrContent(spilContent(mediaNote.getCoverOcrContent()));
            excelMediaNote.setAudioOcrContent(spilContent(mediaNote.getAudioOcrContent()));
            excelMediaNote.setHighlightOcrContent(spilContent(mediaNote.getHighlightOcrContent()));
            excelMediaNote.setVideoContent(spilContent(mediaNote.getVideoContent()));
            excelMediaNote.setInteractionCnt(mediaNote.getInteractionCnt());
            excelMediaNote.setCollectionCnt(mediaNote.getCollectionCnt());
            excelMediaNote.setLikeCnt(mediaNote.getLikeCnt());
            excelMediaNote.setRepostsCnt(mediaNote.getRepostsCnt());
            excelMediaNote.setReviewCnt(mediaNote.getReviewCnt());

            excelMediaNote.setNoteUrl(mediaNote.getNoteUrl());
            excelMediaNote.setContent(spilContent(mediaNote.getContent()));
            excelMediaNote.setIsOriginal(mediaNote.getIsOriginal());
            excelMediaNote.setPublishTime(Times.dateTimeToStringFormat(Times.toLocalDateTime(mediaNote.getPublishTime())));
            excelMediaNotes.add(excelMediaNote);
        }
        try {
            MediaTask task = mediaTaskDomainService.getById(handle.getTaskId());
            String fileName = task.getName() + LocalDate.now().toString().replaceAll("-", "");
            response.setContentType(MimeEnum.XLSX.getType());
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharsetUtil.UTF_8));
            EasyExcel.write(response.getOutputStream(), ExcelMediaNote.class).sheet().doWrite(excelMediaNotes);
        } catch (Exception e) {
            log.error("excel文件下载异常，分析对象id：[{}]，e:{}", mediaObjectsId, e.toString());
            throw new ResponseException("文件下载异常");
        }

    }
    private String spilContent(String content) {
        if (null != content && content.length() > 3000) {
            return content.substring(0, 3000);
        }
        return content;
    }
}
