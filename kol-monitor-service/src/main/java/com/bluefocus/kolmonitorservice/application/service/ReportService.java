package com.bluefocus.kolmonitorservice.application.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.basebean.exception.ResponseException;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.dto.ppt.PPTData;
import com.bluefocus.kolmonitorinterface.dto.ppt.PPTemplate;
import com.bluefocus.kolmonitorinterface.dto.ppt.PptChart;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleTrend;
import com.bluefocus.kolmonitorinterface.dto.res.PieChartResp;
import com.bluefocus.kolmonitorinterface.dto.res.PlatformReportData;
import com.bluefocus.kolmonitorinterface.dto.res.ReportResponse;
import com.bluefocus.kolmonitorservice.application.service.monitor.ArticleExport;
import com.bluefocus.kolmonitorservice.application.service.monitor.PptExport;
import com.bluefocus.kolmonitorservice.base.common.PPtConstant;
import com.bluefocus.kolmonitorservice.base.enums.MimeEnum;
import com.bluefocus.kolmonitorservice.base.enums.ParameterEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import com.bluefocus.kolmonitorservice.base.enums.ProjectStatusEnum;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDataTrendDomainService;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDomainService;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import com.bluefocus.kolmonitorservice.domain.project.entity.ProjectDataTrend;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bluefocus.kolmonitorservice.base.common.PPtConstant.*;

/**
 * <AUTHOR>
 * @Date: 2023/3/9 13:44
 * @Description:
 */
@Log4j2
@Service
@RequiredArgsConstructor
public class ReportService {

    private final ProjectDomainService projectDomainService;
    private final ArticleDomainService articleDomainService;
    private final ProjectDataTrendDomainService projectDataTrendDomainService;
    private final ArticleExport articleExport;
    private final PptExport pptExport;
    private final UserCommon userCommon;
    private final ThreadPoolExecutor threadPoolExecutor;

    public ResponseBean<ReportResponse> findProjectReport(Long projectId) {
        Project project = projectDomainService.getById(projectId);
        ReportResponse reportResponse = new ReportResponse();
        reportResponse.setProjectId(projectId);
        reportResponse.setMonitorPeriod(project.getMonitorPeriod());
        reportResponse.setProjectName(project.getName());

        ProjectDataTrend projectDataTrend = projectDataTrendDomainService.getById(projectId);

        if (ObjectUtil.isEmpty(projectDataTrend)) {
            return ResponseBean.create(reportResponse);
        }
        List<PieChartResp> notesRateList = JSON.parseArray(projectDataTrend.getPlantNotesRate(), PieChartResp.class);
        notesRateList.forEach(note -> note.setPlatName(PlatformEnum.codeOf(note.getPlatType())));
        reportResponse.setPieNotesRate(notesRateList);

        List<PieChartResp> readRateList = JSON.parseArray(projectDataTrend.getPlantReadRate(), PieChartResp.class);
        readRateList.forEach(note -> note.setPlatName(PlatformEnum.codeOf(note.getPlatType())));
        reportResponse.setPieReadRates(readRateList);

        JSONArray platArray = JSON.parseArray(projectDataTrend.getPlantData());
        reportResponse.setXhsReportData(getPlatData(platArray, PlatformEnum.XHS.getIndex()));
        reportResponse.setBzReportData(getPlatData(platArray, PlatformEnum.BLI.getIndex()));
        reportResponse.setKsReportData(getPlatData(platArray, PlatformEnum.KS.getIndex()));
        reportResponse.setDyReportData(getPlatData(platArray, PlatformEnum.DY.getIndex()));
        if (platArray.size() > 2) {
            reportResponse.setTotalReportData(getPlatData(platArray, PlatformEnum.ALL.getIndex()));
        }
        return ResponseBean.create(reportResponse);
    }

    private PlatformReportData getPlatData(JSONArray platArray, Integer index) {

        for (Object o : platArray) {
            JSONObject map = (JSONObject) o;
            if (index.equals(map.getInteger("plat_type"))) {
                PlatformReportData platformReportData = new PlatformReportData();
                platformReportData.setArticleNum(map.getInteger("total_notes_num"));
                platformReportData.setTotalReadNum(map.getLong("total_read_num"));
                platformReportData.setTotalLikeNum(map.getLong("total_like_num"));
                platformReportData.setTotalCollectionNum(map.getLong("total_collection_num"));
                platformReportData.setTotalCommentNum(map.getLong("total_comment_num"));
                platformReportData.setTotalInteractionNum(map.getLong("total_interaction_num"));

                String totalInteractionRate = map.getString("total_interaction_rate");
                platformReportData.setInteractionRate(StringUtils.isEmpty(totalInteractionRate) ? null : new BigDecimal(totalInteractionRate));

                platformReportData.setTotalShareNum(map.getLong("total_share_num"));
                platformReportData.setTotalFollowNum(map.getLong("total_follow_num"));
                platformReportData.setTotalExposure(map.getLong("total_exposure"));
                platformReportData.setTotalCoinNum(map.getLong("total_coin_num"));
                platformReportData.setTotalBulletNum(map.getLong("total_bullet_num"));
                platformReportData.setAvgReadNum(map.getInteger("avg_read_num"));
                platformReportData.setAvgExposureNum(map.getInteger("avg_exposure_num"));
                platformReportData.setAvgLikeNum(map.getInteger("avg_like_num"));
                platformReportData.setAvgCommentNum(map.getInteger("avg_comment_num"));
                platformReportData.setAvgInteractionNum(map.getInteger("avg_interaction_num"));
                platformReportData.setAvgCollectionNum(map.getInteger("avg_collection_num"));
                platformReportData.setAvgShareNum(map.getInteger("avg_share_num"));
                platformReportData.setAvgFollowNum(map.getInteger("avg_follow_num"));
                platformReportData.setAvgBulletNum(map.getInteger("avg_bullet_num"));
                platformReportData.setAvgCoinNum(map.getInteger("avg_coin_num"));

                Map<String, ArticleTrend> trendMap = JSON.parseArray(map.getString("tend"), ArticleTrend.class)
                        .stream().collect(Collectors.toMap(ArticleTrend::getName, Function.identity()));

                platformReportData.setReadTrend(trendMap.get(ParameterEnum.READ.getName()));
                platformReportData.setLikeTrend(trendMap.get(ParameterEnum.LIKE.getName()));
                platformReportData.setCollectionTrend(trendMap.get(ParameterEnum.COLLECTION.getName()));
                platformReportData.setCommentTrend(trendMap.get(ParameterEnum.COMMENT.getName()));
                platformReportData.setInteractionTrend(trendMap.get(ParameterEnum.INTERACTION.getName()));
                platformReportData.setShareTrend(trendMap.get(ParameterEnum.SHARE.getName()));
                platformReportData.setFollowTrend(trendMap.get(ParameterEnum.FOLLOW.getName()));
                platformReportData.setBulletTrend(trendMap.get(ParameterEnum.BULLET.getName()));
                platformReportData.setCoinTrend(trendMap.get(ParameterEnum.COIN.getName()));
                return platformReportData;
            }
        }
        return null;
    }

    public void exportReport(Long projectId) {
        Project project = projectDomainService.getById(projectId);
        Assert.isTrue(project.getStatus().equals(ProjectStatusEnum.MONITORING.getIndex())
                        || project.getStatus().equals(ProjectStatusEnum.MONITORED.getIndex())
                , "项目还未开始检测，暂无数据");
        List<Article> articleList = articleDomainService.findArticleList(project.getId(), false);
        articleExport.exportReport(project, articleList);
    }

    public void exportReportForPpt(Long projectId) {
        Project project = projectDomainService.getById(projectId);
        Long operatorId = project.getOperatorId();
        String userName = userCommon.getUserName(operatorId);
        ProjectDataTrend projectDataTrend = projectDataTrendDomainService.getById(projectId);
        Assert.notNull(projectDataTrend, "项目还未生成效果数据");

        String fileName = project.getName();
        String allFileName = fileName + "." + MimeEnum.PPTX.getExt();
        ConcurrentSkipListMap<String, JSONObject> pageMaps = new ConcurrentSkipListMap<>();
        CopyOnWriteArrayList<PPTemplate> templates = new CopyOnWriteArrayList<>();

        buildPptData(project, projectDataTrend, pageMaps, templates, userName);

        String tmpDir = PPtConstant.TMP_DIR.replace("{pId}", project.getId().toString());
        pptExport.creatPptFile(allFileName, PPtConstant.TEMPLATE_DIR, tmpDir, pageMaps, templates);
        pptExport.downPpt(fileName, tmpDir + allFileName);

    }

    private void buildPptData(Project project, ProjectDataTrend projectDataTrend
            , ConcurrentSkipListMap<String, JSONObject> pageMaps, CopyOnWriteArrayList<PPTemplate> templates, String userName) {

        PPTData<PptChart> pieChart1 = PptExport.getPieChartData(projectDataTrend.getPlantNotesRate(), PIE_NOTE_RENAME, PIE_NOTE_EXCEL_COL_1, PIE_NOTE_EXCEL_COL_2);
        PPTData<PptChart> pieChart2 = PptExport.getPieChartData(projectDataTrend.getPlantReadRate(), PIE_READ_RENAME, PIE_READ_EXCEL_COL_1, PIE_READ_EXCEL_COL_2);

        JSONArray platLineChart = JSON.parseArray(projectDataTrend.getPlantData());
        List<PlatformEnum> platformList = Arrays.asList(PlatformEnum.values());

        CountDownLatch latch = new CountDownLatch(platformList.size());
        pptExport.handleHomePageData(templates, pageMaps, userName, project.getMonitorPeriod(), project.getName());
        platformList.forEach(platform -> {
                    Callable<Boolean> callable = () -> {
                        latch.countDown();
                        PlatformReportData platData = getPlatData(platLineChart, platform.getIndex());
                        if (ObjectUtil.isNotNull(platData) && ObjectUtil.isNotEmpty(platData.getArticleNum())) {
                            List<String> tableData = PptExport.getTableData(platData);
                            pptExport.handleLineData(templates, pageMaps, platData, tableData, pieChart1, pieChart2, platform);
                        }
                        return true;
                    };
                    Future<Boolean> future = threadPoolExecutor.submit(callable);
                    Boolean result = false;
                    try {
                        result = future.get();
                    } catch (Exception e) {
                        log.error("ReportService.exportReportForPPT 线程处理业务异常：", e);
                    } finally {
                        if (ObjectUtil.isNull(result) || !result) {
                            log.error("业务数据异常，项目id：{}", project.getId());
                        }
                    }
                }
        );

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("ReportService.exportReportForPPT 线程中断异常：", e);
            throw new ResponseException("生成ppt异常");
        }
    }

}
