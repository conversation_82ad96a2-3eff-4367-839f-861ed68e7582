package com.bluefocus.kolmonitorservice.application.valid;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.IProjectService;
import com.bluefocus.kolmonitorinterface.dto.res.ProjectResponse;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2022/1/12 10:19 上午
 */
public class ProjectValidHandler implements IProjectService {

    @Override
    public ResponseBean<PageBean<ProjectResponse>> findProjectList(Integer page, Integer limit) {
        Assert.notNull(page, "请传入当前页数");
        Assert.notNull(limit, "请传入每页条数");
        return null;
    }

    @Override
    public BaseResponseBean deleteProject(Long id) {
        return null;
    }


}
