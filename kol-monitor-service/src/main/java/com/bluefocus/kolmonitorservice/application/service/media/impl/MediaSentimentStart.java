package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.domain.datastory.DataStoryDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStorySentimentDistributeResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaSentiment;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTrend;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaSentimentDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTrendDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0023 2024/5/23 11:50
 * @description: 正-负 / 正+负面
 */
@Slf4j
@Order(3)
@Service
@RequiredArgsConstructor
public class MediaSentimentStart extends IMediaStart {

    private final DataStoryDomainService dataStoryService;
    private final MediaSentimentDomainService mediaSentimentDomainService;
    private final MediaTrendDomainService mediaTrendDomainService;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {

        DataHandleState dataHandleState = new DataHandleState();
        MediaSentiment mediaSentiment = mediaSentimentDomainService.findByHandleId(handle.getId());
        if (ObjectUtil.isNotNull(mediaSentiment) && mediaSentiment.getStatus().equals(FinishStatusEnum.ANALY.getCode())) {
            dataHandleState.setSentiment(true);
            return dataHandleState;
        }

        DataStoryEntity req = getDataStoryFixedParam(handle);
        req.setSources(Arrays.stream(handle.getSources().split(",")).map(code -> ExtraConditionEnum.codeOf(Integer.valueOf(code))).collect(Collectors.toList()));

        DataStorySentimentDistributeResponse.ResultDTO trend = retryReq(req, 3, handle.getMediaObjectsId());
        if (trend == null || trend.getTotal() == null) {
            log.error("api情感度趋势数据请求失败,handle={},source={}，跳过", handle.getId(), sourceCode);
            dataHandleState.setSentiment(false);
            MediaObjects obj = mediaObjectsDomainService.getById(handle.getId());
            objFail(handle, obj, BusinessException.KEY_OTHER, null);
            return dataHandleState;
        }

        HashMap<Long, String> sentimentMap = trend.getTotal();
        ArrayList<KeyValue<Integer, BigDecimal>> sourceMapList = new ArrayList<>();
        List<MediaTrend> byHandleId = mediaTrendDomainService.findByHandleId(handle.getId());
        long totalVolume = byHandleId.stream().filter(m -> m.getSourceCode() != 0 && m.getAllVolume() != null).mapToLong(MediaTrend::getAllVolume).sum();
        BigDecimal positive = BigDecimal.ZERO;
        BigDecimal negative = BigDecimal.ONE;
        for (Long type : sentimentMap.keySet()) {
            KeyValue<Integer, BigDecimal> sourceMap = new KeyValue<>();
            sourceMap.setKey(String.valueOf(type));
            BigDecimal bigDecimal = new BigDecimal(sentimentMap.get(type).replace("%", ""))
                    .divide(BigDecimal.valueOf(100), 4, 4);
            if (type == 1) {
                positive = bigDecimal;
            }
            if (type == -1) {
                negative = bigDecimal;
            }
            sourceMap.setRate(bigDecimal);
            sourceMap.setValue(bigDecimal.multiply(BigDecimal.valueOf(totalVolume)));
            sourceMapList.add(sourceMap);
        }

        if (null == mediaSentiment) {
            mediaSentiment = new MediaSentiment();
            mediaSentiment.setCreateTime(LocalDateTime.now());
            mediaSentiment.setRetryCount(0);
        } else {
            mediaSentiment.setRetryCount(mediaSentiment.getRetryCount() + 1);
        }

        mediaSentiment.setHandleId(handle.getId());
        mediaSentiment.setMediaObjectsId(handle.getMediaObjectsId());
        mediaSentiment.setRateSentiments(JSON.toJSONString(sourceMapList));
        BigDecimal add = positive.add(negative);
        if (add.equals(BigDecimal.ZERO)) {
            add = BigDecimal.ONE;
        }
        mediaSentiment.setTotalSentiments(positive.subtract(negative).divide(add, 4));
        mediaSentiment.setStatus(FinishStatusEnum.ANALY.getCode());
        mediaSentimentDomainService.saveOrUpdate(mediaSentiment);
        dataHandleState.setSentiment(true);
        return dataHandleState;
    }

    private DataStorySentimentDistributeResponse.ResultDTO retryReq(DataStoryEntity req, int i, Long id) {
        DataStorySentimentDistributeResponse sentimentDistribute = null;
        try {
            sentimentDistribute = dataStoryService.getSentimentDistribute(req);
            if (sentimentDistribute.isSuccess()) {
                return sentimentDistribute.getData();
            } else if (i > 0) {
                return retryReq(req, --i, id);
            }

        } catch (Exception e) {
            log.error("请求数说总互动量接口报错，第{}次", 3 - i);
            return retryReq(req, i, id);
        } finally {
            if ((null == sentimentDistribute || !sentimentDistribute.isSuccess()) && i == 0) {
                log.error("请求数说情感度分布接口重试次数耗尽：{}", sentimentDistribute);
                String con = sentimentDistribute != null ? sentimentDistribute.getMsg() : null;
                handleMsg(null, String.format("请求数说情感度分布接口异常,对象id=%s，内容：%s", id, con));
            }
        }
        return null;
    }

    public void drawMediaSentimentRate(Map<String, BigDecimal> sentimentDistribute, MediaTaskHandle handle) {

        MediaSentiment mediaSentiment = mediaSentimentDomainService.findByHandleId(handle.getId());
        if (null == mediaSentiment) {
            mediaSentiment = new MediaSentiment();
            mediaSentiment.setCreateTime(LocalDateTime.now());
            mediaSentiment.setRetryCount(0);
        }
        mediaSentiment.setHandleId(handle.getId());
        mediaSentiment.setMediaObjectsId(handle.getMediaObjectsId());
        BigDecimal sum = new BigDecimal(String.valueOf(sentimentDistribute.values().stream().mapToDouble(BigDecimal::doubleValue).sum()));
        mediaSentiment.setTotalSentiments(sum);
        List<KeyValue<Integer, BigDecimal>> rateList = sentimentDistribute.keySet().stream().map(k -> {
            KeyValue<Integer, BigDecimal> sourceMap = new KeyValue<>();
            sourceMap.setKey(String.valueOf(k));
            sourceMap.setValue(sentimentDistribute.get(k));
            sourceMap.setRate(sourceMap.getValue().divide(sum.compareTo(BigDecimal.ZERO) > 0 ? sum : BigDecimal.ONE, 3, 4));
            return sourceMap;
        }).collect(Collectors.toList());
        mediaSentiment.setRateSentiments(JSON.toJSONString(rateList));
        mediaSentiment.setStatus(FinishStatusEnum.ANALY.getCode());
        mediaSentimentDomainService.saveOrUpdate(mediaSentiment);
    }

    public void handleDefault(MediaTaskHandle handle) {
        MediaSentiment mediaSentiment = mediaSentimentDomainService.findByHandleId(handle.getId());
        if (null == mediaSentiment) {
            mediaSentiment = new MediaSentiment();
            mediaSentiment.setCreateTime(LocalDateTime.now());
            mediaSentiment.setRetryCount(0);
        }
        mediaSentiment.setStatus(FinishStatusEnum.ANALY.getCode());
        mediaSentiment.setHandleId(handle.getId());
        mediaSentiment.setMediaObjectsId(handle.getMediaObjectsId());
        mediaSentiment.setRateSentiments("[]");
        mediaSentiment.setTotalSentiments(BigDecimal.ZERO);
        mediaSentimentDomainService.saveOrUpdate(mediaSentiment);
    }
}
