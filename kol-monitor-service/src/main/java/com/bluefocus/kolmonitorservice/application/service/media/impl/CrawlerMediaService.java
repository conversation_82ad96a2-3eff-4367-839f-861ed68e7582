package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.HandleTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.base.listener.media.*;
import com.bluefocus.kolmonitorservice.base.util.KafkaSender;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.crawl.CrawlerMediaDomainService;
import com.bluefocus.kolmonitorservice.domain.crawl.common.CrawlerStateCode;
import com.bluefocus.kolmonitorservice.domain.crawl.req.CrawlerMediaReq;
import com.bluefocus.kolmonitorservice.domain.crawl.resp.CrawlerMediaResp;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTextResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTask;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTrend;
import com.bluefocus.kolmonitorservice.domain.media.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0003 2024/6/3 16:01
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrawlerMediaService {

    private final KafkaSender kafkaSender;
    private final CrawlerMediaDomainService crawlerMediaDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final MediaTrendDomainService mediaTrendDomainService;
    private final MediaVolumeStart mediaVolumeStart;
    private final MediaInteractionStart mediaInteractionStart;
    private final MediaAllDataStart mediaAllDataStart;
    private final MediaNoteDomainService mediaNoteDomainService;
    private final MediaSentimentStart mediaSentimentStart;
    private final MediaWordCouldStart mediaWordCouldStart;
    private final MediaTaskDomainService mediaTaskDomainService;

    @Value("${robot.developer}")
    private String devRobotUrl;
    @Value("${robot.url}")
    private String robotUrl;

    public void handleMq(MediaMsgBody mediaMsgBody, String topic) {
        kafkaSender.sendByKey(topic, String.valueOf(mediaMsgBody.getMediaTaskId()), mediaMsgBody);
    }

    public void doHandle(MediaObjects obj, MediaTaskHandle handle) {
        DataHandleState dataHandleState = new DataHandleState();
        List<String> sourceNames = Arrays.stream(handle.getSources().split(",")).map(k -> ExtraConditionEnum.codeOf(Integer.valueOf(k))).collect(Collectors.toList());

        long startTime = System.currentTimeMillis();

        // 重试3次
//        CrawlerMediaResp crawlerResp = getCrawlerData(crawlerMediaDomainService.getCrawlerDataParam(handle, sourceNames), handle.getId(), 3);
        String s = FileUtil.readString("D:\\shushuo.txt", "UTF-8");
        CrawlerMediaResp crawlerResp = JSON.parseObject(s, CrawlerMediaResp.class);
        handle.setCralwerTime(System.currentTimeMillis() - startTime);

        MediaMsgBody mediaMsgBody = new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), this.getClass().getSimpleName(), handle.getId());

        if (!checkCrawlerResp(handle, crawlerResp, mediaMsgBody, obj)) {
            log.error("请求爬虫数说聚合接口失败：分析对象【{}】,failureList={}，耗时={}ms", obj.getId(), ObjectUtil.isNotNull(crawlerResp) ? crawlerResp.getFailureList() : "全部失败"
                    , System.currentTimeMillis() - startTime);
            handle.setCralwerRetry(handle.getCralwerRetry() + 1);
//            handle.setErrMsg(String.format("爬虫数说聚合接口失败,code为[%s]", crawlerResp != null ? crawlerResp.getStateCode() : "null"));
            mediaTaskHandleDomainService.updateById(handle);
            return;
        }
        log.info("请求爬虫数说聚合接口完毕：对象【{}】,code={},failureList={}，耗时={}ms", obj.getId(), crawlerResp.getStateCode(), crawlerResp.getFailureList(), System.currentTimeMillis() - startTime);

        // 落库
        storeData(crawlerResp, handle, dataHandleState, sourceNames);

        boolean requireTask = false;
        try {
            // 分平台判断 情感度|原帖|词云为null | 0时，判断声量是否为0，=0爬虫成功  !=0爬虫失败 发预警
            Thread.sleep(1000);
            dataHandleState = mediaAllDataStart.checkAndHandle(handle, dataHandleState);

            if (dataHandleState.checkAllState()) {
                obj.setStatus(handle.getType().equals(HandleTypeEnum.CREAT.getType()) ? MediaEditStatusEnum.ANALY.getCode() : MediaEditStatusEnum.EDIT_ANALY.getCode());
                obj.setFinishTime(LocalDateTime.now());
                handle.setStatus(FinishStatusEnum.ANALY.getCode());
                handle.setCralwerFinishTime(LocalDateTime.now());
                requireTask = true;
            } else {
                log.warn("爬虫当前对象有部分数据缺失, handle={}, 处理结果={},响应fail={}", handle.getId(), JSON.toJSONString(dataHandleState), crawlerResp.getFailureList());
                List<MediaTrend> mediaTrendDays = mediaTrendDomainService.findByHandleIds(Collections.singletonList(handle.getId()));
                if (mediaTrendDays.stream().filter(t -> !t.getSourceCode().equals(ExtraConditionEnum.ALL.getCode())).anyMatch(t -> t.getAllVolume() != null && t.getAllVolume() >= MediaVolumeStart.MAX_VOLUME)) {
                    MediaTask task = mediaTaskDomainService.getById(handle.getTaskId());
                    mediaAllDataStart.handleMsg(robotUrl, String.format("请求爬虫数说聚合接口异常(声量大于10亿，且缺失部分数据) \n任务名称[%s] \n处理单元=%s,关键词=%s \n请求爬虫地址=[%s], 爬虫响应code=%s \n爬虫各字段处理结果=%s"
                            , task.getName(), handle.getId(), handle.getKeyword(), crawlerResp.getReqUrl(), crawlerResp.getStateCode(), JSON.toJSONString(dataHandleState)));
                    mediaVolumeStart.objFail(handle, obj, null, BusinessException.KEY_TIME_OUT);
                    this.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
                } else {

                    // 发api 发预警
                    MediaTask task = mediaTaskDomainService.getById(handle.getTaskId());
                    mediaAllDataStart.handleMsg(robotUrl, String.format("请求爬虫数说聚合接口异常(有部分数据缺失，后续任务已转发api处理) \n任务名称[%s] \n处理单元=%s,关键词=%s \n请求爬虫地址=[%s], 爬虫响应code=%s \n爬虫各字段处理结果=%s"
                            , task.getName(), handle.getId(), handle.getKeyword(), crawlerResp.getReqUrl(), crawlerResp.getStateCode(), JSON.toJSONString(dataHandleState)));
                    this.handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
                }
            }
        } catch (Exception e) {
            log.error("处理爬虫数据异常,handle={},e={}", handle.getId(), e);
            this.handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
        } finally {
            mediaTaskHandleDomainService.updateById(handle);
            mediaAllDataStart.updateMediaObjects(obj);
            // 全部成功 进行后续任务处理
            if (requireTask) {
                this.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
                this.handleMq(mediaMsgBody, MediaHandleNoteDayListener.TOPIC);
                this.handleMq(mediaMsgBody, MediaHandleWordCloudListener.TOPIC);
            }
        }

    }

    private void storeData(CrawlerMediaResp crawlerDataStory, MediaTaskHandle handle, DataHandleState dataHandleState, List<String> sourceNames) {
        List<String> failureList = crawlerDataStory.getFailureList();
        try {
            // 趋势数据入库
            HashMap<String, List<KeyValue<String, Long>>> volumeTread = getTrendMap(crawlerDataStory.getVolumeTread(), sourceNames, "volumeTread");
            dataHandleState.setVolume(volumeTread.size() == sourceNames.size());
            mediaVolumeStart.drawMediaVolumTrend(volumeTread, handle);
            HashMap<String, List<KeyValue<String, Long>>> interactionTread = getTrendMap(crawlerDataStory.getInteractionTread(), sourceNames, "interactionTread");
            mediaInteractionStart.drawMediaInteractionTrend(interactionTread, handle);
            dataHandleState.setInteraction(interactionTread.size() == sourceNames.size());
        } catch (Exception e) {
            log.warn("保存爬虫数说聚合数据异常,处理id={},e={}", handle.getId(), e);
        }

        Map<String, BigDecimal> sentimentDistribute = crawlerDataStory.getSentimentDistribute();
        if (failureList.contains("sentimentDistribute") || CollectionUtil.isEmpty(sentimentDistribute)) {
            log.warn("爬虫数说聚合接口缺失sentimentDistribute数据，对象={}", handle.getMediaObjectsId());
            dataHandleState.setSentiment(false);
        } else {
            mediaSentimentStart.drawMediaSentimentRate(crawlerDataStory.getSentimentDistribute(), handle);
            dataHandleState.setSentiment(true);
        }

        Map<String, Map<String, Long>> cloudWordMap = crawlerDataStory.getCloudWord();
        if (failureList.contains("cloudWord") || CollectionUtil.isEmpty(cloudWordMap)) {
            log.warn("爬虫数说聚合接口缺失cloudWord数据，对象={}", handle.getMediaObjectsId());
            dataHandleState.setCloudWord(false);
        } else {
            mediaWordCouldStart.drawMediaWordData(cloudWordMap, handle);
        }

        Map<String, DataStoryTextResponse.ResultDTO> yuanwenMap = crawlerDataStory.getYuanwen();
        if (failureList.contains("yuanwen") || CollectionUtil.isEmpty(yuanwenMap)) {
            log.warn("爬虫数说聚合接口缺失yuanwen数据，对象={}", handle.getMediaObjectsId());
            dataHandleState.setYuanwen(false);
        } else {
            AtomicInteger fail = new AtomicInteger();
            sourceNames.forEach(name -> {
                int count = mediaNoteDomainService.findCountByHandle(handle.getMediaObjectsId(), handle.getId(), ExtraConditionEnum.keyOf(name));
                if (count > 0) {
                    log.info("爬虫当前对象笔记已经存在:{}, 数据源={}", handle.getMediaObjectsId(), name);
                    return;
                }
                DataStoryTextResponse.ResultDTO resultDTO = yuanwenMap.get(name);
                if (ObjectUtil.isNull(resultDTO) || resultDTO.getTotal().size() == 0) {
                    log.warn("爬虫当前对象笔记数据缺失,对象={}, 数据源={}", handle.getMediaObjectsId(), name);
                    MediaTrend trend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), ExtraConditionEnum.keyOf(name));
                    if (trend.getAllVolume() == null || trend.getAllVolume() != 0L) {
                        fail.getAndIncrement();
                    }
                    return;
                }
                mediaNoteDomainService.saveNotes(resultDTO, handle.getMediaObjectsId(), handle.getId(), ExtraConditionEnum.keyOf(name));
                dataHandleState.setYuanwenNum(dataHandleState.getYuanwenNum() + 1);
            });
            dataHandleState.setYuanwen(fail.get() == 0);
        }
    }

    private CrawlerMediaResp getCrawlerData(CrawlerMediaReq crawlerMediaReq, Long handleId, int count) {
        count--;
        if (count < 0) {
            return null;
        }
        CrawlerMediaResp resp = crawlerMediaDomainService.getCrawlerDataStory(crawlerMediaReq, handleId);
        if (ObjectUtil.isNotNull(resp)) {
            return resp;
        } else {
            log.error("爬虫聚合重试剩余{}次,handleId={}", count, handleId);
            return getCrawlerData(crawlerMediaReq, handleId, count);
        }
    }

    /**
     * 校验code 校验声量|互动量为null 爬虫失败，放入api
     */
    private boolean checkCrawlerResp(MediaTaskHandle handle, CrawlerMediaResp crawlerResp, MediaMsgBody mediaMsgBody, MediaObjects obj) {

        // 响应为null 开发预警
        if (null == crawlerResp) {
            mediaAllDataStart.handleMsg(robotUrl, String.format("请求爬虫数说聚合接口失败(响应数据为null) \n当前任务=%s,处理单元=%s,当前关键词=%s"
                    , handle.getMediaObjectsId(), handle.getId(), handle.getKeyword()));
            handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
            return false;
        }
        List<String> failureList = crawlerResp.getFailureList();

        // code
        switch (crawlerResp.getStateCode()) {
            case CrawlerStateCode.SUCCESS:
                boolean volumeTread = CollectionUtil.isEmpty(crawlerResp.getVolumeTread()) || failureList.contains("volumeTread");
                boolean interactionTread = CollectionUtil.isEmpty(crawlerResp.getInteractionTread()) || failureList.contains("interactionTread");
                if (volumeTread || interactionTread) {
                    // 缺失声量 发产品预警
                    mediaAllDataStart.handleMsg(robotUrl, String.format("请求爬虫数说接口失败(聚合声量|互动量缺失) \n请求地址[%s],响应code=%s \n当前处理单元=%s,当前关键词=%s"
                            , crawlerResp.getReqUrl(), crawlerResp.getStateCode(), handle.getId(), handle.getKeyword()));
                    handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
                    return false;
                }
                return true;
            case CrawlerStateCode.LOGIN_ERR:
                // 登录失败，发产品预警，并且api兜底
                mediaAllDataStart.handleMsg(robotUrl, String.format("请求爬虫数说接口失败(登录失败) \n请求地址[%s],StateCode=%s \n当前处理单元=%s,当前关键词=%s"
                        , crawlerResp.getReqUrl(), crawlerResp.getStateCode(), handle.getId(), handle.getKeyword()));
                handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
                return false;

            case CrawlerStateCode.JSON_ERR_303:
                // 登录失败，发产品预警，并且api兜底
                mediaAllDataStart.handleMsg(devRobotUrl, String.format("请求爬虫数说聚合失败 \n请求地址[%s],StateCode=%s \n当前处理单元=%s,当前关键词=%s"
                        , crawlerResp.getReqUrl(), crawlerResp.getStateCode(), handle.getId(), handle.getKeyword()));
                handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
                return false;
            case CrawlerStateCode.JSON_ERR:
            case CrawlerStateCode.JSON_ERR_301:
            case CrawlerStateCode.JSON_ERR_302:
            case CrawlerStateCode.JSON_ERR_309:
                // 任务失败，发产品预警
                mediaAllDataStart.handleMsg(robotUrl, String.format("请求爬虫数说聚合失败 \n请求地址[%s],StateCode=%s \n当前处理单元=%s,当前关键词=%s"
                        , crawlerResp.getReqUrl(), crawlerResp.getStateCode(), handle.getId(), handle.getKeyword()));
                mediaVolumeStart.objFail(handle, obj, null, crawlerResp.getErrorMsg());
                handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
                return false;
            case CrawlerStateCode.PRAM_ERR:
            case CrawlerStateCode.PRAM_ERR_101:
            case CrawlerStateCode.PRAM_ERR_109:
            default:
                // 其它失败开发预警
                mediaAllDataStart.handleMsg(devRobotUrl, String.format("请求爬虫数说聚合失败 \n请求地址[%s],StateCode=%s \n当前处理单元=%s,当前关键词=%s"
                        , crawlerResp.getReqUrl(), crawlerResp.getStateCode(), handle.getId(), handle.getKeyword()));
                handleMq(mediaMsgBody, MediaHandleCrawlerListener.TOPIC);
                return false;
        }
    }

    private HashMap<String, List<KeyValue<String, Long>>> getTrendMap(Map<String, Map<String, Long>> trendMap, List<String> sourceNames, String param) {
        HashMap<String, List<KeyValue<String, Long>>> trendResult = new HashMap<>(4);
        if (CollectionUtil.isEmpty(trendMap)) {
            log.warn("爬虫数说聚合接口：缺失字段{}，数据为null,跳过", param);
            return trendResult;
        }
        sourceNames.forEach(name -> {
            Map<String, Long> map = trendMap.get(name);
            if (CollectionUtil.isEmpty(map)) {
                log.warn("爬虫数说聚合接口：当前平台{}趋势，字段{}，数据为null,跳过", name, param);
                return;
            }
            List<KeyValue<String, Long>> dateList = map.keySet().stream().map(k ->
                    new KeyValue<>(Times.toLocalDateTime(Long.parseLong(k)).toLocalDate().toString().replace("-", ""), map.get(k))).collect(Collectors.toList());
            trendResult.put(String.valueOf(ExtraConditionEnum.keyOf(name)), dateList);
        });
        return trendResult;
    }

    private String complyRate(HashMap<String, List<KeyValue<String, Long>>> map, Long allCount) {

        return JSON.toJSONString(map.keySet().stream().map(k -> {
            KeyValue<Integer, Long> sourceMap = new KeyValue<>();
            sourceMap.setKey(k);
            sourceMap.setValue(map.get(k).stream().mapToLong(KeyValue::getValue).sum());
            sourceMap.setRate(BigDecimal.valueOf((double) sourceMap.getValue() / allCount)
                    .setScale(10, BigDecimal.ROUND_UP));
            return sourceMap;
        }).collect(Collectors.toList()));

    }
}
