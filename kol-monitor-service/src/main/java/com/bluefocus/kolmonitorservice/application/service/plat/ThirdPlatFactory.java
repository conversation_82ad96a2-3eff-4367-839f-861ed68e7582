package com.bluefocus.kolmonitorservice.application.service.plat;

import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/7 16:55
 * @Description:
 */
@RequiredArgsConstructor
@Service
public class ThirdPlatFactory {

    private final List<PlatHandler> handlers;

    public PlatResult dispatch(String oriUrl) {
        if (oriUrl == null || oriUrl.isEmpty()) {
            return unknownResult(oriUrl);
        }

        for (PlatHandler handler : handlers) {
            if (handler instanceof PlatHandlerOthers) {
                continue;
            }
            if (handler.supports(oriUrl)) {
                return handler.getResult(oriUrl);
            }
        }

        return getDefaultHandler().getResult(oriUrl);
    }

    private PlatHandler getDefaultHandler() {
        return handlers.stream()
                .filter(h -> h instanceof PlatHandlerOthers)
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("PlatOthers not found"));
    }

    private PlatResult unknownResult(String url) {
        return PlatResult.builder()
                .url(url)
                .type(PlatformEnum.ALL.getIndex())
                .status(MonitorStatusEnum.MONITOR_CHECK_ERROR).build();
    }


//    public static void main(String[] args) {
//        String url2 =  "https://www.douyin.com/channel/300203?modal_id=7205960629331496252";
//        String url1 =  "https://www.douyin.com/user/MS4wLjABAAAAAK_4pAQhUYhHp7QT1s8TtJD8a1ywRy8PSgFO73H5iE0?modal_id=7214947741380742455";
//        String url = "https://v.douyin.com/JndpbAf/";
//        final PlatResult plat = getPlat(url);
//        System.out.println(plat);
//    }
}
