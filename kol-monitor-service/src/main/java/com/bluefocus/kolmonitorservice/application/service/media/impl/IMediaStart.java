package com.bluefocus.kolmonitorservice.application.service.media.impl;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.HandleTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleTaskListener;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.KafkaSender;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 2024/5/22 20:16
 * @description:
 */
@Service
public abstract class IMediaStart {

    @Resource
    RedisUtils redisUtils;
    @Resource
    FsRobotUtil fsRobotUtil;
    @Resource
    MediaObjectsDomainService mediaObjectsDomainService;
    @Resource
    MediaTaskHandleDomainService mediaTaskHandleDomainService;

    @Value("${robot.developer}")
    private String developer;
    public final static String DATASTORY_API_LIMITER = "DATASTORY_API_LIMITER_";
    public final static String DATASTORY_CRAWL_LIMITER = "DATASTORY_CRAWL_LIMITER";
    public final static String DATASTORY_ALGORITHM_LIMITER = "DATASTORY_ALGORITHM_LIMITER";

    protected boolean limit(String key) {
        switch (key) {
            case DATASTORY_CRAWL_LIMITER:
                return redisUtils.getRateLimiter(DATASTORY_CRAWL_LIMITER, 1, 7, RateIntervalUnit.SECONDS).tryAcquire();
            case DATASTORY_ALGORITHM_LIMITER:
                return redisUtils.getRateLimiter(DATASTORY_ALGORITHM_LIMITER, 500, 60, RateIntervalUnit.SECONDS).tryAcquire();
            default:
                return redisUtils.getRateLimiter(DATASTORY_API_LIMITER, 30, 5, RateIntervalUnit.SECONDS).tryAcquire();
        }
    }

    public DataStoryEntity getDataStoryFixedParam(MediaTaskHandle handle) {
        DataStoryEntity fixed = new DataStoryEntity();
        List<String> keywords = JSON.parseArray(handle.getKeyword(), String.class);
        StringBuilder keywordBuilder = new StringBuilder();
        for (int i = 0; i < keywords.size(); i++) {
            keywordBuilder.append(keywords.get(i));
            if (i < keywords.size() - 1) {
                keywordBuilder.append("|");
            }
        }
        fixed.setKeyword(keywordBuilder.toString());

        List<String> filterWords = JSON.parseArray(handle.getFilterword(), String.class);
        StringBuilder filterWordBuilder = new StringBuilder();
        for (int i = 0; i < filterWords.size(); i++) {
            filterWordBuilder.append(filterWords.get(i));
            if (i < filterWords.size() - 1) {
                filterWordBuilder.append("|");
            }
        }
        fixed.setFilterWord(filterWordBuilder.toString());
        fixed.setStartTime(handle.getStartTime());
        fixed.setEndTime(handle.getEndTime());
        return fixed;
    }

    protected void updateMediaObjects(MediaObjects obj) {
        mediaObjectsDomainService.updateById(obj);
    }

    protected void handleMsg(String robotUrl, String msg) {
        fsRobotUtil.sendRobotMsg(StringUtils.isBlank(robotUrl) ? developer : robotUrl, msg);
    }

    public abstract DataHandleState handle(MediaTaskHandle handle, Integer sourceCode);

    protected List<KeyValue<String, Long>> getTrendList(HashMap<String, Long> sourceTrend) {
        List<KeyValue<String, Long>> trendList = new ArrayList<>();
        sourceTrend.keySet().forEach(key -> {
            KeyValue<String, Long> keyValue = new KeyValue<>();
            if (key.length() > 8 && !key.contains("~")) {
                LocalDate localDate = Times.strToLocalDay(Times.toLocalDateTime(Long.parseLong(key)).toLocalDate().toString());
                keyValue.setDate(localDate.toString());
            } else {
                keyValue.setDate(key);
            }
            keyValue.setValue(sourceTrend.get(key));
            trendList.add(keyValue);
        });
        return trendList;
    }

    protected List<KeyValue<String, Long>> weekData(List<KeyValue<String, Long>> keyValueDays) {

        keyValueDays.sort(Comparator.comparing(KeyValue::getDate));
        HashMap<String, Long> keyValue = new HashMap<>();
        LongAdder weekValue = new LongAdder();
        int size = keyValueDays.size();
        for (int i = 0; i < size; i++) {

            KeyValue<String, Long> keyValueDay = keyValueDays.get(i);
            String date = keyValueDay.getDate();
            LocalDate localDate;
            if (date.length() > 8) {
                localDate = Times.toLocalDateTime(Long.parseLong(date)).toLocalDate();
            } else {
                localDate = Times.strToLocalDay(date);
            }

            DayOfWeek dayOfWeek = localDate.getDayOfWeek();
            if (i == 0 || dayOfWeek.compareTo(DayOfWeek.MONDAY) == 0) {
                String dateKey = localDate + "~" + localDate.plusDays(7 - dayOfWeek.getValue());
                weekValue.add(keyValueDay.getValue());
                if (null == keyValue.get(dateKey)) {
                    keyValue.put(dateKey, weekValue.longValue());
                } else {
                    keyValue.replace(dateKey, weekValue.longValue());
                }
            }

            if (dayOfWeek.compareTo(DayOfWeek.SUNDAY) == 0) {
                weekValue = new LongAdder();
            }
        }
        return keyValue.keySet().stream().map(k -> {
            KeyValue<String, Long> kv = new KeyValue<>();
            kv.setDate(k);
            kv.setValue(keyValue.get(k));
            return kv;
        }).collect(Collectors.toList());
    }

    protected List<KeyValue<String, Long>> monthData(List<KeyValue<String, Long>> keyValueDays) {
        HashMap<String, Long> monthMap = new HashMap<>(16);
        for (KeyValue<String, Long> keyValueDay : keyValueDays) {
            String date = keyValueDay.getDate();
            LocalDate localDate;
            if (date.length() > 8) {
                localDate = Times.toLocalDateTime(Long.parseLong(date)).toLocalDate();
            } else {
                localDate = Times.strToLocalDay(date);
            }
            String monthKey = localDate.getYear() + "-" + localDate.getMonthValue();
            if (monthMap.containsKey(monthKey)) {
                monthMap.replace(monthKey, monthMap.get(monthKey) + keyValueDay.getValue());
            } else {
                monthMap.put(monthKey, keyValueDay.getValue());
            }
        }
        return getTrendList(monthMap);
    }


    public List<KeyValue<String, Long>> kvJsonConvert(String kv) {
        if (null == kv) {
            return new ArrayList<>();
        }
        return new Gson().fromJson(kv, new TypeToken<List<KeyValue<String, Long>>>() {
        }.getType());
    }

    protected List<KeyValue<Long, Long>> wordMap2KvLabel(Map<String, Long> wordMap) {
        ArrayList<KeyValue<Long, Long>> keyValues = new ArrayList<>();
        List<Map.Entry<String, Long>> mapList = wordMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).collect(Collectors.toList());
        int count = getWordArtCount(mapList.size());
        int i = 1;
        while (i <= count) {
            Map.Entry<String, Long> entry = mapList.get(mapList.size() - i);
            KeyValue<Long, Long> kv = new KeyValue<>();
            kv.setLabel(entry.getKey());
            kv.setValue(entry.getValue());
            kv.setEnable(Boolean.TRUE);
            keyValues.add(kv);
            i++;
        }
        return keyValues;
    }

    protected int getWordArtCount(Integer size) {
        return Math.min(75, size);
    }


    @Resource
    private KafkaSender<Object> kafkaSender;

    protected void sendMessage(String rollBackTopic, String sucTopic, Boolean status, MediaMsgBody message) {
        if (status) {
            handleMq(message, sucTopic);
        } else {
            handleMq(message, rollBackTopic);
        }
    }

    public void handleMq(MediaMsgBody mediaMsgBody, String topic) {
        kafkaSender.sendByKey(topic, String.valueOf(mediaMsgBody.getMediaTaskId()), mediaMsgBody);
    }

    protected List<KeyValue<String, Long>> getDefaultTrendList(Long startTime, Long endTime) {
        LocalDate startDay = Times.toLocalDateTime(startTime).toLocalDate();
        LocalDate endDay = Times.toLocalDateTime(endTime).toLocalDate();
        List<KeyValue<String, Long>> trendList = new ArrayList<>();

        while (startDay.isBefore(endDay)) {
            KeyValue<String, Long> keyValue = new KeyValue<>();
            String date = startDay.toString();
            if (date.contains("-")) {
                date = date.replace("-", "");
            }
            keyValue.setDate(date);
            keyValue.setValue(0L);
            trendList.add(keyValue);
            startDay = startDay.plusDays(1L);
        }
        return trendList;
    }

    public void objFail(MediaTaskHandle handle, MediaObjects obj, String apiFailDesc, String errDesc) {
        handle.setStatus(FinishStatusEnum.FAIL.getCode());
        handle.setApiErrMsg(apiFailDesc);
        handle.setErrMsg(String.format(BusinessException.KEY_SENSITIVE_CONTENT, errDesc));
        obj.setStatus(handle.getType().equals(HandleTypeEnum.CREAT.getType()) ? MediaEditStatusEnum.FAIL.getCode() : MediaEditStatusEnum.EDIT_FAIL.getCode());
        mediaTaskHandleDomainService.updateById(handle);
        mediaObjectsDomainService.updateById(obj);
        MediaMsgBody mediaMsgBody = new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), this.getClass().getSimpleName(), handle.getId());
        handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
    }
}
