package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.datastory.DataStoryDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTextResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTrend;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaNoteDayDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTrendDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0019 2024/6/19 15:40
 * @description:
 */
@Slf4j
@Order(9)
@Service
@RequiredArgsConstructor
public class MediaNoteDayStart extends IMediaStart {

    private final DataStoryDomainService dataStoryService;
    private final MediaTrendDomainService mediaTrendDomainService;
    private final MediaNoteDayDomainService mediaNoteDayDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final RedisUtils redisUtils;
    @Value("${robot.url}")
    private String robotUrl;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {
        DataHandleState dataHandleState = new DataHandleState();
        MediaMsgBody msgBody = new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), this.getClass().getSimpleName(), handle.getId());

        List<MediaTrend> verifyMediaTrendList = verifyMediaTrend(msgBody);
        if (CollectionUtil.isEmpty(verifyMediaTrendList)) {
            log.info("MediaNoteDayStart分析已经结束，handle={}", msgBody.getHandId());
            dataHandleState.setAll(true);
            return dataHandleState;
        }
        AtomicInteger fail = new AtomicInteger(0);
        MediaTrend allMediaTrend = null;
        for (MediaTrend mediaTrend : verifyMediaTrendList) {
            if (mediaTrend.getSourceCode().equals(ExtraConditionEnum.ALL.getCode())) {
                allMediaTrend = mediaTrend;
                break;
            }
        }
        List<MediaTrend> platMediaTrendList = verifyMediaTrendList.stream()
                .filter(m -> !m.getSourceCode().equals(ExtraConditionEnum.ALL.getCode())).collect(Collectors.toList());
        Map<Integer, String> platHotDayMap = platMediaTrendList.stream().collect(Collectors.toMap(MediaTrend::getSourceCode, MediaTrend::getHotDay));
        // 处理单平台
        if (platMediaTrendList.size() > 0) {
            DataStoryEntity req = getDataStoryFixedParam(handle);

            platMediaTrendList.forEach(mediaTrend -> batchHandNoteDay(mediaTrend, req, fail));
        } else {
            try {
                Thread.sleep(3000);
                fail.getAndIncrement();
            } catch (InterruptedException e) {
                log.warn("MediaNoteDayStart睡眠等待失败,handleId={}", handle.getId());
            }
        }

        // plat some day not in all
        if (allMediaTrend != null && allMediaTrend.getHotDay() != null && CollectionUtil.isNotEmpty(platHotDayMap)) {
            List<String> hotDayList = JSON.parseArray(allMediaTrend.getHotDay(), String.class);

            HashMap<String, List<Integer>> platNotHotDayMap = new HashMap<>(8);
            hotDayList.forEach(hotDay -> {
                ArrayList<Integer> platList = new ArrayList<>();
                for (Integer plat : platHotDayMap.keySet()) {
                    List<String> platHotDayList = JSON.parseArray(platHotDayMap.get(plat), String.class);
                    if (!platHotDayList.contains(hotDay)) {
                        platList.add(plat);
                    }
                }
                if (platList.size() > 0) {
                    platNotHotDayMap.put(hotDay, platList);
                }
            });

            if (platNotHotDayMap.size() > 0) {
                DataStoryEntity req = getDataStoryFixedParam(handle);
                for (String hotDay : hotDayList) {
                    List<Integer> notDayPlat = platNotHotDayMap.get(hotDay);
                    if (CollectionUtil.isEmpty(notDayPlat)) {
                        continue;
                    }
                    MediaTrend finalAllMediaTrend = allMediaTrend;
                    notDayPlat.forEach(plat -> singleHotNoteDay(finalAllMediaTrend, req, fail, hotDay, plat));
                }
            }
        }

        MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(msgBody.getHandId());
        mediaTaskHandleDomainService.lambdaUpdate().set(MediaTaskHandle::getApiDayRetry, taskHandle.getApiDayRetry() + 1)
                .eq(MediaTaskHandle::getId, taskHandle.getId()).update();

        dataHandleState.setAll(fail.get() == 0);
        return dataHandleState;
    }

    private void batchHandNoteDay(MediaTrend mediaTrend, DataStoryEntity req, AtomicInteger fail) {
        mediaTrend.setHotDayTimes(mediaTrend.getHotDayTimes() + 1);
        if (mediaTrend.getAllVolume() == 0L || mediaTrend.getAlgStatus().equals(FinishStatusEnum.FINISH.getCode())) {
            mediaTrend.setIsAlgorithmSuc(FinishStatusEnum.FINISH.getCode());
            mediaTrendDomainService.updateById(mediaTrend);
            return;
        }
        mediaTrendDomainService.updateById(mediaTrend);
        List<String> hotDayList = JSON.parseArray(mediaTrend.getHotDay(), String.class);
        for (String hotDay : hotDayList) {
            singleHotNoteDay(mediaTrend, req, fail, hotDay, null);
        }
    }

    public void singleHotNoteDay(MediaTrend mediaTrend, DataStoryEntity req, AtomicInteger fail, String hotDay, Integer sourceCode) {
        if (mediaTrend.getAllVolume() == 0L || mediaTrend.getAlgStatus().equals(FinishStatusEnum.FINISH.getCode())) {
            return;
        }
        Integer finalSourceCode = null == sourceCode ? mediaTrend.getSourceCode() : sourceCode;
        String source = ExtraConditionEnum.codeOf(finalSourceCode);
        req.setSource(source);
        // 二次校验
        if (getHotNoteCountByHandle(mediaTrend.getHandleId(), finalSourceCode, hotDay) > 0) {
            log.info("当前处理单元[{}]平台[{}]日期[{}]热点笔记已存在,直接跳过", mediaTrend.getHandleId(), finalSourceCode, hotDay);
            return;
        }
        if (!limit(this.getClass().getSimpleName())) {
            try {
                Thread.sleep(1000);
                log.warn("当前处理单元[{}]平台[{}]日期[{}]热点日笔记接口限流,直接跳过", mediaTrend.getHandleId(), finalSourceCode, hotDay);
            } catch (InterruptedException e) {
                log.info("MediaNoteDayStart睡眠等待失败,处理单元{}，平台{}", mediaTrend.getHandleId(), finalSourceCode);
            }
            fail.getAndIncrement();
            return;
        }
        LocalDate startDat = Times.strToLocalDay(hotDay);
        req.setStartTime(Times.toEpochMilli(startDat.atTime(LocalTime.MIN)));
        req.setEndTime(Times.toEpochMilli(startDat.atTime(LocalTime.MAX)));
        req.setSource(source);

        DataStoryTextResponse.ResultDTO textList = retryReq(req, 3, mediaTrend.getHandleId());
        if (null == textList) {
            fsRobotUtil.sendRobotMsg(robotUrl, String.format("请求数说原帖日接口异常: 处理单元[%s]平台[%s]日期[%s] 热点日笔记接口限流", mediaTrend.getHandleId(), finalSourceCode, hotDay));
            fail.getAndIncrement();
            return;
        }
        mediaNoteDayDomainService.saveNotes(textList, mediaTrend.getMediaObjectsId(), mediaTrend.getHandleId(), finalSourceCode, hotDay);
        log.info("MediaNoteDayStart插入热点日笔记成功,当前handle[{}]平台[{}]日期[{}],笔记数量{}", mediaTrend.getHandleId()
                , finalSourceCode, hotDay, textList.getTotal().size());
    }

    private int getHotNoteCountByHandle(Long handleId, Integer sourceCode, String hotDay) {
        return mediaNoteDayDomainService.findCountByHandle(handleId, sourceCode, hotDay);
    }

    public List<MediaTrend> verifyMediaTrend(MediaMsgBody msgBody) {
        MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(msgBody.getHandId());
        if (!taskHandle.getPause() || taskHandle.getStatus().equals(FinishStatusEnum.FAIL.getCode())) {
            return new ArrayList<>();
        }
        if (taskHandle.getApiDayRetry() > getRetryLimit()) {
            return new ArrayList<>();
        }

        List<MediaTrend> mediaTrendList = mediaTrendDomainService.findByHandleIds(Collections.singletonList(taskHandle.getId()));

        return mediaTrendList.stream()
                .filter(t -> {
                    if (!t.getIsAlgorithmSuc().equals(FinishStatusEnum.ANALY.getCode())) {
                        return false;
                    }
                    if (t.getAlgStatus().equals(FinishStatusEnum.FINISH.getCode())) {
                        return false;
                    }
                    if (t.getAllVolume() == 0L) {
                        return false;
                    }
                    if (!checkJson(t.getHotDay())) {
                        return false;
                    }
                    List<String> hotDayList = JSON.parseArray(t.getHotDay(), String.class);

                    int haveNoteDay = 0;
                    haveNoteDay += hotDayList.stream().mapToInt(d -> getHotNoteCountByHandle(taskHandle.getId(), t.getSourceCode(), d))
                            .filter(count -> count > 0).count();
                    if (haveNoteDay == hotDayList.size()) {
                        log.info("当前对象[{}]平台[{}]日期{}热点笔记已全部存在,直接跳过", t.getMediaObjectsId(), t.getSourceCode(), hotDayList);
                        return false;
                    }
                    if (t.getHotDayTimes() > getRetryLimit()) {
                        log.info("当前对象[{}],平台[{}]热点笔记已重试{}次,直接跳过", taskHandle.getMediaObjectsId(), t.getSourceCode(), t.getHotDayTimes());
                        return false;
                    }

                    return true;
                }).collect(Collectors.toList());
    }

    private boolean checkJson(String hotJson) {
        if (StringUtils.isEmpty(hotJson)) {
            return false;
        }
        if (!JSON.isValid(hotJson)) {
            return false;
        }
        if (!JSON.isValidArray(hotJson)) {
            return false;
        }
        if (JSON.parseArray(hotJson, String.class).size() == 0) {
            return false;
        }

        return true;
    }

    private DataStoryTextResponse.ResultDTO retryReq(DataStoryEntity req, int i, Long handleId) {
        DataStoryTextResponse text = null;
        try {
            i--;
            text = dataStoryService.getText(req);
            if (text.isSuccess()) {
                return text.getData();
            } else if (!text.isSuccess() && null != text.getMsg()) {
                log.error("数说原帖日响应异常,handle={}，第{}次，响应信息={}", handleId, 3 - i, text.getMsg());
            }
        } catch (Exception e) {
            log.error("请求数说原帖日接口报错，第{}次", 3 - i);
        }
        if (i <= 0) {
            String con = text != null ? text.getMsg() : req.getSource() + ":" + req.getKeyword();
            log.error(String.format("请求数说原帖日接口异常, handleId=%s，平台=%s, 响应内容：%s", handleId, req.getSource(), con));
            return null;
        } else {
            return retryReq(req, --i, handleId);
        }
    }

    public int getRetryLimit() {
        RBucket<Object> string = redisUtils.getString(RedisKeyComm.MEDIA_API_DAY_LIMIT_RETRY);
        if (!string.isExists()) {
            string.set(3);
        }
        return Integer.parseInt(string.get().toString());
    }

}
