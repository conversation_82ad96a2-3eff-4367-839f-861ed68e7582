package com.bluefocus.kolmonitorservice.application.service.media.impl;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.domain.datastory.DataStoryDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryInteractionTreadResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTrend;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTrendDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 2024/5/22 20:18
 * @description:
 */
@Slf4j
@Order(2)
@Service
@RequiredArgsConstructor
public class MediaInteractionStart extends IMediaStart {

    private final DataStoryDomainService dataStoryService;
    private final MediaTrendDomainService mediaTrendDomainService;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {
        DataHandleState dataHandleState = new DataHandleState();

        DataStoryEntity req = getDataStoryFixedParam(handle);

        MediaTrend mediaTrend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), sourceCode);
        if (null != mediaTrend.getInteractionTrendDay() && null != mediaTrend.getAllInteraction()) {
            log.info("api互动量趋势数据已存在,handle={},source={}，跳过", handle.getId(), sourceCode);
            dataHandleState.setInteraction(true);
            return dataHandleState;
        }

        String source = ExtraConditionEnum.codeOf(sourceCode);
        req.setSource(source);
        DataStoryInteractionTreadResponse.ResultDTO trend = retryReq(req, 3, handle.getId());
        if (trend == null) {
            log.error("api互动量趋势数据请求失败,handle={},source={}，跳过", handle.getId(), sourceCode);
            dataHandleState.setInteraction(false);

            MediaObjects obj = mediaObjectsDomainService.getById(handle.getId());
            objFail(handle, obj, BusinessException.KEY_OTHER, null);
            return dataHandleState;
        }
        HashMap<String, Long> sourceTrend = trend.getTotal().get(source.toLowerCase(Locale.ROOT));
        List<KeyValue<String, Long>> trendList = getTrendList(sourceTrend);
        weekAndMonth(mediaTrend, trendList);
        mediaTrendDomainService.saveOrUpdate(mediaTrend);

        dataHandleState.setInteraction(true);
        return dataHandleState;
    }

    protected void drawMediaInteractionTrend(HashMap<String, List<KeyValue<String, Long>>> trendMap, MediaTaskHandle handle) {
        List<MediaTrend> mediaTrendList = mediaTrendDomainService.findByHandleIds(Collections.singletonList(handle.getId()));
        Map<Integer, MediaTrend> mediaTrendPoMap = mediaTrendList.stream().collect(Collectors.toMap(MediaTrend::getSourceCode, mediaTrend -> mediaTrend));

        trendMap.keySet().forEach(code -> {
            MediaTrend mediaTrendPo = mediaTrendPoMap.get(Integer.valueOf(code));
            if (!mediaTrendPo.getIsAlgorithmSuc().equals(FinishStatusEnum.CRAWLER.getCode()) && mediaTrendPo.getAllInteraction() != null) {
                log.info("当前互动量趋势数据已经落库，直接跳过，handle={}", handle.getId());
                return;
            }

            mediaTrendPo.setSourceCode(Integer.valueOf(code));
            mediaTrendPo.setMediaObjectsId(handle.getMediaObjectsId());
            mediaTrendPo.setHandleId(handle.getId());
            mediaTrendPo.setIsAlgorithmSuc(FinishStatusEnum.ANALY.getCode());

            List<KeyValue<String, Long>> keyValues = trendMap.get(code);
            weekAndMonth(mediaTrendPo, keyValues);

            mediaTrendDomainService.saveOrUpdate(mediaTrendPo);
        });

    }

    private void weekAndMonth(MediaTrend mediaTrendPo, List<KeyValue<String, Long>> keyValues) {
        keyValues.sort(Comparator.comparing(KeyValue::getDate));
        mediaTrendPo.setInteractionTrendDay(JSON.toJSONString(keyValues));
        mediaTrendPo.setInteractionTrendWeek(JSON.toJSONString(weekData(keyValues)));
        mediaTrendPo.setInteractionTrendMonth(JSON.toJSONString(monthData(keyValues)));
        long sum = keyValues.stream().mapToLong(KeyValue::getValue).sum();
        mediaTrendPo.setAllInteraction(sum);
    }


    private DataStoryInteractionTreadResponse.ResultDTO retryReq(DataStoryEntity req, int i, Long id) {
        DataStoryInteractionTreadResponse interactionTread = null;
        try {
            i--;
            if (i < 0) {
                return null;
            }
            interactionTread = dataStoryService.getInteractionTread(req);
            if (interactionTread.isSuccess()) {
                return interactionTread.getData();
            } else {
                log.warn("请求数说互动量趋势接口响应错误，第{}次,handle={},关键词={},响应信息={}", 3 - i, id, req.getKeyword(), interactionTread.getMsg());
            }

        } catch (Exception e) {
            log.error("请求数说互动量趋势接口报错，第{}次,e={}", 3 - i, e);
        } finally {
            if ((null == interactionTread || !interactionTread.isSuccess()) && i == 0) {
                String con = interactionTread != null ? interactionTread.getMsg() : null;
                handleMsg(null, String.format("请求数说互动量趋势接口异常(共重试%s次),对象id=%s,关键词%s。响应内容：%s", 3 - i, id, req.getKeyword(), con));
                log.error("请求数说互动量趋势接口重试次数耗尽：{}", JSON.toJSONString(interactionTread));
            }
        }
        return retryReq(req, i, id);
    }

    public void defaultTrend(MediaTaskHandle handle, Integer sourceCode) {
        MediaTrend trend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), sourceCode);
        weekAndMonth(trend, getDefaultTrendList(handle.getStartTime(), handle.getEndTime()));
        mediaTrendDomainService.saveOrUpdate(trend);
    }
}
