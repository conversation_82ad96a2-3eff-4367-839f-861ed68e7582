package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.common.MediaExclude;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.HotConclusionComm;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleAnalyzeListener;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleTaskListener;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.algorithm.AlgorithmDomainService;
import com.bluefocus.kolmonitorservice.domain.algorithm.entity.HotAnalysisRespEntity;
import com.bluefocus.kolmonitorservice.domain.algorithm.request.AlgorithmHotAnalysisRequest;
import com.bluefocus.kolmonitorservice.domain.algorithm.response.AlgorithmHotAnalysisResponse;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.*;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaNoteDayDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTrendDomainService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: yjLiu
 * @date: 0007 2024/6/7 17:17
 * @description: https://bluefocus.feishu.cn/docx/TRmHdVzT1o3chSxRt2Oclekvnad
 */
@Slf4j
@Order(8)
@Service
@MediaExclude
@RequiredArgsConstructor
public class MediaAlgorithmStart extends IMediaStart {

    private final MediaTrendDomainService mediaTrendDomainService;
    private final AlgorithmDomainService algorithmDomainService;
    private final ThreadPoolExecutor algorithmThreadPoolExecutor;
    private final MediaNoteDayDomainService mediaNoteDayDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;

    private final static Logger logger = LoggerFactory.getLogger(MediaAlgorithmStart.class);

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {
        DataHandleState dataHandleState = new DataHandleState();
        MediaMsgBody msgBody = new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), this.getClass().getSimpleName() + ":generateAlgorithm", handle.getId());

        RLock handleLock = redisUtils.getLock(RedisKeyComm.LOCK_MEDIA_ANALYZE_ + msgBody.getHandId());
        try {
            // 防住同一线程再次获取公共锁 qps 10/s
            if (handleLock.isLocked() || !handleLock.tryLock(0, 10, TimeUnit.SECONDS)) {
                log.warn("当前处理对象被其它线程锁住,跳过,handle={}", msgBody.getHandId());
                return dataHandleState;
            }
            dataHandleState.setAll(generateAlgorithm(msgBody));
        } catch (Exception e) {
            log.warn("线程中断异常:{}", JSON.toJSONString(msgBody), e);
        } finally {
            try {
                if (handleLock.isLocked()) {
                    handleLock.unlock();
                }
            } catch (Exception e) {
                log.warn("竞争解锁异常:{}", JSON.toJSONString(msgBody));
            }

            if (dataHandleState.getAll()) {
                this.handleMq(msgBody, MediaHandleTaskListener.TOPIC);
            } else {
                this.handleMq(msgBody);
            }
        }
        return dataHandleState;
    }


    private List<MediaTrend> getTrendList(Long handId) {
        return mediaTrendDomainService.findByHandleIds(Collections.singletonList(handId));
    }

    public void checkAndHotAnalyze(MediaTaskHandle handle) {
        MediaMsgBody msgBody = new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), this.getClass().getSimpleName() + ":checkAndHotAnalyze", handle.getId());
        generateAlgorithm(msgBody);
    }

    public Boolean generateAlgorithm(MediaMsgBody msgBody) {
        MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(msgBody.getHandId());
        if (!taskHandle.getPause() || taskHandle.getStatus() >= FinishStatusEnum.FINISH.getCode()) {
            return true;
        }
        MediaObjects obj = mediaObjectsDomainService.getById(msgBody.getId());
        if (MediaEditStatusEnum.isFinish(obj.getStatus()) || MediaEditStatusEnum.isFail(obj.getStatus())) {
            return true;
        }
        if (checkAnalyzeDay(taskHandle)) {
            return true;
        }
        return executeAlgorithm(taskHandle);
    }

    public Boolean executeAlgorithm(MediaTaskHandle handle) {

        List<String> keyList = JSON.parseArray(handle.getKeyword(), String.class);
        List<MediaTrend> mediaTrendList = getTrendList(handle.getId());
        Set<Integer> codeList = mediaTrendList.stream().map(MediaTrend::getSourceCode).collect(Collectors.toSet());
        List<String> failList = new CopyOnWriteArrayList<>();
        ConcurrentHashMap<String, Set<String>> nonNoteMap = new ConcurrentHashMap<>();
        mediaTrendList.stream().sorted(Comparator.comparing(MediaTrend::getSourceCode))
                .filter(t -> extractedFilter(t, handle))
                .forEach(mediaTrend -> {
                    List<HotAnalysisRespEntity> existedAnalyList = new ArrayList<>();
                    String hotConclusions = mediaTrend.getHotConclusions();
                    if (StringUtils.isNotEmpty(hotConclusions)) {
                        existedAnalyList = JSON.parseArray(hotConclusions, HotAnalysisRespEntity.class);
                    }
                    List<String> existDate = existedAnalyList.stream().map(HotAnalysisRespEntity::getDate).collect(Collectors.toList());
                    List<String> dateList = JSON.parseArray(mediaTrend.getHotDay(), String.class);
                    Stream<AlgorithmHotAnalysisRequest> requestStream = dateList.stream()
                            .filter(e -> !existDate.contains(e))
                            .map(date -> buildRequest(mediaTrend, date, keyList, handle, codeList));

                    List<CompletableFuture<HotAnalysisRespEntity>> futureList = requestStream
                            .map(request -> CompletableFuture.supplyAsync(() -> getHotAnalysisRespEntity(request, handle, failList, nonNoteMap, mediaTrend)
                                    , algorithmThreadPoolExecutor)
                            ).collect(Collectors.toList());

                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
                    List<HotAnalysisRespEntity> hots = futureList.stream().map(c -> {
                        try {
                            return c.get();
                        } catch (Exception e) {
                            return new HotAnalysisRespEntity();
                        }
                    }).filter(e -> StringUtils.isNotEmpty(e.getDate())).collect(Collectors.toList());
                    logger.info("热点分析结果,任务id={},handleId={},source={},hots={}", handle.getTaskId(), handle.getId(), mediaTrend.getSourceCode(), JSON.toJSONString(hots));
                    existedAnalyList.addAll(hots);
                    existedAnalyList.removeIf(ObjectUtil::isEmpty);
                    mediaTrend.setHotConclusions(JSON.toJSONString(existedAnalyList));
                    mediaTrend.setUpdateTime(LocalDateTime.now());
                    mediaTrend.setAlgorithmTimes(mediaTrend.getAlgorithmTimes() + 1);
                    if (isHotDayAll(dateList, existedAnalyList)) {
                        mediaTrend.setIsAlgorithmSuc(FinishStatusEnum.FINISH.getCode());
                    } else {
                        if (getAlgorithmTimes() <= mediaTrend.getAlgorithmTimes()) {
                            mediaTrend.setIsAlgorithmSuc(FinishStatusEnum.FINISH.getCode());
                        }
                    }
                    mediaTrendDomainService.updateById(mediaTrend);
                });

        if (mediaTrendList.stream().anyMatch(t -> t.getIsAlgorithmSuc() != FinishStatusEnum.FINISH.getCode() && getAlgorithmTimes() <= t.getAlgorithmTimes())) {
            handleMsg(null, String.format("热点分析接口模块异常: 当前任务id=%d, 分handleId=%d 重试多次异常, 请人工介入", handle.getTaskId(), handle.getId()));
        }

        updateHandleAlgorithmTime(handle);

        if (CollectionUtil.isNotEmpty(nonNoteMap)) {
            handleMsg(null, String.format("热点分析接口模块异常: 当前任务id=%d,handleId=%d 日期=%s 原文为空 ", handle.getTaskId(), handle.getId(), JSON.toJSONString(nonNoteMap)));
        }
        return CollectionUtil.isEmpty(failList);
    }

    public boolean checkAnalyzeDay(MediaTaskHandle handle) {
        return (handle.getEndTime() - handle.getStartTime()) < getAlgorithmMinTimeMs();
    }

    public AlgorithmHotAnalysisRequest buildRequest(MediaTrend mediaTrend, String date, List<String> keyList, MediaTaskHandle handle, Set<Integer> codeList) {
        AlgorithmHotAnalysisRequest algorithmHotAnalysisRequest = new AlgorithmHotAnalysisRequest();
        algorithmHotAnalysisRequest.setMediaObjectsId(mediaTrend.getMediaObjectsId());
        algorithmHotAnalysisRequest.setDate(date);
        algorithmHotAnalysisRequest.setKeywords(keyList);
        String sourceName = ExtraConditionEnum.codeOf(mediaTrend.getSourceCode());
        algorithmHotAnalysisRequest.setSource(sourceName);
        if (sourceName.equals(ExtraConditionEnum.ALL.getKey())) {
            algorithmHotAnalysisRequest.setNotes(syncAllDataStoryNote(handle.getId(), codeList, date));
        } else {
            algorithmHotAnalysisRequest.setNotes(syncDataStoryNote(handle.getId(), mediaTrend.getSourceCode(), date));
        }
        return algorithmHotAnalysisRequest;
    }

    public HotAnalysisRespEntity getHotAnalysisRespEntity(AlgorithmHotAnalysisRequest request, MediaTaskHandle handle, List<String> failList
            , ConcurrentHashMap<String, Set<String>> nonNote, MediaTrend mediaTrend) {
        String sourceName = request.getSource();
        HotAnalysisRespEntity hotConclusion = new HotAnalysisRespEntity();

        hotConclusion.setDate(request.getDate());
        long algorithmNotes = getAlgorithmNotes();
        List<KeyValue<String, Long>> keyValues = kvJsonConvert(mediaTrend.getInteractionTrendDay());
        if (CollectionUtil.isNotEmpty(request.getNotes()) && request.getNotes().size() < algorithmNotes) {
            hotConclusion.setMessage(HotConclusionComm.VOLUME_TEN);
        } else if (CollectionUtil.isNotEmpty(request.getNotes()) && request.getNotes().size() >= algorithmNotes) {
            // 优化热点分析 状态400
            HotAnalysisRespEntity analysis = doAnalysis(request, handle);
            if (analysis == null) {
                hotConclusion.setMessage(HotConclusionComm.ERROR);
                failList.add(sourceName + "(" + JSON.toJSONString(request.getDate()) + ")");
            } else {
                hotConclusion = analysis;
            }
        } else if (CollectionUtil.isNotEmpty(keyValues)) {
            String key = null;
            Long value = null;
            for (KeyValue<String, Long> keyValue : keyValues) {
                if (keyValue.getDate().equals(request.getDate())) {
                    key = keyValue.getDate();
                    value = keyValue.getValue();
                    break;
                }
            }
            // 当天为0
            if (key != null && value != null && value == 0) {
                hotConclusion.setMessage(HotConclusionComm.VOLUME_TEN);
            } else {
                hotConclusion.setMessage(HotConclusionComm.VOLUME_FAIL);
            }
        } else {
            putNonNote(nonNote, request.getSource(), request.getDate());
            hotConclusion.setMessage(HotConclusionComm.VOLUME_FAIL);
        }

        return hotConclusion;
    }

    private void putNonNote(ConcurrentHashMap<String, Set<String>> nonNote, String sourceName, String errDate) {
        Set<String> value = nonNote.get(sourceName);
        if (CollectionUtil.isEmpty(value)) {
            value = new HashSet<>();
        }
        value.add(errDate);
        nonNote.put(sourceName, value);
    }

    public HotAnalysisRespEntity doAnalysis(AlgorithmHotAnalysisRequest req, MediaTaskHandle handle) {
        HotAnalysisRespEntity resp = null;
        for (int i = 0; i < getAlgorithmTimes(); i++) {
            try {
                int loop = 0;
                while (!limit(DATASTORY_ALGORITHM_LIMITER) && loop < 60) {
                    loop++;
                    try {
                        Thread.sleep(1000L);
                        log.info("热点分析限频模块等待,当前任务id={},处理单元id={},日期={},平台={}", handle.getTaskId(), handle.getId(), req.getDate(), req.getSource());
                    } catch (InterruptedException ignored) {
                    }
                }
                log.info("开始请求算法分析,handleId={},数据源={},日期={},笔记数={}", handle.getId(), req.getSource(), req.getDate(), req.getNotes().size());
                long start = System.currentTimeMillis();
                AlgorithmHotAnalysisResponse response = algorithmDomainService.hotAnalysis(req);
                if (response.isSuccess() && response.getCode().startsWith("2")) {
                    log.info("请求算法分析完成：handleId={},数据源={},日期={},笔记数={},耗时={}ms"
                            , handle.getId(), req.getSource(), req.getDate(), req.getNotes().size(), System.currentTimeMillis() - start);
                    resp = response.getData().getHotConclusion();
                    break;
                } else if (response.isSuccess() && response.getCode().startsWith("4")) {
                    log.warn("请求算法分析警告：handleId={},数据源={},日期={},笔记数={},耗时={}ms, code={}"
                            , handle.getId(), req.getSource(), req.getDate(), req.getNotes().size(), System.currentTimeMillis() - start, response.getCode());
                    break;
                } else {
                    log.error("请求算法热点分析接口失败,handleId={},数据源={},日期={},code={},当前次数={},响应内容：{}"
                            , handle.getId(), req.getSource(), req.getDate(), response.getCode(), i, JSON.toJSONString(response));
                }
            } catch (Exception e) {
                log.error("请求算法热点分析接口异常，handleId={},数据源={},日期={},e:", handle.getId(), req.getSource(), req.getDate(), e);
            }
        }
        return resp;
    }

    private Long getAlgorithmTimes() {
        RBucket<Object> limit = redisUtils.getString(RedisKeyComm.MEDIA_ALGORITHM_TIME_LIMIT);
        return getRedisLimit(limit, RedisKeyComm.DEFAULT_ALGORITHM_TIMES);
    }

    private Long getAlgorithmNotes() {
        RBucket<Object> limit = redisUtils.getString(RedisKeyComm.MEDIA_ALGORITHM_LIMIT_NOTES);
        return getRedisLimit(limit, RedisKeyComm.DEFAULT_ALGORITHM_NOTES);
    }

    private Long getAlgorithmMinTimeMs() {

        RBucket<Object> limit = redisUtils.getString(RedisKeyComm.MEDIA_ALGORITHM_LIMIT_MIN_TIME_MS);
        if (!limit.isExists()) {
            limit.set(RedisKeyComm.DEFAULT_ALGORITHM_MIN_TIME_MS);
        }
        return Long.parseLong(limit.get().toString());
    }

    private Long getRedisLimit(RBucket<Object> limit, String times) {
        if (!limit.isExists()) {
            limit.set(times);
        }
        return Long.parseLong(limit.get().toString());
    }

    /**
     * 判断是否需要生成AI分析
     *
     * @return boolean
     */
    private boolean extractedFilter(MediaTrend t, MediaTaskHandle handle) {
        try {

//            if (t.getAllVolume() <= 0L) {
//                return false;
//            }
            if (checkAnalyzeDay(handle)) {
                return false;
            }
            if (t.getHotDayTimes() > 10) {
                return false;
            }

            if (getAlgorithmTimes() <= t.getAlgorithmTimes()) {
                // 请求次数超过限制)
                return false;
            }
            if (!t.getIsAlgorithmSuc().equals(FinishStatusEnum.ANALY.getCode())) {
                // 不在分析中，没有数据
                return false;
            }
            List<String> dateList = new Gson().fromJson(t.getHotDay(), new TypeToken<List<String>>() {
            }.getType());
            if (CollectionUtil.isEmpty(dateList)) {
                return false;
            }

            List<HotAnalysisRespEntity> analyzeValues = new Gson().fromJson(t.getHotConclusions(), new TypeToken<List<HotAnalysisRespEntity>>() {
            }.getType());
            if (CollectionUtil.isEmpty(analyzeValues)) {
                return true;
            }

            if (isHotDayAll(dateList, analyzeValues)) {
                if (t.getIsAlgorithmSuc() == FinishStatusEnum.ANALY.getCode()) {
                    t.setIsAlgorithmSuc(FinishStatusEnum.FINISH.getCode());
                    mediaTrendDomainService.updateById(t);
                }
                return false;
            }

        } catch (Exception e) {
            logger.error("判断是否需要生成AI分析异常 请求对象：{}", JSON.toJSONString(t), e);
            return false;
        }
        return true;
    }

    public boolean checkAlgorithmSuccess(MediaTaskHandle handle) {
        List<MediaTrend> trendList = getTrendList(handle.getId());
        // 如果全部为false 则返回为true
        return trendList.stream().noneMatch(t -> extractedFilter(t, handle));
    }

    public void updateHandleAlgorithmTime(MediaTaskHandle handle) {
        if (!checkAlgorithmSuccess(handle)) {
            return;
        }
        MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(handle.getId());

        if (taskHandle.getAnalyTime() == null && !taskHandle.checkStatusFinish()) {
            taskHandle.setAnalyTime(Times.toEpochMilli(LocalDateTime.now()));
            mediaTaskHandleDomainService.updateById(taskHandle);
        }
    }

    public Boolean isHotDayAll(List<String> dateList, List<HotAnalysisRespEntity> hotAnalyList) {
        if (CollectionUtil.isEmpty(hotAnalyList) || CollectionUtil.isEmpty(dateList)) {
            return false;
        }

        Set<String> dateSet = new HashSet<>(dateList);

        // 移除hotAnalyList中不在dateList中的元素
        hotAnalyList.removeIf(hot -> StringUtils.isEmpty(hot.getDate()) || !dateSet.contains(hot.getDate()));
        if (CollectionUtil.isEmpty(hotAnalyList)) {
            return false;
        }
        List<String> collectDate = hotAnalyList.stream().map(HotAnalysisRespEntity::getDate).collect(Collectors.toList());
        if (collectDate.size() != dateSet.size()) {
            return false;
        }
        // 检查处理后的collect是否包含dateList的所有日期，无需转换为Set，直接比较
        return dateSet.containsAll(collectDate);
    }

    public List<MediaNoteDay> syncAllDataStoryNote(Long handId, Set<Integer> codeList, String date) {
        return mediaNoteDayDomainService.findHotDayNote(handId, date, null, 10).stream().map(this::buildNoteDay).collect(Collectors.toList());
    }

    public MediaNoteDay buildNoteDay(MediaNoteDay mediaNoteDay) {
        if (mediaNoteDay.getTitle() == null) {
            String title = mediaNoteDay.getContent();
            if (title != null && title.length() > 200) {
                title = title.substring(0, 200);
            }
            mediaNoteDay.setTitle(title);
        }
        return mediaNoteDay;
    }

    public List<MediaNoteDay> syncDataStoryNote(Long handId, Integer sourceCode, String date) {
        return mediaNoteDayDomainService.findHotDayNote(handId, date, sourceCode, 10).stream().map(this::buildNoteDay).collect(Collectors.toList());
    }

    public void handleMq(MediaMsgBody mediaMsgBody) {
        super.handleMq(mediaMsgBody, MediaHandleAnalyzeListener.TOPIC);
    }

}
