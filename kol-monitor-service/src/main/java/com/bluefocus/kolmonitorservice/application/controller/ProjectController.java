package com.bluefocus.kolmonitorservice.application.controller;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.paramvalid.ParamValidHandler;
import com.bluefocus.kolmonitorinterface.IProjectService;
import com.bluefocus.kolmonitorinterface.dto.res.ProjectResponse;
import com.bluefocus.kolmonitorservice.application.service.ProjectService;
import com.bluefocus.kolmonitorservice.application.valid.ProjectValidHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Descirption 项目管理
 * @date 2022/1/10 10:09 上午
 */
@RestController
@RequestMapping("/lark/projects")
@ParamValidHandler(ProjectValidHandler.class)
@RequiredArgsConstructor
public class ProjectController implements IProjectService {

    private final ProjectService projectService;

    @Override
    @GetMapping("/list")
    public ResponseBean<PageBean<ProjectResponse>> findProjectList(
            @RequestParam(value = "page", defaultValue = "1") Integer page
            , @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        return projectService.findProjectList(page, limit);
    }

    @Override
    @GetMapping("/{id}/remove")
    public BaseResponseBean deleteProject(@PathVariable Long id) {
        return projectService.deleteProject(id);
    }


}
