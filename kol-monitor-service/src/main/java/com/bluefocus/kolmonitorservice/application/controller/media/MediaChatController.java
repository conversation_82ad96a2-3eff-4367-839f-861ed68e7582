package com.bluefocus.kolmonitorservice.application.controller.media;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.paramvalid.ParamValidHandler;
import com.bluefocus.kolmonitorinterface.IChatService;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatRefreshReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatStopReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ConversationReq;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatHistoryResp;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatResp;
import com.bluefocus.kolmonitorservice.application.service.chat.MediaChatService;
import com.bluefocus.kolmonitorservice.application.valid.media.ChatValidHandler;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.util.ArrayList;

/**
 * @author: yjLiu
 * @date: 0017 2024/10/17 10:51
 * @description:
 */
@RequiredArgsConstructor
@RequestMapping("/lark/media/")
@RestController
@ParamValidHandler(ChatValidHandler.class)
public class MediaChatController implements IChatService {

    private final MediaChatService mediaChatService;
    private final UserCommon userCommon;

    @Override
    @GetMapping("chat/list")
    public ResponseBean<ChatHistoryResp> chatList(@RequestParam(value = "mediaObjectsId") Long mediaObjectsId) {
        return ResponseBean.create(mediaChatService.chatList(mediaObjectsId));
    }

    @Override
    @PostMapping(value = "chat/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ChatResp> chatStream(@RequestBody ChatReq chatReq) {
        return mediaChatService.chatStream(chatReq);
    }

    @Override
    @PostMapping("chat/completion")
    public ResponseBean<ChatResp> chatCompletion(@RequestBody ChatReq chatReq) {
        return ResponseBean.create(mediaChatService.chatCompletion(chatReq));
    }

    @Override
    @PostMapping("chat/clean")
    public BaseResponseBean clean(@RequestBody ConversationReq request) {
        mediaChatService.clean(request);
        return BaseResponseBean.SUCCESS;
    }

    @Override
    @PostMapping("chat/refresh")
    public ResponseBean<ChatResp> refreshChat(@RequestBody ChatRefreshReq request) {
        return ResponseBean.create(mediaChatService.refreshChat(request));
    }

    @Override
    @PostMapping("chat/stop")
    public BaseResponseBean stopChat(@RequestBody ChatStopReq request) {
        mediaChatService.stopChat(request);
        return BaseResponseBean.SUCCESS;
    }
}
