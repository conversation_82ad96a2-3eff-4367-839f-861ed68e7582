package com.bluefocus.kolmonitorservice.application.valid;

import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.kolmonitorinterface.IReportService;
import com.bluefocus.kolmonitorinterface.dto.res.ReportResponse;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/8 16:12
 */
public class ReportValidHandler implements IReportService {
    @Override
    public ResponseBean<ReportResponse> findProjectReport(Long projectId) {
        return null;
    }

    @Override
    public void exportReport(Long projectId) {
    }

    @Override
    public void exportReportForPPT(Long projectId) {

    }
}
