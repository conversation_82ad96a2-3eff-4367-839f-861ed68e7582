package com.bluefocus.kolmonitorservice.application.service.route;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.IRouteService;
import com.bluefocus.kolmonitorinterface.dto.route.req.RouteTaskReq;
import com.bluefocus.kolmonitorinterface.dto.route.req.RouteTokenReq;
import com.bluefocus.kolmonitorinterface.dto.route.resp.RouteTaskResp;
import com.bluefocus.kolmonitorinterface.dto.route.resp.RouteTokenResp;
import com.bluefocus.kolmonitorservice.application.service.route.impl.DataStoryPlatform;
import com.bluefocus.kolmonitorservice.application.service.route.impl.IPlatform;
import com.bluefocus.kolmonitorservice.application.service.route.impl.PlatformFactory;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.route.platform.comm.PlatResponse;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteBiz;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteBizToken;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteReqRecord;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteTask;
import com.bluefocus.kolmonitorservice.domain.route.service.RouteBizDomainService;
import com.bluefocus.kolmonitorservice.domain.route.service.RouteReqRecordDomainService;
import com.bluefocus.kolmonitorservice.domain.route.service.RouteTaskDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * @author: yjLiu
 * @date: 0031 2024/7/31 10:32
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RouteService implements IRouteService {

    private final RouteBizDomainService routeBizDomainService;
    private final RouteReqRecordDomainService routeReqRecordDomainService;
    private final RouteTaskDomainService routeTaskDomainService;
    private final DataStoryPlatform dataStoryPlatform;

    @Override
    public ResponseBean<RouteTokenResp> token(RouteTokenReq req) {
        RouteBiz biz = routeBizDomainService.getBiz(req.getBizId());
        Assert.isTrue(biz != null, "bizId不存在" + req.getBizId());

        RouteBizToken token = routeBizDomainService.getToken(req.getBizId());
        RouteTokenResp routeTokenResp = new RouteTokenResp();
        routeTokenResp.setToken(token.getToken());
        routeTokenResp.setExpireTime(Times.toEpochMilli(token.getExpireTime()));
        return ResponseBean.create(routeTokenResp);
    }

    @Override
    public ResponseBean<PageBean<RouteTaskResp>> routeTaskList(String bizId, Long startTime, Long endTime, int page, int limit) {
        Page<RouteTask> taskPage = routeTaskDomainService.findTaskPage(bizId, startTime, endTime, page, limit);
        PageBean<RouteTaskResp> routeTaskRespPageBean = new PageBean<>();
        routeTaskRespPageBean.setPage(page);
        routeTaskRespPageBean.setLimit(limit);
        routeTaskRespPageBean.setTotalCount((int) taskPage.getTotal());
        ArrayList<RouteTaskResp> routeTaskResps = new ArrayList<>();
        for (RouteTask record : taskPage.getRecords()) {
            RouteTaskResp routeTaskResp = new RouteTaskResp();
            routeTaskResp.setJobId(record.getJobId());
            routeTaskResp.setPlatform(record.getPlatform());
            routeTaskResp.setDataCount(record.getDataCount());
            routeTaskResp.setDataCost(record.getDataCost());
            routeTaskResp.setCreatTime(Times.toEpochMilli(record.getCreateTime()));
            routeTaskResps.add(routeTaskResp);
        }
        routeTaskRespPageBean.setItems(routeTaskResps);
        return ResponseBean.create(routeTaskRespPageBean);
    }

    @Override
    public ResponseBean<Object> routeTaskCreat(RouteTaskReq request) {
        RouteReqRecord routeRecord = saveReqRecord(request);
        IPlatform platform = PlatformFactory.getPlatform(request.getPlatform());
        PlatResponse platResponse = platform.creatTask(routeRecord, request.getData());
        Assert.notNull(platResponse, String.format("请求%s平台异常，请联系后台人员", request.getPlatform()));
        if (!platResponse.isSuccess() && platResponse.getMsg().contains("token已过期")) {
            dataStoryPlatform.sendRouteRobotMsg(String.format("token预警：[%s]平台,token已过期,请人工及时处理！", request.getPlatform()));
        }
        return ResponseBean.create(JSONObject.toJSON(platResponse));
    }

    private RouteReqRecord saveReqRecord(RouteTaskReq request) {
        RouteBizToken bizByToken = routeBizDomainService.getBizByToken(request.getToken());
        return routeReqRecordDomainService.saveRecord(request, bizByToken.getBizId());
    }

    @Override
    public ResponseBean<Object> routeTaskQuery(RouteTaskReq request) {
        RouteReqRecord routeRecord = saveReqRecord(request);
        PlatResponse platResponse = PlatformFactory.getPlatform(request.getPlatform())
                .queryTask(routeRecord, request.getData());
        if (!platResponse.isSuccess() && platResponse.getMsg().contains("token已过期")) {
            dataStoryPlatform.sendRouteRobotMsg(String.format("token预警：[%s]平台,token已过期,请人工及时处理！", request.getPlatform()));
        }
        return ResponseBean.create(JSONObject.toJSON(platResponse));
    }

    public boolean checkToken(String token) {
        if (StringUtils.isEmpty(token)) {
            return false;
        }
        return routeBizDomainService.checkToken(token);
    }
}
