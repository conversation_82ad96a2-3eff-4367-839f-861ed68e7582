package com.bluefocus.kolmonitorservice.application.service.datastory;

import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.datastory.DataStoryDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;


/**
 * @author: yjLiu
 * @date: 0031 2024/7/31 10:32
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataStoryService {

    private final DataStoryDomainService dataStoryDomainService;

    public DSJobStatusDetailResponse queryJobStatus(Long jobId, Integer page) {
        try {
            return dataStoryDomainService.queryJobStatus(jobId, page);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    public DSJobDetailResponse queryJobDetail(Long jobId) {
        try {
            return dataStoryDomainService.queryJobDetail(jobId);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    public DSJobRerunResponse jobRerun(Long id) {
        try {
            return dataStoryDomainService.jobRerun(id);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    public DSJobCountResponse queryJobCount(Long jobId, String startTime, String endTime) {
        try {
            return dataStoryDomainService.queryJobCount(jobId, startTime, endTime);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    public DSJobSearchResponse queryJobSearch(Long jobId, String startTime, String endTime, String scrollId) {

        try {
            return dataStoryDomainService.queryJobSearch(jobId, startTime, endTime, scrollId);
        } catch (ApiException e) {
            throw new RuntimeException(e);
        }
    }

    public boolean refreshLoginToken() {
        return dataStoryDomainService.refreshLoginToken();
    }

    public List<Long> getSubJobId(Long jobId, String activeDate) {
        try {
            DSJobStatusDetailResponse response = this.queryJobStatus(jobId, 1);
            DSJobStatusDetailResponse.ResultDTO data = response.getData();
            List<DSJobStatusDetailResponse.JobStatusDTO> jobStatus = data.getJobStatus();
            LocalDate localDate = Times.strToLocalDate(activeDate);
            return getSubJobIds(jobStatus, Times.toEpochMilli(localDate.atTime(LocalTime.MIN)));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private ArrayList<Long> getSubJobIds(List<DSJobStatusDetailResponse.JobStatusDTO> jobStatus, long activeDateLong) {
        ArrayList<Long> subJobIds = new ArrayList<>();
        for (DSJobStatusDetailResponse.JobStatusDTO e : jobStatus) {
            String startDate = Times.toLocalDateTime(e.getSchedStart() == null ? e.getStartTime() : e.getSchedStart()).toLocalDate().toString();
            String endDate = Times.toLocalDateTime(e.getSchedEnd() == null ? e.getEndTime() : e.getSchedEnd()).toLocalDate().toString();
            String activateDate = Times.toLocalDateTime(activeDateLong).toLocalDate().toString();
            if (startDate.equals(activateDate) && endDate.equals(activateDate)) {
                if (2 == e.getStatus()) {
                    subJobIds.add(e.getId());
                }
            }
        }
        return subJobIds;
    }

}
