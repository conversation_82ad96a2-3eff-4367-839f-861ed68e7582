package com.bluefocus.kolmonitorservice.application.controller.media;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.paramvalid.ParamValidHandler;
import com.bluefocus.kolmonitorinterface.IMediaService;
import com.bluefocus.kolmonitorinterface.dto.media.req.*;
import com.bluefocus.kolmonitorinterface.dto.media.resp.*;
import com.bluefocus.kolmonitorservice.application.service.media.MediaService;
import com.bluefocus.kolmonitorservice.application.valid.media.MediaValidHandler;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: yjLiu
 * @date: 0021 2024/5/21 20:24
 * @description:
 */
@RestController
@RequestMapping("/lark/media/")
@ParamValidHandler(MediaValidHandler.class)
public class MediaController implements IMediaService {

    @Resource
    MediaService mediaService;

    @Override
    @GetMapping("source/list")
    public ResponseBean<List<SourceResp>> sourceList() {
        return mediaService.sourceList();
    }

    @Override
    @PostMapping("task/save")
    public BaseResponseBean save(@RequestBody MediaTaskSaveReq request) {
        mediaService.save(request);
        return BaseResponseBean.SUCCESS;
    }

    @Override
    @GetMapping("task/list")
    public ResponseBean<MediaTaskPageBean<MediaTaskResp>> findMediaTaskPage(@RequestParam(value = "taskName", required = false) String taskName
            , @RequestParam(value = "startTime", required = false) Long startTime
            , @RequestParam(value = "endTime", required = false) Long endTime
            , @RequestParam(value = "page", defaultValue = "1") Integer page
            , @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        return mediaService.findMediaTaskPage(taskName, startTime, endTime, page, limit);
    }

    @Override
    @GetMapping("task/detail")
    public ResponseBean<MediaTaskDetailResp> findTaskDetail(@RequestParam(value = "id") Long id) {
        return mediaService.findTaskDetail(id);
    }

    @Override
    @GetMapping("task/detail/{mediaObjectsId}")
    public ResponseBean<MediaObjectsResp> findObjDetail(@PathVariable("mediaObjectsId") Long mediaObjectsId) {
        return mediaService.findObjDetail(mediaObjectsId);
    }

    @Override
    @GetMapping("note/list")
    public ResponseBean<PageBean<MediaNoteResp>> findMediaNotePage(@RequestParam(value = "mediaObjectsId") Long mediaObjectsId
            , @RequestParam(value = "sort", defaultValue = "interactionCnt") String sort
            , @RequestParam(value = "sortType", defaultValue = "1") Integer sortType
            , @RequestParam(value = "sourceCode", defaultValue = "1") Integer sourceCode
            , @RequestParam(value = "page", defaultValue = "1") Integer page
            , @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        return mediaService.findMediaNotePage(mediaObjectsId, sort, sortType, sourceCode, page, limit);
    }

    @Override
    @PostMapping("picture/upload")
    public ResponseBean<String> uploadPicture(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "mediaObjectsId") Long mediaObjectsId) {
        return mediaService.uploadPicture(file, mediaObjectsId);
    }

    @Override
    @GetMapping("picture/gen")
    public ResponseBean<List<String>> genPicture(@RequestParam(value = "content") String content, @RequestParam(value = "mediaObjectsId") Long mediaObjectsId) {
        return mediaService.genPicture(content, mediaObjectsId);
    }

    @Override
    @PostMapping("word-cloud/gen")
    public ResponseBean<WordCloudResp> genWordCloud(@RequestBody GenWordCloudReq req) {
        return mediaService.genWordCloud(req);
    }

    @Override
    @PostMapping("word-cloud/update")
    public ResponseBean<WordCloudImg> updateWordCloud(@RequestBody UpdateWordCloudReq req) {
        return mediaService.updateWordCloud(req);
    }

    @Override
    @GetMapping("hot/analy")
    public ResponseBean<TaskTrendResp> hotAnaly(@RequestParam(value = "mediaObjectsId") Long mediaObjectsId) {

        return mediaService.hotAnaly(mediaObjectsId);
    }

    @Override
    @PostMapping("task/edit")
    public BaseResponseBean edit(@RequestBody MediaObjectsReq request) {
        return mediaService.edit(request);
    }

    @Override
    @PostMapping("word-cloud/reset")
    public ResponseBean<WordCloudResp> resetWordCloud(@RequestBody ResetWordCloudReq req) {
        return mediaService.resetWordCloud(req);
    }

    @Override
    @GetMapping("word-cloud/get")
    public ResponseBean<WordCloudResp> getWordCloud(@RequestParam(value = "mediaObjectsId") Long mediaObjectsId) {
        return mediaService.getWordCloud(mediaObjectsId);
    }

    @Override
    @GetMapping("task/init")
    public void taskInit(Long mediaObjectsId) {
        mediaService.taskInit(mediaObjectsId);
    }

    @Override
    @GetMapping("/note/download")
    public void downloadNoteData(@RequestParam(value = "mediaObjectsId") Long mediaObjectsId) {
        mediaService.downloadNoteData(mediaObjectsId);
    }
}
