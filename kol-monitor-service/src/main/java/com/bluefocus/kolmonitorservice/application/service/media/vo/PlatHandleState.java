package com.bluefocus.kolmonitorservice.application.service.media.vo;

import com.bluefocus.kolmonitorservice.application.service.media.impl.DataHandleState;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: yjLiu
 * @date: 0002 2024/7/2 16:34
 * @description:
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class PlatHandleState extends DataHandleState {

    private Integer sourceCode;
}
