package com.bluefocus.kolmonitorservice.application.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.fastjson.JSON;
import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.basebean.exception.InvalidParameterException;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.dto.excel.ExcelTrendValue;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.req.EditArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleDetailResponse;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleUrlResp;
import com.bluefocus.kolmonitorinterface.dto.res.MonitorArticleTrendResponse;
import com.bluefocus.kolmonitorinterface.dto.res.PageArticleDetail;
import com.bluefocus.kolmonitorservice.application.service.plat.PlatResult;
import com.bluefocus.kolmonitorservice.application.service.plat.ThirdPlatFactory;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import com.bluefocus.kolmonitorservice.base.enums.ProjectStatusEnum;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.KolVideoCloudDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleDetail;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleUpload;
import com.bluefocus.kolmonitorservice.domain.article.entity.KolVideoCloud;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDomainService;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date: 2023/3/8 19:07
 * @Description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ArticleService {

    private final ArticleDomainService articleDomainService;
    private final ProjectDomainService projectDomainService;
    private final UserCommon userCommon;
    private final KolVideoCloudDomainService kolVideoCloudDomainService;
    private final ThirdPlatFactory thirdPlatFactory;

    @Transactional(rollbackFor = Exception.class)
    public ResponseBean<ArticleUrlResp> save(ArticleUrlRequest request) {
        String articleUrl = request.getArticleUrl();
        Long userId = userCommon.getUserId(true);
        PlatResult platResult = getUrlResult(request.getProjectId(), articleUrl, userId, null);
        Article article = storageData(platResult, request.getProjectId(), userId, null);
        return ResponseBean.create(getArticleUrlResp(article));
    }

    public Article storageData(PlatResult platResult, Long projectId, Long userId, Long id) {
        Article articled;
        if (id != null) {
            articled = articleDomainService.updateArticleUrl(id, platResult);
        } else {
            articled = articleDomainService.saveArticleUrl(projectId, platResult, userId);
        }

        if (articled.getProjectId() != null && MonitorStatusEnum.suc(platResult.getStatus().getCode())) {
            Project project = projectDomainService.getById(articled.getProjectId());
            projectPlatUpdate(project, PlatformEnum.codeOf(platResult.getType()));
        }
        return articled;
    }

    private PlatResult getUrlResult(Long projectId, String articleUrl, Long userId, Long articleId) {
        Article article;
        if (projectId != null) {
            article = articleDomainService.findArticleByProjectIdAndUrl(projectId, articleUrl);
        } else {
            article = articleDomainService.findArticleByUrlIdAndUrl(userId, articleUrl);
        }
        if (null != article && !article.getId().equals(articleId)) {
            return PlatResult.builder()
                    .status(MonitorStatusEnum.MONITOR_URL_REPEAT)
                    .type(article.getPlatType())
                    .url(article.getArticleUrl())
                    .longUrl(article.getArticleFormalUrl())
                    .build();
        }
        return thirdPlatFactory.dispatch(articleUrl);
    }

    private void projectPlatUpdate(Project project, String platName) {
        if (!project.getMonitorPlat().contains(platName)) {
            project.setMonitorPlat(project.getMonitorPlat() + "," + platName);
            project.setUpdateTime(LocalDateTime.now());
            projectDomainService.updatePlat(project);
        }
    }

    public BaseResponseBean saveBatch(Long projectId, MultipartFile file) {
        if (projectId != null) {
            Project project = projectDomainService.getById(projectId);
            Assert.isFalse(project.getStatus().equals(ProjectStatusEnum.MONITORED.getIndex()), "项目已完成，不能新增链接");
        }

        try (InputStream is = file.getInputStream()) {
            Long userId = userCommon.getUserId();
            CompletableFuture<Set<String>> future = CompletableFuture.supplyAsync(() -> {
                ArticleUploadListener listener = new ArticleUploadListener(articleDomainService, thirdPlatFactory, projectId, userId);
                ExcelReader build = EasyExcelFactory.read(is, ArticleUpload.class, listener).build();
                try {
                    build.readAll();
                } finally {
                    build.finish();
                }
                return listener.getErr();
            });

            Set<String> strings = future.get();
            if (CollectionUtil.isNotEmpty(strings)) {
                log.warn("用户{},上传失败笔记链接为：{}", userId, strings);
            }

        } catch (Exception e) {
            log.error("新媒体监测文件上传异常,err:", e);
            throw new InvalidParameterException("文件读取失败，请检查内容！");
        }

        return BaseResponseBean.SUCCESS;
    }

    public ResponseBean<ArticleUrlResp> update(EditArticleUrlRequest request) {
        Article article = articleDomainService.getById(request.getArticleId());
        Assert.notNull(article, "文章链接Id不存在！");
        checkEditStatus(article.getStatus());
        String articleUrl = request.getArticleUrl();
        Long userId = userCommon.getUserId(true);
        PlatResult platResult = getUrlResult(article.getProjectId(), articleUrl, userId, article.getId());
        article = storageData(platResult, article.getProjectId(), userId, article.getId());
        return ResponseBean.create(getArticleUrlResp(article));
    }

    private void checkEditStatus(String status) {
        Assert.isFalse(status.equals(MonitorStatusEnum.MONITORING.getCode())
                        || status.equals(MonitorStatusEnum.MONITOR_FINISH.getCode())
                        || status.equals(MonitorStatusEnum.MONITOR_END_ERROR.getCode())
                        || status.equals(MonitorStatusEnum.MONITOR_ING_ERROR.getCode())
                        || status.equals(MonitorStatusEnum.MONITOR_FINISH_ERROR.getCode())
                , "监测中链接请勿操作");
    }

    public BaseResponseBean delete(Long id) {
        Article article = articleDomainService.getById(id);
        checkEditStatus(article.getStatus());
        articleDomainService.delete(id);
        return BaseResponseBean.SUCCESS;
    }


    public ResponseBean<List<ArticleUrlResp>> findArticleList(Long projectId) {
        List<Article> articleList;
        if (projectId == null) {
            articleList = articleDomainService.findListByUser(userCommon.getUserId(), true);
        } else {
            articleList = articleDomainService.findUnArticleList(projectId, true);
        }
        return ResponseBean.create(articleBaseDtoConvert(articleList));
    }

    private List<ArticleUrlResp> articleBaseDtoConvert(List<Article> articleList) {
        return articleList.stream().map(this::getArticleUrlResp).collect(Collectors.toList());
    }

    private ArticleUrlResp getArticleUrlResp(Article article) {
        ArticleUrlResp urlResp = new ArticleUrlResp();
        urlResp.setId(article.getId());
        urlResp.setArticleUrl(article.getArticleUrl());
        urlResp.setPlatformName(PlatformEnum.codeOf(article.getPlatType()));
        urlResp.setStatus(article.getStatus());
        urlResp.setStatusDesc(MonitorStatusEnum.getDescByCode(article.getStatus()));
        return urlResp;
    }


    public ResponseBean<PageArticleDetail> findArticlePage(Long projectId, Integer platform) {
        List<Article> articleList = articleDomainService.listByPlatform(projectId, platform);

        List<KolVideoCloud> videoCloudList = new ArrayList<>();
        if (PlatformEnum.DY.getIndex().equals(platform)) {
            // 抖音数据处理
            videoCloudList = kolVideoCloudDomainService.findByVideoIds(articleList.stream()
                    .filter(a -> a.getPlatType().equals(PlatformEnum.DY.getIndex())).map(Article::getArticlePlatId).collect(Collectors.toSet()));
        }
        List<ArticleDetailResponse> responses = articleDetailDtoConvert(articleList, videoCloudList);
        PageArticleDetail pageArticleDetail = articleDomainService.findTotalData(projectId, platform);
        if (pageArticleDetail == null) {
            pageArticleDetail = new PageArticleDetail();
        }
        if (pageArticleDetail.getTotalInteractionNum() != null && pageArticleDetail.getTotalReadNum() != null && pageArticleDetail.getTotalReadNum() > 0) {
            pageArticleDetail.setTotalInteractionRate(new BigDecimal(pageArticleDetail.getTotalInteractionNum())
                    .divide(new BigDecimal(pageArticleDetail.getTotalReadNum()), 2, RoundingMode.HALF_UP));
        }

        pageArticleDetail.setPages(responses);
        return ResponseBean.create(pageArticleDetail);
    }

    private List<ArticleDetailResponse> articleDetailDtoConvert(List<Article> records, List<KolVideoCloud> videoCloudList) {
        Map<String, KolVideoCloud> videoCloudMap = videoCloudList.stream().collect(Collectors.toMap(KolVideoCloud::getVideoId, Function.identity()));
        return records.stream().map(article -> dtoConvert(article, videoCloudMap.get(article.getArticlePlatId()))).collect(Collectors.toList());
    }

    private ArticleDetailResponse dtoConvert(Article article, KolVideoCloud kolVideoCloud) {
        ArticleDetailResponse detailResponse = new ArticleDetailResponse();
        detailResponse.setId(article.getId());
        detailResponse.setKolName(article.getKolName());
        detailResponse.setArticleTitle(article.getArticleTitle());
        detailResponse.setArticleUrl(article.getArticleUrl());
        detailResponse.setStatus(article.getStatus());
        detailResponse.setStatusDesc(MonitorStatusEnum.getDescByCode(article.getStatus()));
        detailResponse.setPlatformName(PlatformEnum.codeOf(article.getPlatType()));
        detailResponse.setReadNum(article.getReadNum());
        detailResponse.setLikeNum(article.getLikeNum());
        detailResponse.setCollectionNum(article.getCollectionNum());
        detailResponse.setCommentNum(article.getCommentNum());
        detailResponse.setShareNum(article.getShareNum());
        detailResponse.setInteractionNum(article.getInteractionNum());
        if (ObjectUtil.isNotNull(article.getConsumeDate())) {
            detailResponse.setUpdateDate(Times.toEpochMilli(LocalDateTime.of(article.getConsumeDate(), LocalTime.MIN)));
        }
        if (kolVideoCloud != null && article.getArticlePlatId().equals(kolVideoCloud.getVideoId())) {
            kolVideoCloudDomainService.covert(detailResponse, kolVideoCloud);
        }
        detailResponse.setHeadUrl(article.getHeadUrl());
        detailResponse.setPostTime(article.getPostTime());
        detailResponse.setImagesCover(article.getImagesCover());
        detailResponse.setFansNum(article.getFansNum());
        detailResponse.setFollowNum(article.getFollowNum());
        detailResponse.setInteractionRate(article.getInteractionRate());
        detailResponse.setFinishRate(article.getFinishRate());
        detailResponse.setBulletNum(article.getBulletNum());
        detailResponse.setCoinNum(article.getCoinNum());
        return detailResponse;
    }

    public ResponseBean<MonitorArticleTrendResponse> findArticleTrend(Long id) {
        Article article = articleDomainService.getById(id);
        ArticleDetail articleDetail = articleDomainService.findDetailDataById(id);

        Project project = projectDomainService.getById(article.getProjectId());
        KolVideoCloud kolVideoCloud = null;
        if (article.getPlatType().equals(PlatformEnum.DY.getIndex())) {
            Set<String> hashSet = new HashSet<>();
            hashSet.add(article.getArticlePlatId());
            List<KolVideoCloud> byVideoIds = kolVideoCloudDomainService.findByVideoIds(hashSet);
            if (CollectionUtil.isNotEmpty(byVideoIds)) {
                kolVideoCloud = byVideoIds.get(0);
            }
        }
        ArticleDetailResponse detailResponse = dtoConvert(article, kolVideoCloud);
        MonitorArticleTrendResponse trendResponse = new MonitorArticleTrendResponse();
        detailResponse.setMonitorPeriod(project.getMonitorPeriod());
        trendResponse.setDetailResponse(detailResponse);

        if (ObjectUtil.isNotEmpty(articleDetail) && ObjectUtil.isNotEmpty(articleDetail.getTendObj())) {
            List<ExcelTrendValue> excelTrendValues = JSON.parseArray(articleDetail.getTendObj(), ExcelTrendValue.class);
            trendResponse.setTrends(excelTrendValues);
        }

        return ResponseBean.create(trendResponse);
    }
}
