package com.bluefocus.kolmonitorservice.application.service.route.impl;

import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.domain.route.platform.comm.PlatResponse;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteReqRecord;

/**
 * @author: yj<PERSON><PERSON>
 * @date: 0002 2024/8/2 17:45
 * @description:
 */
public interface IPlatform {
    String getPlatform();

    /**
     * 路由转发.
     *
     * @param routeRecord 路由记录.
     * @param data        路由数据.
     * @return 路由结果.
     */
    PlatResponse creatTask(RouteReqRecord routeRecord, JSONObject data);

    PlatResponse queryTask(RouteReqRecord routeRecord, JSONObject data);

}
