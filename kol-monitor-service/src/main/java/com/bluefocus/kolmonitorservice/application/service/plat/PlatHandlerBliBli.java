package com.bluefocus.kolmonitorservice.application.service.plat;

import com.bluefocus.kolmonitorservice.application.service.plat.match.IUrlPattern;
import com.bluefocus.kolmonitorservice.application.service.plat.match.MatchType;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @Date: 2023/3/7 16:43
 * @Description:
 */
@Service
@Slf4j
public class PlatHandlerBliBli extends PlatHandler<PlatHandlerBliBli.UrlPattern> {

    @Override
    public List<UrlPattern> getUrlPatterns() {
        return Arrays.asList(UrlPattern.values());
    }

    @Getter
    @RequiredArgsConstructor
    public enum UrlPattern implements IUrlPattern {
        /**
         * B站 URL 匹配规则枚举
         */
        SHORT_URL("https://b23.tv", MatchType.PREFIX, "B站短链"),
        MOBILE_VIDEO("https://m.bilibili.com", MatchType.PREFIX, "B站移动端视频页"),
        TWEET_PAGE("https://t.bilibili.com", MatchType.PREFIX, "B站动态/推文页"),
        LIVE_ROOM("live.bilibili.com", MatchType.CONTAINS, "B站直播间"),
        HOME_PAGE("https://www.bilibili.com", MatchType.PREFIX, "B站主页"),
        USER_SPACE("space.bilibili.com", MatchType.CONTAINS, "B站用户空间");

        private final String url;
        private final MatchType type;
        private final String description;

        @Override
        public boolean matches(String url) {
            return type.apply(url, this.url);
        }
    }


    @Override
    public PlatResult getResult(String oriUrl) {
        PlatResult platResult = new PlatResult();
        String realUrl = extractUrl(oriUrl, null, null);

        String longUrl = realUrl;
        if (realUrl.startsWith(UrlPattern.USER_SPACE.url)) {
            platResult.setStatus(MonitorStatusEnum.USER_PROFILE_ERROR);
        } else if (realUrl.startsWith(UrlPattern.SHORT_URL.url)) {
            longUrl = getLongUrlRetry(realUrl, PlatHandler.initial);
            platResult.setStatus(checkBliLongUrl(longUrl));
        } else {
            longUrl = normLongUrl(longUrl);
            platResult.setStatus(checkBliLongUrl(longUrl));
        }

        platResult.setType(PlatformEnum.BLI.getIndex());
        platResult.setUrl(realUrl);
        platResult.setLongUrl(longUrl);
        return platResult;
    }

    public MonitorStatusEnum checkBliLongUrl(String longUrl) {

        if (StringUtils.isEmpty(longUrl)) {
            return MonitorStatusEnum.MONITOR_CHECK_ERROR;
        }

        if (longUrl.startsWith(UrlPattern.TWEET_PAGE.getUrl())
                || longUrl.startsWith(UrlPattern.MOBILE_VIDEO.getUrl())
                || longUrl.contains(UrlPattern.LIVE_ROOM.getUrl())) {
            return MonitorStatusEnum.DYNAMIC_PROFILE_ERROR;
        }
        return checkLongUrl(longUrl) ? MonitorStatusEnum.PRE_MONITOR : MonitorStatusEnum.MONITOR_CHECK_ERROR;
    }

}
