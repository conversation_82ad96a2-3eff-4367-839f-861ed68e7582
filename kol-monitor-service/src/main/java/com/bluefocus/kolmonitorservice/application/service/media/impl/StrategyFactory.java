package com.bluefocus.kolmonitorservice.application.service.media.impl;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.common.EmailConstant;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleApiListener;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleCrawlerListener;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTask;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.bluefocus.usercenterinterface.dto.res.LarkUserRes;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0014 2024/6/14 15:45
 * @description:
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class StrategyFactory {

    private final MediaTaskDomainService mediaTaskDomainService;
    private final RedisUtils redisUtils;
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final ApiMediaService apiMediaService;
    private final CrawlerMediaService crawlerMediaService;
    private final UserCommon userCommon;

    @Value("${robot.url}")
    private String robotUrl;
    @Value("${robot.task}")
    private String robotTaskUrl;
    private final FsRobotUtil fsRobotUtil;


    public void handleMqTactics(List<MediaTaskHandle> handleList) {
        Integer taskCount = mediaTaskDomainService.findCountNewDay();
        String dayTaskCount = redisUtils.getString(RedisKeyComm.TASK_COUNT_KEY).get().toString();

        ArrayList<MediaMsgBody> mediaMsgBodyCrawlerList = new ArrayList<>();
        ArrayList<MediaMsgBody> mediaMsgBodyApiList = new ArrayList<>();
        handleList.forEach(handle -> {
            MediaMsgBody mediaMsgBody = new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), this.getClass().getSimpleName(), handle.getId());
            if (allocation(taskCount, dayTaskCount, handle.getStartTime(), handle.getEndTime())) {
                mediaMsgBodyCrawlerList.add(mediaMsgBody);
            } else {
                mediaMsgBodyApiList.add(mediaMsgBody);
            }
        });

        log.info("创建并分发任务，爬虫队列={},api队列={}", JSON.toJSONString(mediaMsgBodyCrawlerList), JSON.toJSONString(mediaMsgBodyApiList));
        this.asyncMq(mediaMsgBodyCrawlerList, mediaMsgBodyApiList);
    }

    public MediaTaskHandle asyncTaskHandle(Long objId, Long mediaTaskId, String type, FinishStatusEnum finishStatusEnum) {
        MediaTaskHandle mediaTaskHandle = new MediaTaskHandle();
        mediaTaskHandle.setTaskId(mediaTaskId);
        mediaTaskHandle.setMediaObjectsId(objId);
        mediaTaskHandle.setCreateTime(LocalDateTime.now());
        mediaTaskHandle.setStatus(finishStatusEnum.getCode());
        mediaTaskHandle.setType(type);
        mediaTaskHandleDomainService.save(mediaTaskHandle);
        return mediaTaskHandle;
    }

    public void asyncMq(List<MediaMsgBody> mediaMsgBodyCrawlerList, List<MediaMsgBody> mediaMsgBodyApiList) {
        if (mediaMsgBodyCrawlerList.size() > 0) {
            for (MediaMsgBody msgBody : mediaMsgBodyCrawlerList) {
                crawlerMediaService.handleMq(msgBody, MediaHandleCrawlerListener.TOPIC);
            }
        }
        if (mediaMsgBodyApiList.size() > 0) {
            for (MediaMsgBody mediaMsgBody : mediaMsgBodyApiList) {
                apiMediaService.handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
            }
        }
    }

    public void convertTaskState(MediaTask mediaTask) {
        if (mediaTask.getStatus() == FinishStatusEnum.FINISH.getCode() || mediaTask.getStatus() == FinishStatusEnum.FAIL.getCode()) {
            return;
        }

        List<MediaTaskHandle> mediaTaskHandleList = mediaTaskHandleDomainService.getRunningTaskHandleByTask(mediaTask.getId(), null);

        List<MediaObjects> mediaObjects = mediaObjectsDomainService.findByTaskId(mediaTask.getId());

        if (mediaTaskHandleList.stream().noneMatch(o -> FinishStatusEnum.CRAWLER.getCode() == o.getStatus() || FinishStatusEnum.ANALY.getCode() == o.getStatus())
                && mediaTaskHandleList.stream().anyMatch(o -> FinishStatusEnum.FAIL.getCode() == o.getStatus())) {
            mediaTask.setStatus(FinishStatusEnum.FAIL.getCode());
            mediaTask.setUpdateTime(LocalDateTime.now());
            mediaTaskDomainService.updateById(mediaTask);
            ArrayList<String> keys = new ArrayList<>();
            for (MediaTaskHandle taskHandle : mediaTaskHandleList) {
                if (taskHandle.getStatus().equals(FinishStatusEnum.FAIL.getCode())) {
                    keys.add(taskHandle.getKeyword());
                }
            }

            sendRobotMsg(robotUrl, String.format("任务整体失败：任务id=%s 任务名称=%s ,其中分析对象数量为%s, 失败对象关键词为:%s"
                    , mediaTask.getId(), mediaTask.getName(), mediaObjects.size(), JSON.toJSONString(keys)));
            return;
        }

        if (mediaTaskHandleList.stream().allMatch(e -> e.getStatus() == FinishStatusEnum.FINISH.getCode())) {
            mediaTask.setStatus(FinishStatusEnum.FINISH.getCode());
            mediaTask.setUpdateTime(LocalDateTime.now());
            mediaTaskDomainService.updateById(mediaTask);
            for (MediaObjects mediaObject : mediaObjects) {
                mediaObject.setStatus(MediaEditStatusEnum.FINISH.getCode());
                mediaObject.setFinishTime(LocalDateTime.now());
            }
            mediaObjectsDomainService.updateBatchById(mediaObjects);
            return;
        }

        if (mediaTaskHandleList.stream().allMatch(e -> e.getStatus() == FinishStatusEnum.ANALY.getCode())) {
            mediaTask.setStatus(FinishStatusEnum.ANALY.getCode());
            mediaTask.setUpdateTime(LocalDateTime.now());
            mediaTaskDomainService.updateById(mediaTask);
        }

    }

    public void sendRobotMsg(String robotUrl, String con) {
        fsRobotUtil.sendRobotMsg(robotUrl, con);
    }

    /**
     * 分配条件：
     * 任务数量 小于特定数 爬虫
     * 分析时间 范围大 api
     * 今日总爬虫次数，超过100次，走接口
     */
    public boolean allocation(Integer todayTaskCount, String dayTaskCount, Long startTime, Long endTime) {
        boolean scope = Times.toLocalDateTime(startTime).plusMonths(6L).compareTo(Times.toLocalDateTime(endTime)) > 0;

        if (dayCrawlerLimitCount() < todayCrawlerSum(LocalDate.now())) {
            return true;
        }

        return todayTaskCount < Integer.parseInt(dayTaskCount) && scope;
    }

    private int dayCrawlerLimitCount() {
        RBucket<Object> dayCrawlerCount = redisUtils.getString(RedisKeyComm.CRAWLER_DAY_LIMIT_COUNT);
        if (!dayCrawlerCount.isExists()) {
            dayCrawlerCount.set(100);
        }
        return Integer.parseInt(dayCrawlerCount.get().toString());
    }

    private int todayCrawlerSum(LocalDate day) {
        RMap<String, Object> todayCrawlerCount = redisUtils.getMap(RedisKeyComm.DATA_STORY_CRAWLER_REDIS_COUNT_ + day);
        return todayCrawlerCount.values().stream().mapToInt(t -> Integer.parseInt(t.toString())).sum();
    }


    /**
     * 通知飞书群
     */
    public void noticeFs(List<MediaTaskHandle> mediaTaskHandleList) {
        MediaTask task = mediaTaskDomainService.getById(mediaTaskHandleList.get(0).getTaskId());
        Map<Long, String> objMap = mediaObjectsDomainService.findByTaskId(task.getId()).stream().collect(Collectors.toMap(MediaObjects::getId, MediaObjects::getName));
        StringBuilder taskContent = new StringBuilder();
        taskContent.append(String.format(EmailConstant.FS_TASK_CONTENT, task.getName())).append("\n");
        for (MediaTaskHandle handle : mediaTaskHandleList) {
            String fsObjContent = EmailConstant.FS_OBJ_CONTENT;
            fsObjContent = fsObjContent.replace("{obj}", objMap.get(handle.getMediaObjectsId()));
            fsObjContent = fsObjContent.replace("{filterword}", handle.getFilterword());
            fsObjContent = fsObjContent.replace("{keyword}", handle.getKeyword());
            fsObjContent = fsObjContent.replace("{time}",
                    Times.toLocalDateTime(handle.getStartTime()).toLocalDate().toString().replace("-", "") + "-"
                            + Times.toLocalDateTime(handle.getEndTime()).toLocalDate().toString().replace("-", ""));
            List<String> sourceNames = Arrays.stream(handle.getSources().split(",")).map(k -> ExtraConditionEnum.nameOf(Integer.valueOf(k))).collect(Collectors.toList());
            fsObjContent = fsObjContent.replace("{source}", JSON.toJSONString(sourceNames));
            fsObjContent = fsObjContent.replace("{status}", FinishStatusEnum.descOf(handle.getStatus()));

            taskContent.append(fsObjContent);
        }
        LarkUserRes userDept = userCommon.getUserDept(task.getOperatorId());
        if (null != userDept) {
            String dept = "";
            if (StringUtils.isNotEmpty(userDept.getDeptName4())) {
                dept = userDept.getDeptName4();
            } else if (StringUtils.isNotEmpty(userDept.getDeptName3())) {
                dept = userDept.getDeptName3();
            } else if (StringUtils.isNotEmpty(userDept.getDeptName2())) {
                dept = userDept.getDeptName2();
            }
            taskContent.append(String.format(EmailConstant.FS_USER_CONTENT, userDept.getLarkName(), dept));
        } else {
            taskContent.append(String.format(EmailConstant.FS_USER_CONTENT, task.getOperatorId(), "未知"));
        }
        sendRobotMsg(robotTaskUrl, taskContent.toString());
    }
}
