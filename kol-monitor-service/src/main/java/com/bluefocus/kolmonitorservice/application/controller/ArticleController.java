package com.bluefocus.kolmonitorservice.application.controller;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.paramvalid.ParamValidHandler;
import com.bluefocus.kolmonitorinterface.IArticleService;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleMonitorRequest;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.req.EditArticleUrlRequest;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleUrlResp;
import com.bluefocus.kolmonitorinterface.dto.res.MonitorArticleTrendResponse;
import com.bluefocus.kolmonitorinterface.dto.res.PageArticleDetail;
import com.bluefocus.kolmonitorservice.application.service.ArticleService;
import com.bluefocus.kolmonitorservice.application.service.ProjectService;
import com.bluefocus.kolmonitorservice.application.valid.ArticleValidHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;


/**
 * 文章前端控制器
 *
 * <AUTHOR>
 * @since 2023-03-08 18:08:16
 */
@RestController
@RequestMapping("/lark/articles")
@ParamValidHandler(ArticleValidHandler.class)
@RequiredArgsConstructor
public class ArticleController implements IArticleService {

    private final ArticleService articleService;
    private final ProjectService projectService;

    @Override
    @PostMapping("/save")
    public ResponseBean<ArticleUrlResp> save(@RequestBody ArticleUrlRequest request) {
        return articleService.save(request);
    }

    @Override
    @PostMapping("/save-batch")
    public BaseResponseBean saveBatch(@RequestParam(value = "projectId", required = false) Long projectId
            , @RequestParam(value = "file") MultipartFile file) {
        return articleService.saveBatch(projectId, file);
    }

    @Override
    @PostMapping("/update")
    public ResponseBean<ArticleUrlResp> update(@RequestBody EditArticleUrlRequest request) {
        return articleService.update(request);
    }

    @Override
    @DeleteMapping("/delete/{id}")
    public BaseResponseBean delete(@PathVariable("id") Long id) {
        return articleService.delete(id);
    }

    @Override
    @PostMapping("/start")
    public BaseResponseBean startMonitor(@RequestBody ArticleMonitorRequest request) {
        return projectService.startMonitor(request);
    }

    @Override
    @GetMapping("/list")
    public ResponseBean<List<ArticleUrlResp>> findArticleList(@RequestParam(value = "projectId", required = false) Long projectId) {
        return articleService.findArticleList(projectId);
    }

    @Override
    @GetMapping("/pages")
    public ResponseBean<PageArticleDetail> findArticlePage(@RequestParam(value = "projectId") Long projectId
            , @RequestParam(value = "platform") Integer platform) {
        return articleService.findArticlePage(projectId, platform);
    }

    @Override
    @GetMapping("/trend/{id}")
    public ResponseBean<MonitorArticleTrendResponse> findArticleTrend(@PathVariable("id") Long id) {
        return articleService.findArticleTrend(id);
    }
}
