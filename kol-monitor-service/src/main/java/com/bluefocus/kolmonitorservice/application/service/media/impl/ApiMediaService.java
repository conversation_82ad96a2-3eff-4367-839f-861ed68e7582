package com.bluefocus.kolmonitorservice.application.service.media.impl;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.application.service.media.vo.PlatHandleState;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.HandleTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.util.KafkaSender;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0014 2024/6/14 15:30
 * @description: api调用次数
 * 单一对象 的
 * 单一平台：互动量 声量 情感 原帖 词云  5次 + 热点分析3次  8次
 * 4个平台 8*4=32次 10秒
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiMediaService {

    private final ThreadPoolExecutor mediaThreadPoolExecutor;
    private final MediaInteractionStart mediaInteractionStart;
    private final MediaVolumeStart mediaVolumeStart;
    private final MediaSentimentStart mediaSentimentStart;
    private final MediaNoteStart mediaNoteStart;
    private final MediaWordCouldStart mediaWordCouldStart;
    private final MediaAllDataStart mediaAllDataStart;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final KafkaSender kafkaSender;

    public void handleMq(MediaMsgBody mediaMsgBody, String topic) {
        kafkaSender.sendByKey(topic, String.valueOf(mediaMsgBody.getMediaTaskId()), mediaMsgBody);
    }

    public DataHandleState handle(MediaObjects obj, MediaTaskHandle handle) {
        long startTime = System.currentTimeMillis();
        DataHandleState dataHandleState = new DataHandleState();
        List<String> sourceCodeList = Arrays.stream(handle.getSources().split(",")).collect(Collectors.toList());

        List<CompletableFuture<PlatHandleState>> platHandleStateList = sourceCodeList.stream().map(sourceCode ->
                CompletableFuture.supplyAsync(() ->
                        handleSinglePlatData(Integer.valueOf(sourceCode), handle), mediaThreadPoolExecutor
                )
        ).collect(Collectors.toList());
        CompletableFuture.allOf(platHandleStateList.toArray(new CompletableFuture[0]));
        try {
            Map<Integer, PlatHandleState> platHandleStateMap = platHandleStateList.stream().map(c -> {
                try {
                    return c.get();
                } catch (InterruptedException | ExecutionException e) {
                    return null;
                }
            }).filter(Objects::nonNull).collect(Collectors.toMap(PlatHandleState::getSourceCode, Function.identity(), (p1, p2) -> p2));

            MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(handle.getId());
            if (taskHandle.getStatus().equals(FinishStatusEnum.FAIL.getCode())){
                return dataHandleState;
            }
            Collection<PlatHandleState> platHandleStateCollection = platHandleStateMap.values();
            if (platHandleStateCollection.stream().noneMatch(DataHandleState::getVolume)) {
                // 全部失败
                dataHandleState.setAll(false);
            } else {
                dataHandleState.setVolume(platHandleStateCollection.stream().allMatch(DataHandleState::getVolume));
            }

            dataHandleState.setCloudWord(platHandleStateCollection.stream().allMatch(DataHandleState::getCloudWord));
            dataHandleState.setInteraction(platHandleStateCollection.stream().allMatch(DataHandleState::getInteraction));
            dataHandleState.setYuanwen(platHandleStateCollection.stream().allMatch(DataHandleState::getYuanwen));
            dataHandleState.setYuanwenNum((int) platHandleStateCollection.stream().filter(DataHandleState::getYuanwen).count());

            if (platHandleStateCollection.stream().allMatch(p -> p.getVolume() && p.getVolumeNum().equals(0L))) {
                mediaSentimentStart.handleDefault(handle);
                dataHandleState.setSentiment(true);
            } else {
                for (Integer code : platHandleStateMap.keySet()) {
                    PlatHandleState state = platHandleStateMap.get(code);
                    if (state.getVolume() && state.getVolumeNum() > 0) {
                        // 有一个平台不为0，情感必有
                        mediaSentimentStart.handle(handle, code);
                        dataHandleState.setSentiment(true);
                        break;
                    }
                }
            }

            // 处理合并
            Thread.sleep(1000);
            DataHandleState andHandle = mediaAllDataStart.checkAndHandle(handle, dataHandleState);
            dataHandleState.setAll(andHandle.checkAllState());

            if (dataHandleState.getAll()) {
                obj.setStatus(handle.getType().equals(HandleTypeEnum.CREAT.getType()) ? MediaEditStatusEnum.ANALY.getCode() : MediaEditStatusEnum.EDIT_ANALY.getCode());
                obj.setFinishTime(LocalDateTime.now());
                log.info("api任务对象基本数据采集完成：taskId={},handle={}, 数据耗时={}ms, 各部分结果{}"
                        , handle.getTaskId(), handle.getId(), System.currentTimeMillis() - startTime, JSON.toJSONString(dataHandleState));
            } else {
                log.warn("api任务对象基本数据采集部分缺失：taskId={},handle={}, 数据耗时={}ms, 各部分结果{}"
                        , handle.getTaskId(), handle.getId(), System.currentTimeMillis() - startTime, JSON.toJSONString(dataHandleState));
            }
        } catch (Exception e) {
            log.error("并发处理数说API异常，异常:", e);
        } finally {
            mediaAllDataStart.updateMediaObjects(obj);
        }
        return dataHandleState;
    }

    public PlatHandleState handleSinglePlatData(Integer sourceCode, MediaTaskHandle handle) {
        PlatHandleState platState = new PlatHandleState();
        platState.setSourceCode(sourceCode);

        // 先声量
        DataHandleState volumeHandle = mediaVolumeStart.handle(handle, sourceCode);
        platState.setVolumeNum(volumeHandle.getVolumeNum());
        if (!volumeHandle.getVolume() && null == volumeHandle.getVolumeNum()) {
            return platState;
        }

        // 校验全部声量
        if (volumeHandle.getVolumeNum().equals(0L)) {
            platState.setVolume(true);
            mediaVolumeStart.defaultTrend(handle, sourceCode);
            mediaInteractionStart.defaultTrend(handle, sourceCode);
//            Long allPlatVolumeNum = mediaVolumeStart.getPlatAllVolumeNum(handle, sourceCode);
//            if (allPlatVolumeNum != null && allPlatVolumeNum == 0L) {
//                platState.setVolume(true);
//                mediaVolumeStart.defaultTrend(handle, sourceCode);
//                mediaInteractionStart.defaultTrend(handle, sourceCode);
//            } else {
//                // 声量失败，后续无须执行 去下一轮
//                platState.setVolume(false);
//                return platState;
//            }
        } else {
            platState.setVolume(volumeHandle.getVolume());
        }

        // 三个并发
        CompletableFuture<DataHandleState> interactionMediaObjects = CompletableFuture.supplyAsync(() -> {
            if (platState.getVolumeNum().equals(0L) && volumeHandle.getVolume()) {
                // 互动量趋势也为0
                DataHandleState dataHandleState = new DataHandleState();
                dataHandleState.setInteraction(true);
                return dataHandleState;
            } else {
                return mediaInteractionStart.handle(handle, sourceCode);
            }
        }, mediaThreadPoolExecutor);

        CompletableFuture<DataHandleState> mediaNote = CompletableFuture.supplyAsync(() -> {
            if (platState.getVolumeNum().equals(0L) && volumeHandle.getVolume()) {
                DataHandleState dataHandleState = new DataHandleState();
                dataHandleState.setYuanwen(true);
                return dataHandleState;
            } else {
                return mediaNoteStart.handle(handle, sourceCode);
            }

        }, mediaThreadPoolExecutor);

        CompletableFuture<DataHandleState> mediaWordCould = CompletableFuture.supplyAsync(() -> {
            if (platState.getVolumeNum().equals(0L) && volumeHandle.getVolume()) {
                // 词云也为0
                DataHandleState dataHandleState = new DataHandleState();
                dataHandleState.setCloudWord(true);
                mediaWordCouldStart.handleDefault(handle, sourceCode);
                return dataHandleState;
            } else {
                return mediaWordCouldStart.handle(handle, sourceCode);
            }

        }, mediaThreadPoolExecutor);

        CompletableFuture.allOf(interactionMediaObjects, mediaNote, mediaWordCould);

        try {
            DataHandleState interactionState = interactionMediaObjects.get();
            platState.setInteraction(interactionState.getInteraction());
            DataHandleState mediaNoteState = mediaNote.get();
            platState.setYuanwen(mediaNoteState.getYuanwen());
            DataHandleState wordCouldState = mediaWordCould.get();
            platState.setCloudWord(wordCouldState.getCloudWord());
        } catch (InterruptedException | ExecutionException e) {
            log.error("api线程并发异常");
        }
        return platState;
    }
}
