package com.bluefocus.kolmonitorservice.application.service;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.bluefocus.kolmonitorservice.application.service.plat.PlatResult;
import com.bluefocus.kolmonitorservice.application.service.plat.ThirdPlatFactory;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleUpload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/9 17:33
 */
@Slf4j
@RequiredArgsConstructor
public class ArticleUploadListener extends AnalysisEventListener<ArticleUpload> {

    private final ArticleDomainService articleDomainService;
    private final ThirdPlatFactory thirdPlatFactory;
    private final Long projectId;
    private final Long userId;

    private static final int BATCH_COUNT = 100;
    List<PlatResult> articleList = new ArrayList<>(128);
    Set<String> errSet = new HashSet<>();

    /**
     * 除去表头的数据
     * 这个每一条数据解析都会来调用
     */

    @Override
    public void invoke(ArticleUpload article, AnalysisContext analysisContext) {
        PlatResult platResult;
        try {
            String articleUrl = article.getArticleUrl().trim();
            platResult = thirdPlatFactory.dispatch(articleUrl);
        } catch (Exception e) {
            platResult = PlatResult.builder()
                    .url(article.getArticleUrl())
                    .type(PlatformEnum.ALL.getIndex())
                    .status(MonitorStatusEnum.MONITOR_CHECK_ERROR).build();
        }

        if (!MonitorStatusEnum.PRE_MONITOR.equals(platResult.getStatus())) {
            errSet.add(platResult.getUrl());
        }

        articleList.add(platResult);
        if (articleList.size() >= BATCH_COUNT) {
            saveData();
            articleList.clear();
        }

    }

    /**
     * 所有数据解析完成了 都会来调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        saveData();
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        int count = articleDomainService.saveBatchArticleUrl(projectId, userId, articleList);
        log.info("存储数据库成功！{}条数据", count);
    }

    /**
     * 解析表头的内容
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

        List<String> headList = new ArrayList<>();
        for (int i = 0; i < headMap.size(); i++) {
            headList.add(headMap.get(i));
        }
        List<String> titleList = Collections.singletonList("链接");
        if (!titleList.containsAll(headList)) {
            throw new RuntimeException("订单表头不正确!");
        }
    }

    public Set<String> getErr() {
        return errSet;
    }

}


