package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.WordArtEnum;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleTaskListener;
import com.bluefocus.kolmonitorservice.base.listener.media.MediaHandleWordCloudListener;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.*;
import com.bluefocus.kolmonitorservice.domain.media.service.*;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordCloudGenRequest;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0007 2024/6/7 17:33
 * @description:
 */
@Slf4j
@Order(7)
@Service
@RequiredArgsConstructor
public class MediaWordArtStart extends IMediaStart {

    private final static Logger logger = LoggerFactory.getLogger(MediaWordArtStart.class);

    private final MediaWordDomainService mediaWordDomainService;
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final GptPictureDomainService gptPictureDomainService;
    private final ThreadPoolExecutor wordArtThreadPoolExecutor;
    private final ThreadPoolTaskScheduler scheduledExecutorPool;
    private final MediaTrendDomainService mediaTrendDomainService;

    @Value("${robot.url}")
    private String robotUrl;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {
        MediaMsgBody msgBody = MediaMsgBody.getMediaMsgBody(handle, this.getClass().getSimpleName());
        RLock lock = null;
        DataHandleState dataHandleState = new DataHandleState();
        try {
            lock = redisUtils.getLock(RedisKeyComm.LOCK_MEDIA_WORDART_ + msgBody.getHandId());
            if (lock.isLocked() || lock.tryLock(1, TimeUnit.MINUTES)) {
                dataHandleState.setAll(genWordCloudPicture(msgBody));
            }
        } catch (InterruptedException e) {
            log.warn("解锁异常");
        } finally {
            try {
                if (lock != null && lock.isLocked()) {
                    lock.unlock();
                }
            } catch (Exception e) {
                log.warn("竞争解锁异常:{}", JSON.toJSONString(msgBody));
            }
            if (dataHandleState.getAll()) {
                this.handleMq(msgBody, MediaHandleTaskListener.TOPIC);
            } else {
                scheduledExecutorPool.schedule(() -> this.handleMq(msgBody), new Date(System.currentTimeMillis() + 3000 * 60));
            }
        }
        return null;
    }

    private Boolean genWordCloudPicture(MediaMsgBody msgBody) {
        //  每次取最新数据
        MediaObjects obj = mediaObjectsDomainService.getById(msgBody.getId());
        Long objId = obj.getId();
        String defaultImg = obj.getImg();
        MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(msgBody.getHandId());
        if (!taskHandle.getPause() || taskHandle.getStatus() >= FinishStatusEnum.FINISH.getCode()) {
            log.info("词云已经结束，跳过, handleId={}", taskHandle.getId());
            return true;
        }

        List<MediaWord> wordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(msgBody.getHandId()));
        if (CollectionUtil.isEmpty(wordList)) {
            MediaTrend allTrend = mediaTrendDomainService.findByHandleIdAndSource(taskHandle.getId(), ExtraConditionEnum.ALL.getCode());
            if (allTrend.getAllVolume() > 0L) {
                handleMsg(robotUrl, String.format("生成WordArt图片异常,任务id=%d, handleId=%d, 声量不为零时,词云为空异常 需要人工介入", taskHandle.getTaskId(), taskHandle.getId()));
            }
            return null;
        }

        List<CompletableFuture<Boolean>> completableFutureList = wordList.stream().filter(word -> {
            if (StringUtils.isNotEmpty(word.getGenImg())) {
                return false;
            }
            if ((ObjectUtil.isNotEmpty(word.getIsInit()) && word.getIsInit() >= FinishStatusEnum.FINISH.getCode())) {
                return false;
            }
            return checkWordCloudStatusByHandleId(word);
        }).map(word -> CompletableFuture.supplyAsync(() -> {
            boolean isSuccess = true;
            try {
                long start = System.currentTimeMillis();
                GptPicture gptPicture = getWordCloudImgReq(taskHandle, word, defaultImg);
                if (ObjectUtil.isNull(gptPicture)) {
                    log.error("生成词云图为null,当前任务={},当前对象={}，当前平台={}", obj.getMediaTaskId(), word.getMediaObjectsId(), word.getSourceCode());
                    isSuccess = false;
                } else {
                    log.info("wordart success,objId={},平台={}, 耗时：{}s", objId, word.getSourceCode(), System.currentTimeMillis() - start);
                    word.setGenImg(gptPicture.getImg());
                    word.setUpdateTime(LocalDateTime.now());
                    word.setIsInit(FinishStatusEnum.FINISH.getCode());
                    word.setArtStatus(WordArtEnum.ZERO.getCode());
                }
            } catch (Exception e) {
                log.error("生成词云图为null,当前任务={},当前对象={}，当前平台={}", obj.getMediaTaskId(), word.getMediaObjectsId(), word.getSourceCode(), e);
                isSuccess = false;
                word.setArtStatus(WordArtEnum.FOUR.getCode());
            }
            if (!isSuccess) {
                Integer errorTimes = word.getErrorTimes();
                word.setErrorTimes(errorTimes + 1);
            }
            mediaWordDomainService.updateById(word);
            return isSuccess;
        }, wordArtThreadPoolExecutor)).collect(Collectors.toList());

        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));
        List<Boolean> failList = completableFutureList.stream().map(c -> {
            try {
                return c.get();
            } catch (InterruptedException | ExecutionException e) {
                return Boolean.FALSE;
            }
        }).collect(Collectors.toList());
        failList.removeIf(v -> v);
        if (failList.size() > 0) {
//            handleMsg(failList, obj.getMediaTaskId(), objId);
            return false;
        }

        updateArtStatus(msgBody);

        return true;
    }

    public GptPicture getWordCloudImgReq(MediaTaskHandle taskHandle, MediaWord word, String defaultImg) {
        List<KeyValue<Long, Long>> keyValues = new Gson().fromJson(word.getAllWord(), new TypeToken<List<KeyValue<Long, Long>>>() {
        }.getType());

        Map<String, Long> wordMap = keyValues.stream().collect(Collectors.toMap(KeyValue::getLabel, KeyValue::getValue));
        if (CollectionUtil.isEmpty(wordMap)) {
            return null;
        }

        WordCloudGenRequest request = new WordCloudGenRequest();
        request.setData(wordMap);
        request.setUrl(defaultImg);

        return gptPictureDomainService.genWordCloudImgAndSave(request, taskHandle.getId(), taskHandle.getMediaObjectsId());
    }

    private boolean checkWordCloudStatusByHandleId(MediaWord word) {

        if (word.getErrorTimes() >= 3) {
            word.setUpdateTime(LocalDateTime.now());
            word.setIsInit(FinishStatusEnum.FINISH.getCode());
            word.setArtStatus(WordArtEnum.FOUR.getCode());
            mediaWordDomainService.updateById(word);
            handleMsg(null, String.format("生成WordCloud图片异常,处理id=%d, 分析对象id=%d,当前平台[%s],错误:%d 次 需要人工介入"
                    , word.getHandleId(), word.getHandleId(), word.getSourceCode(), word.getErrorTimes()));
            return false;
        }

        List<KeyValue> keyValues = JSON.parseArray(word.getAllWord(), KeyValue.class);

        if (CollectionUtil.isEmpty(keyValues)) {
            MediaTrend trend = mediaTrendDomainService.findByHandleIdAndSource(word.getHandleId(), word.getSourceCode());
            word.setUpdateTime(LocalDateTime.now());
            word.setErrorTimes(word.getErrorTimes() + 1);
            if (trend.getAllVolume() == 0L) {
                word.setArtStatus(WordArtEnum.ONE.getCode());
                word.setIsInit(FinishStatusEnum.FINISH.getCode());
            }
            mediaWordDomainService.updateById(word);
            return trend.getAllVolume() != 0L;
        }

        return Boolean.TRUE;
    }

    public boolean checkTaskWordArtIsInitByHandleIds(List<Long> handleIdList) {
        List<MediaWord> mediaWords = mediaWordDomainService.findByHandleIds(handleIdList);
        return mediaWords.stream().allMatch(e -> e.getIsInit() == FinishStatusEnum.FINISH.getCode()
                || e.getIsInit() == FinishStatusEnum.FAIL.getCode()
                || StringUtils.isNotEmpty(e.getGenImg()));
    }

    public void handleMq(MediaMsgBody mediaMsgBody) {
        super.handleMq(mediaMsgBody, MediaHandleWordCloudListener.TOPIC);
    }

    protected void handleMsg(List<Boolean> failList, Long mediaTaskId, Long id) {
        if (failList.size() > 0) {
            handleMsg(robotUrl, "生成wordart图片异常, " + "任务id=" + mediaTaskId + "分析对象id=" + id);
        }
    }

    public boolean updateArtStatus(MediaMsgBody msgBody) {
        if (!checkTaskWordArtIsInitByHandleIds(Collections.singletonList(msgBody.getHandId()))) {
            return false;
        }
        List<MediaTaskHandle> res;
        if (msgBody.getHandId() != null) {
            res = mediaTaskHandleDomainService.listByIds(Collections.singletonList(msgBody.getHandId()));
        } else {
            res = mediaTaskHandleDomainService.findByTaskIdAndMediaObjectsId(msgBody.getMediaTaskId(), msgBody.getId());
        }
        List<MediaTaskHandle> update = res.stream().filter(m -> !(m.checkStatusFinish() && m.getArtTime() != null)).map(mediaTaskHandle -> {
            MediaTaskHandle req = new MediaTaskHandle();
            req.setId(mediaTaskHandle.getId());
            req.setArtTime(Times.toEpochMilli(LocalDateTime.now()));
            return req;
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(update)) {
            boolean b = mediaTaskHandleDomainService.updateBatchById(update);
            logger.info("更新MediaTaskHandle:{}, 结果：{}", JSON.toJSONString(update), b);
        }
        return true;
    }
}
