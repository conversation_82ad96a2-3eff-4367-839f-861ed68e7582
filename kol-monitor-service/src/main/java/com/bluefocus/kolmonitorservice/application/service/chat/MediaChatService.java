package com.bluefocus.kolmonitorservice.application.service.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bluefocus.basebean.exception.InvalidParameterException;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatRefreshReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatStopReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ConversationReq;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatHistoryResp;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatResp;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAllDataStart;
import com.bluefocus.kolmonitorservice.base.enums.ChatMsgTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.ChatRoleEnum;
import com.bluefocus.kolmonitorservice.base.enums.ChatStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import com.bluefocus.kolmonitorservice.domain.chat.coze.CozeDomainService;
import com.bluefocus.kolmonitorservice.domain.chat.coze.dto.CozeChatDo;
import com.bluefocus.kolmonitorservice.domain.chat.coze.dto.CozeCompletedDo;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.ConversationResp;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.CozeChatResp;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.CozeChatStreamResp;
import com.bluefocus.kolmonitorservice.domain.chat.entity.ChatCommDo;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChat;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChatText;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaConversation;
import com.bluefocus.kolmonitorservice.domain.chat.service.MediaChatDomainService;
import com.bluefocus.kolmonitorservice.domain.chat.service.MediaConversationDomainService;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: yjLiu
 * @date: 0016 2024/10/16 11:13
 * @description:
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class MediaChatService {

    private final MediaChatDomainService mediaChatDomainService;
    private final MediaConversationDomainService mediaConversationDomainService;
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final UserCommon userCommon;
    private final CozeDomainService cozeDomainService;
    private final MediaAllDataStart mediaAllDataStart;

    public ChatHistoryResp chatList(Long mediaObjectsId) {

        MediaConversation currentConversation = mediaConversationDomainService.findCurrentConversation(mediaObjectsId, null);
        ChatHistoryResp chatHistoryResp = new ChatHistoryResp();
        if (null == currentConversation) {
            return chatHistoryResp;
        }
        chatHistoryResp.setConversationId(String.valueOf(currentConversation.getConversationId()));
        List<ChatCommDo> chatCommDoList = mediaChatDomainService.findCurrentChat(currentConversation.getConversationId());

        List<ChatResp> arrayList = new ArrayList<>();
        chatHistoryResp.setChatRespList(arrayList);
        for (ChatCommDo chatCommDo : chatCommDoList) {

            if (ChatRoleEnum.SYSTEM.getRole().equals(chatCommDo.getRole())
                    && ChatMsgTypeEnum.QUESTION.getType().equals(chatCommDo.getType())) {
                continue;
            }
            if (ChatStatusEnum.CHAT_CANCELED.getCode().equals(chatCommDo.getStatus())
                    && ChatRoleEnum.ASSISTANT.getRole().equals(chatCommDo.getRole())) {
                continue;
            }
            ChatResp chatResp = new ChatResp();
            chatResp.setMessageId(chatCommDo.getMessageId());
            chatResp.setChatId(String.valueOf(chatCommDo.getChatId()));
            chatResp.setConversationId(currentConversation.getConversationId().toString());
            chatResp.setContent(chatCommDo.getContent());
            chatResp.setRole(chatCommDo.getRole());
            chatResp.setStatus(chatCommDo.getStatus());
            if (null != chatCommDo.getChatTime()) {
                chatResp.setChatTime(Times.toEpochMilli(chatCommDo.getChatTime()));
            }

            arrayList.add(chatResp);
        }

        return chatHistoryResp;
    }

    public ChatResp refreshChat(ChatRefreshReq request) {
        MediaChatText chatText = refreshChatCheck(request.getConversationId(), request.getChatId());

        // 创建新对话
        Long conversationId = request.getConversationId();
        MediaChat mediaChat = chatCreate(conversationId, chatText.getContent(), chatText.getRole(), userCommon.getUserId(true));
        log.info("刷新对话-创建新对话:{}", request);
        mediaChatDomainService.clean(request.getChatId());
        log.info("刷新对话-失效对话:{}", request);
        return getCompletedResult(mediaChat.getChatId(), conversationId, mediaChat, userCommon.getUserId(true), ChatRoleEnum.ASSISTANT.getRole());
    }

    private MediaChatText refreshChatCheck(Long conversationId, Long chatId) {
        MediaChat chat;
        if (null == chatId) {
            chat = mediaChatDomainService.findChatByConversationId(conversationId);
        } else {
            chat = mediaChatDomainService.findChatById(chatId);
        }

        Assert.notNull(chat, "对话不存在");
        Assert.isFalse(ChatStatusEnum.CREATED.getCode().equals(chat.getStatus()), "进行中对话不可刷新");

        MediaChatText chatText = mediaChatDomainService.findChatTextQuestion(chatId);
        Assert.notNull(chatText, "对话内容不存在");
        return chatText;
    }

    public void clean(ConversationReq request) {
        mediaConversationDomainService.clean(null, request.getConversationId());
    }

    public void stopChat(ChatStopReq request) {
        // 失效原对话
        MediaChat chatByConversationId;
        if (request.getChatId() == null) {
            chatByConversationId = mediaChatDomainService.findChatByConversationId(request.getConversationId());
        } else {
            chatByConversationId = mediaChatDomainService.findChatById(request.getChatId());
        }
        if (chatByConversationId == null) {
            try {
                Thread.sleep(2000L);
            } catch (InterruptedException ignored) {
            }
            chatByConversationId = mediaChatDomainService.findChatByConversationId(request.getConversationId());
        }
        if (null == chatByConversationId) {
            log.error("对话内容不存在,不能停止,请求内容：{}", JSON.toJSONString(request));
            throw new BusinessException("对话内容不存在,不能停止");
        }
        Assert.isFalse(ChatStatusEnum.COMPLETED.getCode().equals(chatByConversationId.getStatus()), "对话内容已经完成,不能停止");
        Assert.isFalse(ChatStatusEnum.CHAT_CANCELED.getCode().equals(chatByConversationId.getStatus()), "对话内容已经取消,不能再次取消");
        mediaChatDomainService.updateChatStatus(chatByConversationId.getChatId(), ChatStatusEnum.CHAT_CANCELED.getCode());
        log.info("取消对话-失效对话:{}", request);
        cozeDomainService.chatStop(chatByConversationId.getChatId(), request.getConversationId());
    }

    public ChatResp chatCompletion(ChatReq chatReq) {
        Long conversationId = chatReq.getConversationId();
        Long mediaObjectsId = chatReq.getMediaObjectsId();
        MediaObjects obj = mediaObjectsDomainService.getById(mediaObjectsId);
        Assert.isTrue(MediaEditStatusEnum.sucCheck(obj.getStatus()), "分析对象未完成，不能进行对话");
        Long userId = userCommon.getUserId(true);
        if (null == conversationId) {
            MediaConversation currentConversation = mediaConversationDomainService.findCurrentConversation(mediaObjectsId, userId);
            if (null != currentConversation) {
                conversationId = currentConversation.getConversationId();
            }
        }
        Long chatId = chatReq.getChatId();
        // 创建会话ID 预设对话
        if (null == conversationId) {
            return conversationCreateRetrun(mediaObjectsId, userId);
        }

        MediaChat mediaChat;
        if (null != chatId) {
            // 查看是否有回答
            mediaChat = mediaChatDomainService.findChatById(chatId);
            if (null == mediaChat) {
                log.warn("对话内容不存在:chatId={},请求内容{}", chatId, JSON.toJSONString(chatReq));
                throw new BusinessException("对话内容不存在:" + chatId);
            }
            Assert.notNull(mediaChat, "对话内容不存在:" + chatId);
            MediaChatText chatText = mediaChatDomainService.findChatTextByChatId(chatId, ChatMsgTypeEnum.ANSWER.getType());
            if (mediaChat.getStatus().equals(ChatStatusEnum.COMPLETED.getCode())) {
                return chatRespBuild(conversationId, chatText, ChatStatusEnum.COMPLETED.getCode());
            } else if (mediaChat.getStatus().equals(ChatStatusEnum.FAILURE.getCode())) {
                return chatRespBuild(conversationId, chatText, ChatStatusEnum.FAILURE.getCode());
            } else if (mediaChat.getStatus().equals(ChatStatusEnum.CHAT_CANCELED.getCode())) {
                chatText.setContent(null);
                return chatRespBuild(conversationId, chatText, ChatStatusEnum.FAILURE.getCode());
            }
        } else {
            String role = null == chatReq.getRole() ? ChatRoleEnum.SYSTEM.getRole() : chatReq.getRole();
            MediaChat mediaChatOld = mediaChatDomainService.findChatByConversationId(conversationId);
            if (null == mediaChatOld && ChatRoleEnum.SYSTEM.getRole().equals(role)) {
                mediaChat = chatCreate(conversationId, contentBuild(mediaObjectsId), role, userId);
            } else if (null != chatReq.getContent() && ChatRoleEnum.USER.getRole().equals(role)) {
                if (null != mediaChatOld && ChatStatusEnum.CREATED.getCode().equals(mediaChatOld.getStatus())) {
                    log.warn("上个对话未完成，不能创建新对话,会话{},对话{}", conversationId, mediaChatOld.getChatId());
                    throw new InvalidParameterException("上个对话状态未完成，不能创建新对话");
                }
                mediaChat = chatCreate(conversationId, chatReq.getContent(), role, userId);
            } else {
                mediaChat = mediaChatDomainService.findChatByConversationId(conversationId);
            }
            chatId = mediaChat.getChatId();
        }

        return getCompletedResult(chatId, conversationId, mediaChat, userId, ChatRoleEnum.ASSISTANT.getRole());

    }

    private ChatResp conversationCreateRetrun(Long mediaObjectsId, Long userId) {
        Long conversationId = conversationCreate(mediaObjectsId, userId);
        MediaChat mediaChat = chatCreate(conversationId, contentBuild(mediaObjectsId), ChatRoleEnum.SYSTEM.getRole(), userId);
        Long chatId = mediaChat.getChatId();
        CozeCompletedDo result = cozeDomainService.getCompleted(conversationId, chatId);
        MediaChatText mediaChatText = mediaChatDomainService.chatMsgSave(chatId, result.getContent(), Long.valueOf(mediaChat.getBotId()), userId
                , ChatRoleEnum.ASSISTANT.getRole(), ChatMsgTypeEnum.ANSWER.getType());

        if (ChatStatusEnum.CREATED.getCode().equals(result.getStatus())) {
            return chatRespBuild(conversationId, mediaChatText, ChatStatusEnum.CREATED.getCode());
        }
        if (ChatStatusEnum.FAILURE.getCode().equals(result.getStatus())) {
            mediaChatDomainService.updateChatStatus(chatId, ChatStatusEnum.FAILURE.getCode());
            return chatRespBuild(conversationId, mediaChatText, ChatStatusEnum.FAILURE.getCode());
        }
        mediaChatDomainService.updateChatStatus(chatId, ChatStatusEnum.COMPLETED.getCode());
        return chatRespBuild(conversationId, mediaChatText, ChatStatusEnum.COMPLETED.getCode());
    }

    private ChatResp getCompletedResult(Long chatId, Long conversationId, MediaChat mediaChat, Long userId, String role) {
        CozeCompletedDo result = cozeDomainService.getCompleted(conversationId, chatId);
        MediaChatText chatText = mediaChatDomainService.chatMsgSave(chatId, result.getContent()
                , Long.valueOf(mediaChat.getBotId()), userId, role, ChatMsgTypeEnum.ANSWER.getType());

        if (ChatStatusEnum.CREATED.getCode().equals(result.getStatus())) {
            return chatRespBuild(conversationId, chatText, ChatStatusEnum.CREATED.getCode());
        }
        if (ChatStatusEnum.FAILURE.getCode().equals(result.getStatus())) {
            mediaChatDomainService.updateChatStatus(chatId, ChatStatusEnum.FAILURE.getCode());
            return chatRespBuild(conversationId, chatText, ChatStatusEnum.FAILURE.getCode());
        }
        mediaChatDomainService.updateChatStatus(chatId, ChatStatusEnum.COMPLETED.getCode());
        log.info("用户{}对话完成,chatId={}", userId, mediaChat.getChatId());
        return chatRespBuild(conversationId, chatText, ChatStatusEnum.COMPLETED.getCode());
    }

    private String contentBuild(Long mediaObjectsId) {
        JSONArray allContentBuild = mediaAllDataStart.allContentBuild(mediaObjectsId);
        return allContentBuild.toJSONString();
    }

    private ChatResp chatRespBuild(Long conversationId, MediaChatText mediaChatText, Integer code) {
        ChatResp chatResp = new ChatResp();
        chatResp.setChatId(String.valueOf(mediaChatText.getChatId()));
        chatResp.setMessageId(mediaChatText.getId());
        if (null != mediaChatText.getCreateTime()) {
            chatResp.setChatTime(Times.toEpochMilli(mediaChatText.getCreateTime()));
        }
        chatResp.setStatus(code);
        chatResp.setConversationId(String.valueOf(conversationId));
        chatResp.setRole(mediaChatText.getRole());
        chatResp.setContent(mediaChatText.getContent());
        return chatResp;
    }

    private MediaChat chatCreate(Long conversationId, String content, String role, Long userId) {
        log.info("用户{}开始对话", userId);
        CozeChatDo cozeChatDo = cozeDomainService.contentBuild(content, userId, role);
        CozeChatResp.ChatObject chatObject = cozeDomainService.chatCreate(userId, conversationId, cozeChatDo);
        Assert.notNull(chatObject, "创建对话失败");
        Long chatId = Long.valueOf(chatObject.getId());
        MediaChat mediaChat = mediaChatDomainService.chatSave(conversationId, chatId, cozeChatDo.getBotId());
        mediaChatDomainService.chatMsgSave(chatId, content, userId, Long.valueOf(cozeChatDo.getBotId()), role, ChatMsgTypeEnum.QUESTION.getType());
        return mediaChat;
    }

    private Long conversationCreate(Long mediaObjectsId, Long userId) {
        ConversationResp.ConversationObject conversationObject = cozeDomainService.conversationCreate(userId);
        Assert.notNull(conversationObject, "创建会话失败");
        Long conversationId = Long.valueOf(conversationObject.getId());
        mediaConversationDomainService.create(mediaObjectsId, conversationId, userId);
        log.info("用户{},对象{},创建会话成功[{}]", userId, mediaObjectsId, conversationId);
        return conversationId;
    }

    public Flux<ChatResp> chatStream(ChatReq chatReq) {
        Long userId = userCommon.getUserId(true);
        log.info("用户{}请求流式对话：{}", userId, JSON.toJSONString(chatReq));
        Long conversationId = chatReq.getConversationId();
        if (null == chatReq.getConversationId()) {
            MediaConversation currentConversation = mediaConversationDomainService.findCurrentConversation(chatReq.getMediaObjectsId(), userId);
            if (null == currentConversation) {
                conversationId = conversationCreate(chatReq.getMediaObjectsId(), userId);
            } else {
                conversationId = currentConversation.getConversationId();
            }
        }

        MediaChat currentChat = mediaChatDomainService.findChatByConversationId(conversationId);
        CozeChatDo cozeChatDo;
        String role;

        if (chatReq.getRefresh()) {
            MediaChatText chatText = refreshChatCheck(chatReq.getConversationId(), chatReq.getChatId());
            role = chatText.getRole();
            cozeChatDo = cozeDomainService.contentBuild(chatText.getContent(), userId, role);
            // 失效对话
            mediaChatDomainService.clean(chatText.getChatId());
        } else if (null == currentChat) {
            role = ChatRoleEnum.SYSTEM.getRole();
            cozeChatDo = cozeDomainService.contentBuild(contentBuild(chatReq.getMediaObjectsId()), userId, role);
        } else {

            role = StringUtils.isEmpty(chatReq.getRole()) ? ChatRoleEnum.SYSTEM.getRole() : chatReq.getRole();
            if (null != chatReq.getChatId()) {
                MediaChat chat = mediaChatDomainService.findChatById(chatReq.getChatId());
                if (chat.getStatus().equals(ChatStatusEnum.CREATED.getCode())) {
                    return Flux.just(getCompletedResult(chat.getChatId(), conversationId, chat, userId, role));
                }
                if (chat.getStatus().equals(ChatStatusEnum.COMPLETED.getCode())) {
                    return Flux.just(getCompletedResult(chat.getChatId(), conversationId, chat, userId, role));
                }
                if (chat.getStatus().equals(ChatStatusEnum.FAILURE.getCode())) {
                    MediaChatText mediaChatText = mediaChatDomainService.findChatTextByChatId(chat.getChatId(), ChatMsgTypeEnum.ANSWER.getType());
                    return Flux.just(chatRespBuild(conversationId, mediaChatText, ChatStatusEnum.FAILURE.getCode()));
                }
                if (chat.getStatus().equals(ChatStatusEnum.CHAT_CANCELED.getCode())) {
                    MediaChatText mediaChatText = mediaChatDomainService.findChatTextByChatId(chat.getChatId(), ChatMsgTypeEnum.ANSWER.getType());
                    return Flux.just(chatRespBuild(conversationId, mediaChatText, ChatStatusEnum.CHAT_CANCELED.getCode()));
                }
            }
            String question = chatReq.getContent();
            if (currentChat.getStatus().equals(ChatStatusEnum.CHAT_CANCELED.getCode())) {
                MediaChatText chatTextByChatId = mediaChatDomainService.findChatTextByChatId(currentChat.getChatId(), ChatMsgTypeEnum.QUESTION.getType());
                if (chatTextByChatId != null && chatTextByChatId.getRole().equals(ChatRoleEnum.SYSTEM.getRole())) {
                    question = chatTextByChatId.getContent();
                    role = chatTextByChatId.getRole();
                }
            }
            cozeChatDo = cozeDomainService.contentBuild(question, userId, role);
        }

        cozeChatDo.setStream(true);
        Flux<CozeChatStreamResp.ChatDetail> chatDetailFlux = cozeDomainService.chatStream(conversationId, cozeChatDo, userId, role);

        final String[] chatId = {null};
        return chatDetailFlux.map(detail -> {
            ChatResp chatResp = new ChatResp();
            chatResp.setChatId(detail.getChat_id());
            chatResp.setConversationId(detail.getConversation_id());
            chatResp.setContent(detail.getContent());
            chatResp.setRole(detail.getRole());
            Integer status = 0;

            if (null == chatId[0] && null != detail.getId()) {
                chatId[0] = detail.getChatId();
            }

            if ("failed".equals(detail.getStatus())) {
                status = ChatStatusEnum.FAILURE.getCode();
                if (null != chatId[0]) {
                    mediaChatDomainService.updateChatStatus(Long.valueOf(chatId[0]), status);
                }
                throw new RuntimeException("对话失败");
            }
            if ("canceled".equals(detail.getStatus())) {
                status = ChatStatusEnum.CHAT_CANCELED.getCode();
                if (null != chatId[0]) {
                    mediaChatDomainService.updateChatStatus(Long.valueOf(chatId[0]), status);
                }
                throw new RuntimeException("对话已取消");
            }

            if ("verbose".equals(detail.getType()) || "follow_up".equals(detail.getType()) || "completed".equals(detail.getStatus())) {
                status = ChatStatusEnum.COMPLETED.getCode();
                if (null != chatId[0]) {
                    mediaChatDomainService.updateChatStatus(Long.valueOf(chatId[0]), ChatStatusEnum.COMPLETED.getCode());
                }
                chatResp.setChatTime(Times.toEpochMilli(LocalDateTime.now()));
            }
            chatResp.setStatus(status);

            return chatResp;
        });
    }
}
