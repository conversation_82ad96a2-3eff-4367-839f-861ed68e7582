package com.bluefocus.kolmonitorservice.application.service.media.impl;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;

import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.exception.BusinessException;
import com.bluefocus.kolmonitorservice.domain.datastory.DataStoryDomainService;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTotalVolumeResponse;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryVolumeTreadResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTrend;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTrendDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 2024/5/22 20:18
 * @description:
 */
@Slf4j
@Order(1)
@Service
@RequiredArgsConstructor
public class MediaVolumeStart extends IMediaStart {

    private final DataStoryDomainService dataStoryService;
    private final MediaTrendDomainService mediaTrendDomainService;
    public final static Long MAX_VOLUME = 1000000000L;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {

        DataHandleState dataHandleState = new DataHandleState();
        DataStoryEntity req = getDataStoryFixedParam(handle);
        MediaTrend mediaTrend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), sourceCode);
        if (null != mediaTrend.getVolumeTrendDay() && null != mediaTrend.getAllVolume()) {
            log.info("api声量趋势数据已存在,handle={},source={}，跳过", handle.getId(), sourceCode);
            dataHandleState.setVolume(true);
            dataHandleState.setVolumeNum(mediaTrend.getAllVolume());
            return dataHandleState;
        }

        String source = ExtraConditionEnum.codeOf(sourceCode);
        req.setSource(source);

        MediaObjects obj = mediaObjectsDomainService.getById(handle.getMediaObjectsId());
        DataStoryVolumeTreadResponse.ResultDTO trend = retryReq(req, 3, handle, sourceCode, obj);
        if (trend == null) {
            log.error("api声量趋势数据请求失败,handle={},source={}，跳过", handle.getId(), sourceCode);
            dataHandleState.setVolumeNum(null);
            dataHandleState.setVolume(false);
            return dataHandleState;
        }
        HashMap<String, Long> sourceTrend = trend.getTotal().get(source.toLowerCase(Locale.ROOT));
        List<KeyValue<String, Long>> trendList = getTrendList(sourceTrend);

        weekAndMonth(mediaTrend, trendList);
        mediaTrendDomainService.saveOrUpdate(mediaTrend);

        if (mediaTrend.getAllVolume() != null && mediaTrend.getAllVolume() > MAX_VOLUME) {
            objFail(handle, obj, BusinessException.KEY_TIME_OUT, null);
            dataHandleState.setVolume(false);
            dataHandleState.setVolumeNum(null);
        } else {
            dataHandleState.setVolume(true);
            dataHandleState.setVolumeNum(mediaTrend.getAllVolume());
        }

        return dataHandleState;

    }

    /**
     * 绘制趋势数据
     */
    protected void drawMediaVolumTrend(HashMap<String, List<KeyValue<String, Long>>> trendMap, MediaTaskHandle handle) {
        List<MediaTrend> mediaTrendList = mediaTrendDomainService.findByHandleIds(Collections.singletonList(handle.getId()));
        Map<Integer, MediaTrend> mediaTrendPoMap = mediaTrendList.stream().collect(Collectors.toMap(MediaTrend::getSourceCode, mediaTrend -> mediaTrend));

        trendMap.keySet().forEach(code -> {
            MediaTrend mediaTrendPo = mediaTrendPoMap.get(Integer.valueOf(code));

            if (!mediaTrendPo.getIsAlgorithmSuc().equals(FinishStatusEnum.CRAWLER.getCode()) && mediaTrendPo.getAllVolume() != null) {
                log.info("当前声量趋势数据已经落库，直接跳过，handle={}", handle.getId());
                return;
            }

            mediaTrendPo.setSourceCode(Integer.valueOf(code));
            mediaTrendPo.setMediaObjectsId(handle.getMediaObjectsId());
            mediaTrendPo.setHandleId(handle.getId());
            List<KeyValue<String, Long>> keyValueList = trendMap.get(code);

            weekAndMonth(mediaTrendPo, keyValueList);

            mediaTrendDomainService.saveOrUpdate(mediaTrendPo);
        });
    }

    private void weekAndMonth(MediaTrend mediaTrendPo, List<KeyValue<String, Long>> keyValueList) {
        keyValueList.sort(Comparator.comparing(KeyValue::getDate));
        mediaTrendPo.setVolumeTrendDay(JSON.toJSONString(keyValueList));
        mediaTrendPo.setVolumeTrendWeek(JSON.toJSONString(weekData(keyValueList)));
        mediaTrendPo.setVolumeTrendMonth(JSON.toJSONString(monthData(keyValueList)));
        long sum = keyValueList.stream().mapToLong(KeyValue::getValue).sum();
        mediaTrendPo.setAllVolume(sum);
        mediaTrendPo.setIsAlgorithmSuc(FinishStatusEnum.ANALY.getCode());
        mediaTrendPo.setAlgStatus(FinishStatusEnum.ANALY.getCode());
    }

    private DataStoryVolumeTreadResponse.ResultDTO retryReq(DataStoryEntity req, int i, MediaTaskHandle handle, Integer sourceCode, MediaObjects obj) {
        DataStoryVolumeTreadResponse volumeTread;
        try {
            i--;
            if (i < 0) {
                return null;
            }
            volumeTread = dataStoryService.getVolumeTread(req);
            if (volumeTread.isSuccess() && "200".equals(volumeTread.getCode())) {
                return volumeTread.getData();
            } else if (!volumeTread.isSuccess() && volumeTread.getMsg() != null) {
                handleMsg(null, String.format("当前数说声量api触发异常: 处理单元[%s],平台[%s],关键词=%s,异常信息e=[%s]", handle.getId(), sourceCode, handle.getKeyword(), volumeTread.getMsg()));
                if (volumeTread.getMsg().contains("敏感词")
                        || volumeTread.getMsg().contains("关键词格式错误")) {
                    objFail(handle, obj, volumeTread.getMsg(), null);
                    return null;
                } else if (volumeTread.getMsg().contains("timed out")) {
                    objFail(handle, obj, BusinessException.KEY_TIME_OUT, null);
                    return null;
                }
            }
        } catch (Exception e) {
            log.error("请求数说声量趋势接口报错，第{}次，处理批次={}，关键词={}", 3 - i, handle.getId(), handle.getKeyword());
        }
        return retryReq(req, i, handle, sourceCode, obj);
    }

    private DataStoryTotalVolumeResponse.ResultDTO retryAllVolume(DataStoryEntity req, int i, Long id) {
        DataStoryTotalVolumeResponse totalVolume;
        try {
            totalVolume = dataStoryService.getTotalVolume(req);
            if (totalVolume.isSuccess() && "200".equals(totalVolume.getCode())) {
                return totalVolume.getData();
            } else if (i > 0) {
                return retryAllVolume(req, --i, id);
            }

        } catch (ApiException e) {
            log.error("请求数说总互动量接口报错，第{}次", 3 - i);
            return retryAllVolume(req, i, id);
        }
        return null;
    }

    public Long getPlatAllVolumeNum(MediaTaskHandle handle, Integer sourceCode) {

        DataStoryEntity req = getDataStoryFixedParam(handle);
        req.setSources(Collections.singletonList(ExtraConditionEnum.codeOf(sourceCode)));

        DataStoryTotalVolumeResponse.ResultDTO resultDTO = retryAllVolume(req, 3, handle.getMediaObjectsId());
        if (resultDTO == null) {
            log.error("api声量趋势数据请求失败,handle={},source={}，跳过", handle.getId(), sourceCode);
            return null;
        }
        return resultDTO.getTotal();
    }

    public void defaultTrend(MediaTaskHandle handle, Integer sourceCode) {
        MediaTrend trend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), sourceCode);
        weekAndMonth(trend, getDefaultTrendList(handle.getStartTime(), handle.getEndTime()));
        mediaTrendDomainService.saveOrUpdate(trend);
    }
}
