package com.bluefocus.kolmonitorservice.application.service.plat;

import com.bluefocus.kolmonitorservice.application.service.plat.match.IUrlPattern;
import com.bluefocus.kolmonitorservice.application.service.plat.match.MatchType;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/7 16:43
 * @Description:
 */
@Service
@Slf4j
public class PlatHandlerKs extends PlatHandler<PlatHandlerKs.UrlPattern> {

    private static final String KS_PRE = "https://www.kuaishou.com/short-video";
    private static final String KS_USER_URL = "https://www.kuaishou.com/profile/";
    private static final String KS_F_URL = "https://www.kuaishou.com/f";

    @Override
    public List<UrlPattern> getUrlPatterns() {
        return Arrays.asList(UrlPattern.values());
    }

    @Getter
    @RequiredArgsConstructor
    public enum UrlPattern implements IUrlPattern {
        /**
         * 快手Url
         */
        SHORT_URL("https://v.kuaishou.com/", MatchType.PREFIX, "快手短链"),
        LONG_URL("https://www.kuaishou.com/", MatchType.PREFIX, "快手长链");
        private final String url;
        private final MatchType type;
        private final String description;

        @Override
        public boolean matches(String url) {
            return type.apply(url, this.url);
        }
    }

    @Override
    public PlatResult getResult(String oriUrl) {
        PlatResult platResult = new PlatResult();
        String realUrl = extractUrl(oriUrl, null, null);

        String longUrl = realUrl;
        if (realUrl.startsWith(KS_USER_URL)) {
            platResult.setStatus(MonitorStatusEnum.USER_PROFILE_ERROR);
        } else if (realUrl.startsWith(KS_F_URL) || !realUrl.startsWith(UrlPattern.LONG_URL.getUrl())) {
            longUrl = getLongUrlRetry(realUrl, PlatHandler.initial);
            // 快手中转链接需转换https://v.m.chenzhongtech.com/fw/long-video/3xt8gf9src7mgj6
            if (StringUtils.isNotBlank(longUrl)) {
                longUrl = KS_PRE + longUrl.substring(longUrl.lastIndexOf("/"));
            }
            platResult.setStatus(StringUtils.isNotBlank(longUrl) ? MonitorStatusEnum.PRE_MONITOR : MonitorStatusEnum.MONITOR_CHECK_ERROR);
        } else {
            longUrl = normLongUrl(longUrl);
            platResult.setStatus(checkLongUrl(realUrl) ? MonitorStatusEnum.PRE_MONITOR : MonitorStatusEnum.MONITOR_CHECK_ERROR);
        }

        platResult.setType(PlatformEnum.KS.getIndex());

        platResult.setUrl(oriUrl);
        platResult.setLongUrl(longUrl);
        return platResult;
    }

}
