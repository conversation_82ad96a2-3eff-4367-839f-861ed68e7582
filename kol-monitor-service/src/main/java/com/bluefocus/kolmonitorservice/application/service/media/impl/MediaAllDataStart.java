package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.basebean.exception.InvalidParameterException;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.SentimentEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.*;
import com.bluefocus.kolmonitorservice.domain.media.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 2024/5/22 20:18
 * @description:
 */
@Slf4j
@Order(5)
@Service
@RequiredArgsConstructor
public class MediaAllDataStart extends IMediaStart {

    private final MediaTrendDomainService mediaTrendDomainService;
    private final MediaAlgorithmStart mediaAlgorithmStart;
    private final MediaWordDomainService mediaWordDomainService;
    private final MediaSentimentDomainService mediaSentimentDomainService;
    private final MediaNoteDayDomainService mediaNoteDayDomainService;
    private final MediaNoteDomainService mediaNoteDomainService;

    @Override
    public DataHandleState handle(MediaTaskHandle handle, Integer sourceCode) {
        return null;
    }

    /**
     * 合并趋势、词云
     * 计算数据
     * 分平台判断 情感度|原帖|词云为null | 0时，判断声量是否为0，=0爬虫成功  !=0爬虫失败 发预警
     */
    public DataHandleState checkAndHandle(MediaTaskHandle handle, DataHandleState dataHandleState) {
        List<String> sourceCodes = Arrays.stream(handle.getSources().split(",")).collect(Collectors.toList());
        List<String> sourceNames = sourceCodes.stream().map(k -> ExtraConditionEnum.codeOf(Integer.valueOf(k))).collect(Collectors.toList());

        // 合并 声量、互动量趋势 全部趋势
        List<MediaTrend> mediaTrendList = handleAllTrendData(handle.getId(), handle.getMediaObjectsId());
        MediaTrend allMediaTrend = mediaTrendList.stream().filter(t -> t.getSourceCode().equals(ExtraConditionEnum.ALL.getCode())).findFirst().orElse(new MediaTrend());

        List<MediaWord> mediaWordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(handle.getId()));

        if (CollectionUtil.isEmpty(mediaWordList) && allMediaTrend.getAllVolume() > 0L) {
            dataHandleState.setCloudWord(false);
        } else {
            // 合并词云
            mediaWordList = handleAllWordData(mediaWordList, handle.getMediaObjectsId(), handle.getId());
            dataHandleState.setCloudWord(true);
        }

        // 无情感判断声量
        if (!dataHandleState.getSentiment()) {
            if (allMediaTrend.getAllVolume().equals(0L)) {
                dataHandleState.setSentiment(true);
            }
        }

        // 某个平台无原文判断声量
        if (dataHandleState.getYuanwen() && dataHandleState.getYuanwenNum() < sourceNames.size()) {
            long count = mediaTrendList.stream().filter(t -> t.getAllVolume().equals(0L)).count();
            dataHandleState.setYuanwen(count + dataHandleState.getYuanwenNum() >= sourceNames.size());
        } else if (!dataHandleState.getYuanwen()) {
            dataHandleState.setYuanwen(true);
            Map<Integer, MediaTrend> mediaTrendMap = mediaTrendList.stream().collect(Collectors.toMap(MediaTrend::getSourceCode, Function.identity(), (m1, m2) -> m2));
            for (String sourceCode : sourceCodes) {
                MediaTrend mediaTrend = mediaTrendMap.get(Integer.valueOf(sourceCode));
                if (mediaTrend.getAllVolume() > 0L) {
                    dataHandleState.setYuanwen(false);
                    break;
                }
            }
        }

        // 无词云 判断声量
        if (!dataHandleState.getCloudWord()) {
            if (allMediaTrend.getAllVolume().equals(0L)) {
                dataHandleState.setCloudWord(true);
            }
        } else {
            Map<Integer, MediaWord> mediaWordMap = mediaWordList.stream().collect(Collectors.toMap(MediaWord::getSourceCode, mediaWord -> mediaWord));
            for (MediaTrend mediaTrend : mediaTrendList) {
                MediaWord mediaWord = mediaWordMap.get(mediaTrend.getSourceCode());
                if (mediaTrend.getAllVolume() > 0L && mediaWord == null) {
                    log.error("当前分析对象所在渠道,声量不为0,词云为null,handle={}", handle.getId());
                    dataHandleState.setCloudWord(false);
                    break;
                }
            }
        }
        if (dataHandleState.getVolume() && dataHandleState.getSentiment()) {
            computeAllVolume2Obj(mediaTrendList, handle);
            setHotDay(mediaTrendList, handle);
        }

        return dataHandleState;
    }

    public void computeAllVolume2Obj(List<MediaTrend> mediaTrendList, MediaTaskHandle handle) {
        MediaObjects mediaObject = mediaObjectsDomainService.getById(handle.getMediaObjectsId());

        long sumVolume = mediaTrendList.stream().filter(t -> t.getSourceCode() != 0 && t.getAllVolume() != null).mapToLong(MediaTrend::getAllVolume).sum();
        long sumInteraction = mediaTrendList.stream().filter(t -> t.getSourceCode() != 0 && t.getAllInteraction() != null).mapToLong(MediaTrend::getAllInteraction).sum();
        mediaObject.setTotalVolume(sumVolume);
        mediaObject.setTotalInteraction(sumInteraction);

        List<KeyValue<Integer, Long>> volumeRateList = new ArrayList<>();
        List<KeyValue<Integer, Long>> interactionRateList = new ArrayList<>();
        mediaTrendList.stream()
                .filter(t -> t.getSourceCode() != 0)
                .forEach(mt -> {
                    KeyValue<Integer, Long> volumeRate = new KeyValue<>();
                    Integer code = mt.getSourceCode();
                    volumeRate.setKey(String.valueOf(code));
                    volumeRate.setValue(mt.getAllVolume());
                    volumeRate.setRate(mediaObject.getTotalVolume().equals(0L)
                            ? BigDecimal.ZERO
                            : new BigDecimal(mt.getAllVolume()).divide(new BigDecimal(mediaObject.getTotalVolume()), 4, 4));
                    volumeRateList.add(volumeRate);

                    KeyValue<Integer, Long> interactionRate = new KeyValue<>();
                    interactionRate.setKey(String.valueOf(code));
                    interactionRate.setValue(mt.getAllInteraction());
                    interactionRate.setRate(mediaObject.getTotalInteraction().equals(0L)
                            ? BigDecimal.ZERO
                            : new BigDecimal(mt.getAllInteraction()).divide(new BigDecimal(mediaObject.getTotalInteraction()), 4, 4));
                    interactionRateList.add(interactionRate);
                });
        mediaObject.setRateVolume(JSON.toJSONString(volumeRateList));
        mediaObject.setRateInteraction(JSON.toJSONString(interactionRateList));

        MediaSentiment sentiment = mediaSentimentDomainService.findByHandleId(handle.getId());

        if (sentiment != null) {
            mediaObject.setTotalSentiments(sentiment.getTotalSentiments());
            mediaObject.setRateSentiments(sentiment.getRateSentiments());
        }

        mediaObjectsDomainService.updateById(mediaObject);
    }

    /**
     * 前置条件，趋势全部成功
     */
    private void setHotDay(List<MediaTrend> mediaTrendList, MediaTaskHandle handle) {

        boolean day5 = mediaAlgorithmStart.checkAnalyzeDay(handle);
        for (MediaTrend mediaTrend : mediaTrendList) {

            if (day5) {
                mediaTrend.setIsAlgorithmSuc(FinishStatusEnum.FINISH.getCode());
                mediaTrend.setAlgStatus(FinishStatusEnum.FINISH.getCode());
                mediaTrendDomainService.updateById(mediaTrend);
                continue;
            }

            if (StringUtils.isEmpty(mediaTrend.getVolumeTrendDay())) {
                continue;
            }
            List<KeyValue<String, Long>> keyValues = kvJsonConvert(mediaTrend.getVolumeTrendDay());
            keyValues.sort(Comparator.comparingLong(KeyValue::getValue));
            int size = keyValues.size();
            ArrayList<String> dateList = new ArrayList<>();
            ArrayList<Long> valList = new ArrayList<>();
            int i = 1;
            // 前3天的
            while (i <= 3 && size >= i) {
                valList.add(keyValues.get(size - i).getValue());
                i++;
            }
            keyValues.sort(Comparator.comparing(KeyValue::getDate));
            for (Long aLong : valList) {
                for (KeyValue<String, Long> keyValue : keyValues) {
                    if (keyValue.getValue().equals(aLong) && !dateList.contains(keyValue.getDate())) {
                        dateList.add(keyValue.getDate());
                        break;
                    }
                }
            }

            if (dateList.size() > 0) {
                mediaTrendDomainService.lambdaUpdate()
                        .set(MediaTrend::getHotDay, JSON.toJSONString(dateList))
                        .eq(MediaTrend::getId, mediaTrend.getId())
                        .update();
            }
        }

    }

    protected List<MediaWord> handleAllWordData(List<MediaWord> mediaWordList, Long objId, Long handleId) {
        MediaWord po = mediaWordList.stream().filter(k -> k.getSourceCode() == 0).findFirst().orElseGet(MediaWord::new);
        po.setMediaObjectsId(objId);
        po.setHandleId(handleId);
        po.setCreateTime(LocalDateTime.now());
        po.setSourceCode(ExtraConditionEnum.ALL.getCode());
        po.setAllWord(mergeWordData(mediaWordList.stream().filter(k -> k.getSourceCode() != 0).map(MediaWord::getAllWord).collect(Collectors.toList())));
        mediaWordDomainService.saveOrUpdate(po);
        if (mediaWordList.stream().noneMatch(w -> w.getSourceCode() == 0)) {
            mediaWordList.add(po);
        }
        return mediaWordList;
    }

    protected List<MediaTrend> handleAllTrendData(Long handleId, Long mediaObjectsId) {

        List<MediaTrend> mediaTrendDays = mediaTrendDomainService.findByHandleIds(Collections.singletonList(handleId));
        Map<Integer, MediaTrend> mediaTrendMap = mediaTrendDays.stream().collect(Collectors.toMap(MediaTrend::getSourceCode, mediaTrend -> mediaTrend));

        MediaTrend po = mediaTrendMap.get(ExtraConditionEnum.ALL.getCode());
        if (po == null) {
            po = new MediaTrend();
            po.setCreateTime(LocalDateTime.now());
        }

        po.setHandleId(handleId);
        po.setMediaObjectsId(mediaObjectsId);
        po.setUpdateTime(LocalDateTime.now());
        po.setSourceCode(ExtraConditionEnum.ALL.getCode());
        List<MediaTrend> platMediaTrendList = mediaTrendDays.stream().filter(m -> m.getSourceCode() != 0).collect(Collectors.toList());
        po.setVolumeTrendDay(mergeTrendData(platMediaTrendList.stream().map(MediaTrend::getVolumeTrendDay).collect(Collectors.toList())));
        po.setVolumeTrendWeek(mergeTrendData(platMediaTrendList.stream().map(MediaTrend::getVolumeTrendWeek).collect(Collectors.toList())));
        po.setVolumeTrendMonth(mergeTrendData(platMediaTrendList.stream().map(MediaTrend::getVolumeTrendMonth).collect(Collectors.toList())));
        po.setInteractionTrendDay(mergeTrendData(platMediaTrendList.stream().map(MediaTrend::getInteractionTrendDay).collect(Collectors.toList())));
        po.setInteractionTrendWeek(mergeTrendData(platMediaTrendList.stream().map(MediaTrend::getInteractionTrendWeek).collect(Collectors.toList())));
        po.setInteractionTrendMonth(mergeTrendData(platMediaTrendList.stream().map(MediaTrend::getInteractionTrendMonth).collect(Collectors.toList())));
        po.setAllVolume(mediaTrendDays.stream().filter(m -> m.getSourceCode() != 0 && m.getAllVolume() != null).mapToLong(MediaTrend::getAllVolume).sum());
        po.setAllInteraction(mediaTrendDays.stream().filter(m -> m.getSourceCode() != 0 && m.getAllInteraction() != null).mapToLong(MediaTrend::getAllInteraction).sum());
        po.setIsAlgorithmSuc(FinishStatusEnum.ANALY.getCode());
        po.setAlgStatus(FinishStatusEnum.ANALY.getCode());

        mediaTrendDomainService.saveOrUpdate(po);
        if (!mediaTrendMap.containsKey(ExtraConditionEnum.ALL.getCode())) {
            mediaTrendDays.add(po);
        }
        return mediaTrendDays;
    }

    private String mergeTrendData(List<String> trendList) {
        if (trendList.size() == 1) {
            return trendList.get(0);
        }
        List<List<KeyValue<String, Long>>> collect = trendList.stream().map(this::kvJsonConvert).collect(Collectors.toList());

        List<KeyValue<String, Long>> kvList = new ArrayList<>(collect.stream().flatMap(Collection::stream)
                .collect(Collectors.groupingBy(KeyValue::getDate, Collectors.reducing((kv1, kv2) -> {
                    long newValue = kv1.getValue() + kv2.getValue();
                    return new KeyValue<>(kv1.getKey(), kv1.getLabel(), kv1.getDate(), newValue, kv1.getRate(), kv1.getEnable());
                }))).values()).stream().filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        return JSON.toJSONString(kvList);
    }

    private String mergeWordData(List<String> wordList) {
        if (wordList.size() == 1) {
            return wordList.get(0);
        }
        List<List<KeyValue<String, Long>>> collect = wordList.stream().map(this::kvJsonConvert).collect(Collectors.toList());

        List<KeyValue<String, Long>> kvList = new ArrayList<>(collect.stream().flatMap(Collection::stream)
                .collect(Collectors.groupingBy(KeyValue::getLabel, Collectors.reducing((kv1, kv2) -> {
                    long newValue = kv1.getValue() + kv2.getValue();
                    return new KeyValue<>(kv1.getKey(), kv1.getLabel(), kv1.getDate(), newValue, kv1.getRate(), kv1.getEnable());
                }))).values()).stream().filter(Optional::isPresent).map(Optional::get).sorted(Comparator.comparing(KeyValue::getValue)).collect(Collectors.toList());

        List<KeyValue<String, Long>> sortList = new ArrayList<>();
        int count = getWordArtCount(kvList.size());
        for (int i = 1; i <= count; i++) {
            sortList.add(kvList.get(kvList.size() - i));
        }
        return JSON.toJSONString(sortList);
    }

    public JSONArray allContentBuild(Long mediaObjectsId) {
        JSONArray array = new JSONArray();

        MediaTaskHandle mediaTaskHandle = mediaTaskHandleDomainService.getRunningTaskHandleByTaskAndObjId(null, mediaObjectsId);
        MediaObjects mediaObj = mediaObjectsDomainService.getById(mediaObjectsId);
        if (null == mediaObj) {
            throw new InvalidParameterException("分析对象不存在");
        }

        List<MediaTrend> mediaTrendList = mediaTrendDomainService.findByHandleIds(Collections.singletonList(mediaTaskHandle.getId()));
        List<MediaWord> mediaWordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(mediaTaskHandle.getId()));
        Arrays.stream(mediaTaskHandle.getSources().split(",")).mapToInt(Integer::parseInt).forEach(code -> {

            Map<String, Object> plat = new LinkedHashMap<>();
            plat.put("平台", ExtraConditionEnum.nameOf(code));

            // json引用问题

            for (MediaTrend mediaTrend : mediaTrendList) {
                if (mediaTrend.getSourceCode().equals(code)) {
                    plat.put("总声量", mediaTrend.getAllVolume());
                    plat.put("总互动量", mediaTrend.getAllInteraction());

                    List<KeyValue<String, Long>> valueList = kvJsonConvert(mediaTrend.getVolumeTrendDay());
                    if (StringUtils.isNotEmpty(mediaTrend.getHotDay())) {
                        List<String> hotDayList = JSON.parseArray(mediaTrend.getHotDay(), String.class);
                        plat.put("声量趋势", hotDayList.stream().sorted().limit(3).map(hot -> {
                            Map<String, Object> map = new HashMap<>(8);
                            map.put("日期", hot);
                            for (KeyValue<String, Long> kv : valueList) {
                                if (kv.getDate().equals(hot)) {
                                    map.put("声量", kv.getValue());
                                    break;
                                }
                            }
                            List<MediaNoteDay> noteList = mediaNoteDayDomainService.findHotDayNote(mediaTaskHandle.getId(), hot, code, 3);
                            map.put("笔记列表", getThreeNoteDay(noteList, code));
                            return map;
                        }).collect(Collectors.toList()));
                    }

                }
            }
            for (MediaWord mediaWord : mediaWordList) {
                if (mediaWord.getSourceCode().equals(code)) {
                    List<KeyValue> keyValues = JSON.parseArray(mediaWord.getAllWord(), KeyValue.class);
                    // 取前20个
                    List<KeyValue> collect = keyValues.stream()
                            .sorted(Comparator.comparing(kv -> convertToLong(kv.getValue())))
                            .limit(20).collect(Collectors.toList());
                    plat.put("词云列表", collect);
                }
            }

            List<MediaNote> noteList = mediaNoteDomainService.findByHandleIdAndCode(mediaTaskHandle.getId(), code);
            plat.put("笔记列表", getThreeNote(noteList, code));
            array.add(new JSONObject(plat));
        });

        List<KeyValue> rateSentiments = JSON.parseArray(mediaObj.getRateSentiments(), KeyValue.class);
        Map<String, Long> sentimentMap = rateSentiments.stream()
                .collect(Collectors.toMap(
                        kv -> SentimentEnum.codeOf(kv.getKey()),
                        kv -> new BigDecimal(kv.getValue().toString()).longValue(),
                        (oldValue, newValue) -> oldValue
                ));
        Long positive = sentimentMap.getOrDefault(SentimentEnum.POSITIVE.getDesc(), 0L);
        Long negative = sentimentMap.getOrDefault(SentimentEnum.NEGATIVE.getDesc(), 0L);

        long sum = positive + negative;
        if (sum != 0) {
            JSONObject nsr = new JSONObject();
            KeyValue<String, String> nsrkV = new KeyValue<>();
            nsrkV.setKey("NSR");
            BigDecimal nsrValue = new BigDecimal(positive - negative).divide(new BigDecimal(sum), 3, RoundingMode.HALF_UP);
            nsrkV.setValue(nsrValue.multiply(new BigDecimal("100")).toPlainString() + "%");
            nsr.put("净情感度", nsrkV);
            array.add(nsr);
        }

        JSONObject sentiment = new JSONObject();
        sentiment.put("情感分布", rateSentiments.stream()
                .peek(kv -> kv.setKey(SentimentEnum.codeOf(kv.getKey()))).collect(Collectors.toList()));
        array.add(sentiment);

        return array;
    }


    private JSONArray getThreeNote(List<MediaNote> noteList, Integer code) {
        JSONArray arrayNote = new JSONArray();
        for (MediaNote mediaNote : noteList) {
            JSONObject note = new JSONObject();
            note.put("平台", ExtraConditionEnum.nameOf(code));
            note.put("头像", mediaNote.getHeadImg());
            note.put("作者", mediaNote.getAuthor());
            note.put("标题", mediaNote.getTitle());
            note.put("内容", spilContent(mediaNote.getContent()));
            note.put("发布时间", mediaNote.getPublishTime());
            note.put("笔记链接", mediaNote.getNoteUrl());
            note.put("互动量", mediaNote.getInteractionCnt());
            note.put("点赞量", mediaNote.getLikeCnt());
            note.put("收藏量", mediaNote.getCollectionCnt());
            note.put("转发量", mediaNote.getRepostsCnt());
            note.put("评论量", mediaNote.getReviewCnt());
            note.put("笔记类型", mediaNote.getIsOriginal());
            note.put("命中关键词", mediaNote.getSearchKeyword());
            note.put("封面命中内容", spilContent(mediaNote.getCoverOcrContent()));
            note.put("音频命中内容", spilContent(mediaNote.getAudioOcrContent()));
            note.put("花字命中内容", spilContent(mediaNote.getHighlightOcrContent()));
            arrayNote.add(note);
        }
        return arrayNote;
    }

    private JSONArray getThreeNoteDay(List<MediaNoteDay> noteList, Integer code) {
        JSONArray arrayNote = new JSONArray();
        for (MediaNoteDay mediaNote : noteList) {
            JSONObject note = new JSONObject();
            note.put("平台", ExtraConditionEnum.nameOf(code));
            note.put("头像", mediaNote.getHeadImg());
            note.put("作者", mediaNote.getAuthor());
            note.put("标题", mediaNote.getTitle());
            note.put("内容", spilContent(mediaNote.getContent()));
            note.put("发布时间", mediaNote.getPublishTime());
            note.put("笔记链接", mediaNote.getNoteUrl());
            note.put("互动量", mediaNote.getInteractionCnt());
            note.put("点赞量", mediaNote.getLikeCnt());
            note.put("收藏量", mediaNote.getCollectionCnt());
            note.put("转发量", mediaNote.getRepostsCnt());
            note.put("评论量", mediaNote.getReviewCnt());
            note.put("笔记类型", mediaNote.getIsOriginal());
            note.put("命中关键词", mediaNote.getSearchKeyword());
            note.put("封面命中内容", spilContent(mediaNote.getCoverOcrContent()));
            note.put("音频命中内容", spilContent(mediaNote.getAudioOcrContent()));
            note.put("花字命中内容", spilContent(mediaNote.getHighlightOcrContent()));
            arrayNote.add(note);
        }
        return arrayNote;
    }

    private String spilContent(String content) {
        if (null != content && content.length() > 300) {
            return content.substring(0, 300);
        }
        return content;
    }

    private final static List<String> STRINGS = Arrays.asList("平台", "总声量", "总互动量", "净情感度", "声量趋势", "词云列表", "笔记列表");

    private static Long convertToLong(Object value) {
        if (value instanceof Number) {
            return ((Number) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        } else {
            return 0L;
        }
    }
}
