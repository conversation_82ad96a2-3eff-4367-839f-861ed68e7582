package com.bluefocus.kolmonitorservice.application.service.route.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.common.TokenCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.util.DataStoryUtils;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.route.platform.comm.PlatResponse;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSJobStatusDetailResponse;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteReqRecord;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteTask;
import com.bluefocus.kolmonitorservice.domain.route.platform.datastory.req.DsJobDefaultRequest;
import com.bluefocus.kolmonitorservice.domain.route.platform.datastory.resp.DsJobDefaultResponse;
import com.bluefocus.kolmonitorservice.domain.route.service.RouteReqRecordDomainService;
import com.bluefocus.kolmonitorservice.domain.route.service.RouteTaskDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RateIntervalUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 18:12
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataStoryPlatform implements IPlatform {

    public final static String DATA_STORY = "datastory";
    public final static String ROUTE_API_LIMITER = "ROUTE_API_LIMITER";
    public final static String CREAT_1 = "https://dc.datastory.com.cn/project/job/add";
    public final static String CREAT_2 = "https://dc.datastory.com.cn/application/market/job/v1/run";
    public final static String RERUN_1 = "https://dc.datastory.com.cn/job/schedule/rerun";
    public final static String DETAIL_1 = "https://dc.datastory.com.cn/project/job/status/detail";
    private final RouteTaskDomainService routeTaskDomainService;
    private final RouteReqRecordDomainService routeReqRecordDomainService;
    private final FsRobotUtil fsRobotUtil;
    private final RedisUtils redisUtils;
    private final DataStoryUtils dataStoryUtils;

    @Value("${robot.developer2}")
    private String developer;
    @Value("${dataStory.api.token}")
    protected String apiToken;
    @Value("${spring.profiles.active}")
    private String env;

    @Override
    public String getPlatform() {
        return DATA_STORY;
    }

    private PlatResponse coverData(DsJobDefaultResponse response) {
        PlatResponse platResponse = new PlatResponse();
        platResponse.setCode(response.getCode());
        platResponse.setData(response.getData());
        platResponse.setMsg(response.getMsg());
        platResponse.setSuccess(response.isSuccess());
        return platResponse;
    }

    private DsJobDefaultRequest getDsJobDefaultRequest(String method, String api, String contentType, String platform, JSONObject data) {

        TokenCommon token = dataStoryUtils.getToken();
        if (!token.getSuccess()) {
            sendRouteRobotMsg(String.format("token预警：获取平台[%s],账号[%s],token失败,请人工处理", token.getPlatform(), token.getUserName()));
            return new DsJobDefaultRequest(method, api, contentType, null, data);
        }
        if (!data.containsKey("token")) {
            data.put("token", apiToken);
        }
        return new DsJobDefaultRequest(method, api, contentType, token.getToken(), data);
    }

    public void sendRouteRobotMsg(String content) {
        fsRobotUtil.sendRouteRobotMsg(developer, content);
    }

    @Override
    public PlatResponse creatTask(RouteReqRecord routeRecord, JSONObject data) {

        DsJobDefaultRequest dsJobDefaultRequest = getDsJobDefaultRequest(routeRecord.getMethod(), routeRecord.getApi()
                , routeRecord.getType(), routeRecord.getPlatform(), data);
        PlatResponse platResponse;
        switch (routeRecord.getApi()) {
            case CREAT_1:
            case CREAT_2:
                try {
                    DsJobDefaultResponse response = routeTaskDomainService.runDataStory(dsJobDefaultRequest);
                    if (!response.isSuccess()) {
                        log.error("数说聚合创建任务失败，请求者[{}]，请求参数[{}]，响应结果：[{}]", routeRecord.getBizId(), routeRecord.getReqParam(), response);
                        sendRouteRobotMsg(String.format("数说聚合创建任务失败，请求者[%s]，请求参数[%s]，响应信息：[%s]", routeRecord.getBizId()
                                , routeRecord.getReqParam(), response.getMsg()));
                        routeRecord.setResqParam(JSON.toJSONString(response));
                        routeReqRecordDomainService.updateById(routeRecord);
                        platResponse = coverData(response);
                        break;
                    }
                    // 创建类，需要保存任务id，定期计费
                    Long jobId = Long.valueOf(response.getData().toString());
                    saveRouteTask(routeRecord, jobId);
                    platResponse = coverData(response);
                } catch (Exception e) {
                    log.error("请求数说任务失败,e=", e);
                    platResponse = new PlatResponse();
                    platResponse.setMsg(e.getMessage());
                    platResponse.setSuccess(false);
                }
//                sendRouteRobotMsg(String.format("业务组[%s]在平台[%s]创建任务:\n记录id[%s],状态[%s],\n创建参数【%s】"
//                        , routeRecord.getBizId(), routeRecord.getPlatform(), routeRecord.getId(), platResponse.isSuccess(), routeRecord.getReqParam()));
                break;
            default:
//                Assert.isTrue(limit(routeRecord.getPlatform() + ":" + routeRecord.getBizId() + ":" + routeRecord.getApi(), getLimiter(20), 60)
//                        , String.format("[%s]平台接口[%s]调用太频繁(QPM20)", routeRecord.getPlatform(), routeRecord.getApi()));
                try {
                    DsJobDefaultResponse response2 = routeTaskDomainService.runDataStory(dsJobDefaultRequest);
                    platResponse = coverData(response2);
                } catch (Exception e) {
                    log.error("请求数说任务失败,e=", e);
                    platResponse = new PlatResponse();
                    platResponse.setMsg(e.getMessage());
                    platResponse.setSuccess(false);
                }

        }
        return platResponse;
    }

    private Integer getLimiter(Integer count) {
        RBucket<Object> string = redisUtils.getString(ROUTE_API_LIMITER);
        if (!string.isExists()) {
            if ("dev".equals(env)) {
                string.set(count == null ? 1 : count);
            } else {
                string.set(count == null ? 5 : count);
            }
        }
        return Integer.valueOf(string.get().toString());
    }

    public void statistOldTask(RouteTask routeTask) {
        JSONObject data = new JSONObject();
        data.put("id", routeTask.getJobId());
        DsJobDefaultRequest dsJobDefaultRequest = getDsJobDefaultRequest("get", DETAIL_1, null, routeTask.getPlatform(), data);
        try {
            DsJobDefaultResponse response = routeTaskDomainService.runDataStory(dsJobDefaultRequest);
            if (response.isSuccess()) {
                DSJobStatusDetailResponse.ResultDTO resultDTO = JSONObject.parseObject(response.getData().toString(), DSJobStatusDetailResponse.ResultDTO.class);
                if (resultDTO.getJobStatus() != null) {
                    long sum = resultDTO.getJobStatus().stream().mapToLong(DSJobStatusDetailResponse.JobStatusDTO::getDataCount).sum();
                    List<Long> subJobs = resultDTO.getJobStatus().stream().map(DSJobStatusDetailResponse.JobStatusDTO::getJobId).collect(Collectors.toList());
                    routeTask.setDataCount(sum);
                    routeTask.setSubJob(JSON.toJSONString(subJobs));
                }
                routeTask.setState(resultDTO.getStatus());
                routeTask.setJobName(resultDTO.getName());
                routeTaskDomainService.updateById(routeTask);
            } else {
                log.error("请求数说任务失败,response={}", JSON.toJSONString(response));
            }
        } catch (ApiException e) {
            log.error("请求数说任务失败,e=", e);
        }
    }

    private void saveRouteTask(RouteReqRecord record, Long jobId) {
        RouteTask routeTask = new RouteTask();
        routeTask.setPlatform(record.getPlatform());
        routeTask.setBizId(record.getBizId());
        routeTask.setJobId(String.valueOf(jobId));
        routeTask.setCreateTime(LocalDateTime.now());
        routeTask.setState(0);
        routeTaskDomainService.save(routeTask);
    }

    @Override
    public PlatResponse queryTask(RouteReqRecord routeRecord, JSONObject data) {
        try {
//            Assert.isTrue(limit(routeRecord.getPlatform() + ":" + routeRecord.getBizId() + ":" + routeRecord.getApi(), getLimiter(60), 60)
//                    , String.format("[%s]平台接口[%s]调用太频繁(QPM60)", routeRecord.getPlatform(), routeRecord.getApi()));

            DsJobDefaultRequest dsJobDefaultRequest = getDsJobDefaultRequest(routeRecord.getMethod(), routeRecord.getApi()
                    , routeRecord.getType(), routeRecord.getPlatform(), data);
            DsJobDefaultResponse response = routeTaskDomainService.runDataStory(dsJobDefaultRequest);
            return coverData(response);
        } catch (ApiException e) {
            log.error("请求数说任务失败,e=", e);
            PlatResponse platResponse = new PlatResponse();
            platResponse.setMsg(e.getMessage());
            platResponse.setSuccess(false);
            return platResponse;
        }
    }

    protected boolean limit(String key, Integer count, Integer scope) {
        return redisUtils.getRateLimiter(key, count, scope, RateIntervalUnit.SECONDS).tryAcquire();
    }

}
