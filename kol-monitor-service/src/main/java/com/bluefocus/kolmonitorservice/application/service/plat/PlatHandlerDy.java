package com.bluefocus.kolmonitorservice.application.service.plat;

import com.bluefocus.kolmonitorservice.application.service.plat.match.IUrlPattern;
import com.bluefocus.kolmonitorservice.application.service.plat.match.MatchType;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date: 2023/3/7 16:43
 * @Description:
 */
@Service
@Slf4j
public class PlatHandlerDy extends PlatHandler<PlatHandlerDy.UrlPattern> {

    private static final String DY_USER_URL = "https://www.douyin.com/user/";
    private static final String DY_SHARE_USER_URL = "https://www.iesdouyin.com/share/user/";
    private static final String DY_VIDEO_URL = "https://www.douyin.com/video/";
    private static final String DY_PRAM_URL = "modal_id=";
    private static final String REGEX = "https?://www\\.douyin\\.com/user/[^?]*\\?(?:[^&]*&)*modal_id=(\\d+)";

    @Override
    public List<UrlPattern> getUrlPatterns() {
        return Arrays.asList(UrlPattern.values());
    }

    @Getter
    @RequiredArgsConstructor
    public enum UrlPattern implements IUrlPattern {
        /**
         * 抖音Url
         */
        SHORT_URL("https://v.douyin", MatchType.PREFIX, "抖音短链"),
        LONG_URL("https://www.douyin", MatchType.PREFIX, "抖音长链"),
        LONG_IES_URL("https://www.iesdouyin.com", MatchType.PREFIX, "快手长链"),
        ;
        private final String url;
        private final MatchType type;
        private final String description;

        @Override
        public boolean matches(String url) {
            return type.apply(url, this.url);
        }
    }

    @Override
    public PlatResult getResult(String oriUrl) {
        PlatResult platResult = new PlatResult();
        String realUrl = extractUrl(oriUrl, REGEX, DY_VIDEO_URL);

        String longUrl = realUrl;
        if (realUrl.startsWith(DY_USER_URL) && !realUrl.contains(DY_PRAM_URL) || realUrl.startsWith(DY_SHARE_USER_URL)) {
            platResult.setStatus(MonitorStatusEnum.USER_PROFILE_ERROR);
        } else if (realUrl.startsWith("https://v.douyin")) {
            longUrl = getLongUrlRetry(realUrl, PlatHandler.initial);
            if (StringUtils.isBlank(longUrl)) {
                platResult.setStatus(MonitorStatusEnum.MONITOR_CHECK_ERROR);
            } else if (longUrl.startsWith(DY_USER_URL) && !longUrl.contains(DY_PRAM_URL) || longUrl.startsWith(DY_SHARE_USER_URL)) {
                platResult.setStatus(MonitorStatusEnum.USER_PROFILE_ERROR);
            } else {
                platResult.setStatus(MonitorStatusEnum.PRE_MONITOR);
            }
        } else {
            if (matcherUrl(realUrl)) {
                platResult.setStatus(MonitorStatusEnum.PRE_MONITOR);
            } else {
                platResult.setStatus(checkLongUrl(realUrl) ? MonitorStatusEnum.PRE_MONITOR : MonitorStatusEnum.MONITOR_CHECK_ERROR);
            }
            longUrl = normDyLongUrl(realUrl);
        }
        platResult.setType(PlatformEnum.DY.getIndex());
        platResult.setUrl(oriUrl);
        platResult.setLongUrl(longUrl);
        return platResult;
    }

    private Boolean matcherUrl(String url) {
        String regex = "^https?:\\/\\/www\\.(?:douyin|iesdouyin)\\.com\\/.*((\\?modal_id=|\\/)|\\bvideo|\\bnote\\/)\\d+\\/?";
        Pattern pattern = Pattern.compile(regex);
        return pattern.matcher(url).matches();
    }

    private String normDyLongUrl(String longUrl) {

        if (longUrl.contains("modal_id") || longUrl.endsWith("/")) {
            return longUrl;
        } else {
            int end = longUrl.indexOf("?");
            if (end != -1) {
                String substring = longUrl.substring(0, end);
                return substring.endsWith("/") ? substring : substring + "/";
            } else {
                return longUrl.endsWith("/") ? longUrl : longUrl + "/";
            }
        }

    }

}
