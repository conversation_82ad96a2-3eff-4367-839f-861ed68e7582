package com.bluefocus.kolmonitorservice.application.service.task;

import cn.hutool.core.collection.CollectionUtil;
import com.bluefocus.kolmonitorservice.application.service.route.impl.DataStoryPlatform;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteTask;
import com.bluefocus.kolmonitorservice.domain.route.service.RouteTaskDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @author: yjLiu
 * @date: 0005 2024/8/5 20:25
 * @description:
 */
@EnableScheduling
@Configuration
@Log4j2
@RequiredArgsConstructor
public class DataStoryTask {
    private static final String DATASTORY_TASK_LOCK = "datastory_task_lock";
    private static final String DATASTORY_TASK_FLAG = "datastory_task_flag";

    private final DataStoryPlatform dataStoryPlatform;
    private final RouteTaskDomainService routeTaskDomainService;
    private final RedisUtils redisUtils;


    @Scheduled(cron = "0 0 9,12,15,18 * * ?")
    public void dataStoryTask() {
        RLock lock = redisUtils.getLock(DATASTORY_TASK_LOCK);
        try {
            boolean acquired = lock.tryLock(0, 60, TimeUnit.SECONDS);
            if (!acquired) {
                log.error("Failed to acquire lock.");
                return;
            }
            RBucket<Object> flag = redisUtils.getString(DATASTORY_TASK_FLAG);
            if (flag.isExists()) {
                log.error("Another task is already running.");
                return;
            }

            List<RouteTask> unFinishList = routeTaskDomainService.findUnFinishList();
            if (unFinishList.isEmpty()) {
                log.error("No unfinished tasks found.");
                return;
            }

            for (RouteTask routeTask : unFinishList) {
                try {
                    dataStoryPlatform.statistOldTask(routeTask);
                } catch (Exception e) {
                    log.error("Failed to execute task: {}", e.getMessage());
                }
            }

            // 设置标志
            flag.set("SEND_SUCCESS", 5, TimeUnit.MINUTES);
            log.info("DataStoryTask completed successfully.");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("Interrupted while waiting for lock: {}", e.getMessage());
        } finally {
            lock.unlock();
        }
    }
}
