package com.bluefocus.kolmonitorservice.application.service.plat;

import com.bluefocus.kolmonitorservice.application.service.plat.match.IUrlPattern;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/7 16:43
 * @Description:
 */
@Service
@Slf4j
public class PlatHandlerOthers extends PlatHandler<PlatHandlerOthers.EmptyUrlPattern> {

    public enum EmptyUrlPattern implements IUrlPattern {

        /**
         * 空
         */
        INSTANCE;

        @Override
        public boolean matches(String url) {
            return false;
        }
    }

    @Override
    public List<EmptyUrlPattern> getUrlPatterns() {
        return Collections.emptyList();
    }

    @Override
    public PlatResult getResult(String url) {
        log.warn("未识别的平台链接: {}", url);
        return PlatResult.builder()
                .url(url)
                .status(MonitorStatusEnum.MONITOR_CHECK_ERROR)
                .build();
    }

}
