package com.bluefocus.kolmonitorservice.application.service.plat;

import com.bluefocus.kolmonitorservice.application.service.plat.match.IUrlPattern;
import com.bluefocus.kolmonitorservice.application.service.plat.match.MatchType;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/3/7 16:43
 * @Description:
 */
@Service
@Slf4j
public class PlatHandlerXhs extends PlatHandler<PlatHandlerXhs.UrlPattern> {

    private static final String XHS_USER_URL = "https://www.xiaohongshu.com/user/profile/";

    @Override
    public List<UrlPattern> getUrlPatterns() {
        return Arrays.asList(UrlPattern.values());
    }

    @Getter
    @RequiredArgsConstructor
    public enum UrlPattern implements IUrlPattern {
        /**
         * 小红书Url
         */
        SHORT_URL("http://xhslink", MatchType.PREFIX, "小红书短链"),
        LONG_URL("https://www.xiaohongshu", MatchType.PREFIX, "小红书长链"),
        ;
        private final String url;
        private final MatchType type;
        private final String description;

        @Override
        public boolean matches(String url) {
            return type.apply(url, this.url);
        }
    }

    @Override
    public PlatResult getResult(String oriUrl) {
        PlatResult platResult = new PlatResult();
        String realUrl = extractUrl(oriUrl, null, null);

        String longUrl = realUrl;
        if (realUrl.startsWith(XHS_USER_URL) && (realUrl.length() < 72 || realUrl.contains("?"))) {
            platResult.setStatus(MonitorStatusEnum.USER_PROFILE_ERROR);
        } else if (!realUrl.startsWith(UrlPattern.LONG_URL.getUrl())) {
            longUrl = getLongUrlRetry(realUrl, PlatHandler.initial);
            platResult.setStatus(StringUtils.isNotBlank(longUrl) ? MonitorStatusEnum.PRE_MONITOR : MonitorStatusEnum.MONITOR_CHECK_ERROR);
        } else {
            longUrl = normLongUrl(longUrl);
            platResult.setStatus(checkLongUrl(realUrl) ? MonitorStatusEnum.PRE_MONITOR : MonitorStatusEnum.MONITOR_CHECK_ERROR);
        }

        platResult.setType(PlatformEnum.XHS.getIndex());
        platResult.setUrl(oriUrl);
        platResult.setLongUrl(longUrl);
        return platResult;
    }
}
