package com.bluefocus.kolmonitorservice.application.service.plat;

import com.bluefocus.kolmonitorservice.application.service.plat.match.IUrlPattern;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HeaderElement;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.ss.formula.functions.T;

import java.io.IOException;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Date: 2023/3/7 16:29
 * @Description:
 */
@Log4j2
public abstract class PlatHandler<T extends Enum<T> & IUrlPattern> {

    public static final int RETRY = 3;
    public static final int initial = 1;
    public static final Pattern PATTERN = Pattern.compile("https?://[\\w\\-.~:/?#\\[\\]@!$&'()*+,;=]+");

    protected String extractUrl(String input, String regex, String newUrl) {
        if (StringUtils.isNotEmpty(regex)) {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(input);
            if (matcher.find() && StringUtils.isNotEmpty(newUrl)) {
                String modalId = matcher.group(1);
                return newUrl + modalId;
            }
            if (matcher.find()) {
                return matcher.group();
            }
        } else {
            Matcher matcher = PATTERN.matcher(input);
            if (matcher.find()) {
                return matcher.group();
            }
            return input;
        }
        return input;
    }

    public abstract List<T> getUrlPatterns();

    public boolean supports(String url) {
        if (url == null) {
            return false;
        }
        for (T pattern : getUrlPatterns()) {
            if (pattern.matches(url)) {
                return true;
            }
        }
        return false;
    }

    public abstract PlatResult getResult(String oriUrl);

    public String getLongUrl(String url) {
        if (null == url) {
            return null;
        }
        long startTime = System.currentTimeMillis();
        HttpGet request = new HttpGet(url);
        try {
            HttpClient client = HttpClients.createDefault();
            request.setConfig(RequestConfig.custom().setRedirectsEnabled(false).build());
            HttpResponse response = client.execute(request);
            Header[] locations = response.getHeaders("Location");
            if (locations.length == 0) {
                return null;
            }
            HeaderElement[] elements = locations[0].getElements();
            if (elements.length == 0) {
                return null;
            }
            String value = elements[0].getName();
            if (StringUtils.isBlank(value) || !value.startsWith("http") || !value.contains("?")) {
                return null;
            }
            return normLongUrl(value);
        } catch (IOException e) {
            log.error("sentGet请求异常,请求路径【{}】,请求时间【{}】ms,异常信息【{}】", url, System.currentTimeMillis() - startTime, e.getMessage());
        } finally {
            request.releaseConnection();
        }
        return null;
    }

    public String getLongUrlRetry(String url, int i) {
        String longUrl = getLongUrl(url);
        if (StringUtils.isBlank(longUrl) && i <= PlatHandler.RETRY) {
            log.error("链接[{}]输入有误，重试[{}]次", url, i);
            i++;
            getLongUrlRetry(url, i);
        }
        return longUrl;
    }


    public Boolean checkLongUrl(String url) {
        if (StringUtils.isEmpty(url)) {
            return Boolean.FALSE;
        }
        long startTime = System.currentTimeMillis();
        HttpGet request = new HttpGet(url);
        try {
            HttpClient client = HttpClients.createDefault();
            request.setConfig(RequestConfig.custom().build());
            HttpResponse response = client.execute(request);
            return response.getStatusLine().getStatusCode() == 200;
        } catch (IOException e) {
            log.error("sentGet请求异常,请求路径【{}】,请求时间【{}】ms,异常信息【{}】", url, System.currentTimeMillis() - startTime, e.getMessage());
        } finally {
            request.releaseConnection();
        }
        return Boolean.FALSE;
    }

    public String normLongUrl(String longUrl) {
        int end = longUrl.indexOf("?");
        if (end != -1) {
            return longUrl.substring(0, end);
        } else {
            return longUrl;
        }
    }

}
