package com.bluefocus.kolmonitorservice.application.valid.media;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.basebean.exception.InvalidParameterException;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.IChatService;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatRefreshReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatStopReq;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ConversationReq;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatHistoryResp;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatResp;
import com.bluefocus.kolmonitorservice.base.enums.ChatRoleEnum;
import reactor.core.publisher.Flux;

/**
 * @author: yjLiu
 * @date: 0017 2024/10/17 16:49
 * @description:
 */
public class ChatValidHandler implements IChatService {
    @Override
    public ResponseBean<ChatHistoryResp> chatList(Long mediaObjectsId) {
        return null;
    }

    @Override
    public Flux<ChatResp> chatStream(ChatReq chatReq) {
        Assert.notNull(chatReq.getMediaObjectsId(), "分析对象Id不可为null");
        if (null == chatReq.getConversationId()) {
            if (null == chatReq.getChatId()) {
                Assert.isFalse(ChatRoleEnum.USER.getRole().equals(chatReq.getRole()), "会话Id为null时，对话角色应为system");
            }
        }
        if (null != chatReq.getRole() && chatReq.getRole().equals(ChatRoleEnum.USER.getRole())) {
            Assert.notNull(chatReq.getContent(), "角色为user时，对话输入内容不可为null");
        }

        if (null != chatReq.getContent()) {
            chatReq.setRole(ChatRoleEnum.USER.getRole());
        }

        if (chatReq.getRefresh()) {
            Assert.notNull(chatReq.getConversationId(), "刷新时，会话Id不可为null");
            Assert.notNull(chatReq.getChatId(), "刷新时，对话Id不可为null");
        }
        return null;
    }

    @Override
    public ResponseBean<ChatResp> chatCompletion(ChatReq chatReq) {

        if (null == chatReq.getConversationId()) {
            Assert.notNull(chatReq.getMediaObjectsId(), "会话Id为null时，分析对象Id不可为null");
            if (null == chatReq.getChatId()) {
                Assert.isFalse(ChatRoleEnum.USER.getRole().equals(chatReq.getRole()), "会话Id为null时，对话角色应为system");
            }
        }
        if (null != chatReq.getRole() && chatReq.getRole().equals(ChatRoleEnum.USER.getRole())) {
            Assert.notNull(chatReq.getContent(), "角色为user时，对话输入内容不可为null");
        }

        if (null != chatReq.getContent()) {
            chatReq.setRole(ChatRoleEnum.USER.getRole());
        }
//        if (null != chatReq.getConversationId()) {
//            Assert.notNull(chatReq.getMediaObjectsId(), "会话Id为null时，分析对象Id不可为null");
//            if (null== chatReq.getChatId() && null == chatReq.getContent()){
//                throw new InvalidParameterException("会话存在时，对话Id、对话内容不可同时为空");
//            }
//        }
        return null;
    }

    @Override
    public BaseResponseBean clean(ConversationReq request) {
        Assert.notNull(request.getConversationId(), "会话Id为null");
        return null;
    }

    @Override
    public ResponseBean<ChatResp> refreshChat(ChatRefreshReq request) {
        Assert.notNull(request.getConversationId(), "会话Id为null");
        return null;
    }

    @Override
    public BaseResponseBean stopChat(ChatStopReq request) {
        Assert.notNull(request.getConversationId(), "会话Id为null");
        return null;
    }
}
