package com.bluefocus.kolmonitorservice.application.service.media.impl;

import lombok.Getter;
import lombok.Setter;

/**
 * @author: yjLiu
 * @date: 0020 2024/6/20 13:46
 * @description:
 */
@Getter
@Setter
public class DataHandleState {
    private Boolean volume = false;
    private Long volumeNum = 0L;
    private Boolean interaction = false;
    private Boolean sentiment = false;
    private Boolean cloudWord = false;
    private Boolean yuanwen = false;
    private Integer yuanwenNum = 0;
    private Boolean all = false;

    public Boolean checkAllState(){
        return volume.equals(true)
                && interaction.equals(true)
                && sentiment.equals(true)
                && cloudWord.equals(true)
                && yuanwen.equals(true);
    }
}
