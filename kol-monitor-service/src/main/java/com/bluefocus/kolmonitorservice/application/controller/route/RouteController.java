package com.bluefocus.kolmonitorservice.application.controller.route;

import com.bluefocus.basebean.bean.PageBean;
import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.IRouteService;
import com.bluefocus.kolmonitorinterface.dto.route.req.RouteTaskReq;
import com.bluefocus.kolmonitorinterface.dto.route.req.RouteTokenReq;
import com.bluefocus.kolmonitorinterface.dto.route.resp.RouteTaskResp;
import com.bluefocus.kolmonitorinterface.dto.route.resp.RouteTokenResp;
import com.bluefocus.kolmonitorservice.application.service.route.RouteService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Locale;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 14:42
 * @description:
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/route/task/")
public class RouteController implements IRouteService {

    private final RouteService routeService;

    @Override
    @PostMapping("token")
    public ResponseBean<RouteTokenResp> token(@RequestBody RouteTokenReq req) {
        return routeService.token(req);
    }

    @Override
    @GetMapping("list")
    public ResponseBean<PageBean<RouteTaskResp>>  routeTaskList(@RequestParam(value = "bizId") String bizId
            , @RequestParam(value = "start", required = false) Long startTime
            , @RequestParam(value = "end", required = false) Long endTime
            , @RequestParam(value = "page", defaultValue = "0") int page
            , @RequestParam(value = "limit", defaultValue = "10") int limit) {
        return routeService.routeTaskList(bizId, startTime, endTime, page, limit);
    }


    @Override
    @PostMapping("creat")
    public ResponseBean<Object> routeTaskCreat(@RequestBody RouteTaskReq request) {
        Assert.isTrue(routeService.checkToken(request.getToken()), "token校验失败", 10005);
        String lowerCase = request.getMethod().toLowerCase(Locale.ROOT);
        Assert.isFalse("post".equals(lowerCase) && StringUtils.isEmpty(request.getType()), "post请求时间，必传type");
        return routeService.routeTaskCreat(request);
    }

    @Override
    @PostMapping("query")
    public ResponseBean<Object> routeTaskQuery(@RequestBody RouteTaskReq request) {

        Assert.isTrue(routeService.checkToken(request.getToken()), "token校验失败", 10005);
        String lowerCase = request.getMethod().toLowerCase(Locale.ROOT);
        Assert.isFalse("post".equals(lowerCase) && StringUtils.isEmpty(request.getType()), "post请求未传type");
        return routeService.routeTaskQuery(request);
    }

}
