package com.bluefocus.kolmonitorservice.application.service.plat.match;

/**
 * @author: yjLiu
 * @date: 0010 2025/6/10 17:03
 * @description:
 */
public enum MatchType {

    /**
     * 匹配规则
     */
    PREFIX(String::startsWith),
    REGEX(String::matches),
    CONTAINS(String::contains);

    private final UrlMatcher matcher;

    MatchType(UrlMatcher matcher) {
        this.matcher = matcher;
    }

    public boolean apply(String url, String pattern) {
        return matcher.match(url, pattern);
    }
}


