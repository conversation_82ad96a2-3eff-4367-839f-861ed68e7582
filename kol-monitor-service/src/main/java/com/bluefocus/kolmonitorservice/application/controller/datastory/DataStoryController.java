package com.bluefocus.kolmonitorservice.application.controller.datastory;

import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.kolmonitorservice.application.service.datastory.DataStoryService;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.*;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/data/story/")
public class DataStoryController {

    private final DataStoryService dataStoryService;

    @GetMapping("task/detail")
    public DSJobDetailResponse jobDetail(@RequestParam(value = "jobId") Long jobId) {
        return dataStoryService.queryJobDetail(jobId);
    }

    @GetMapping("task/status")
    public DSJobStatusDetailResponse jobStatusDetail(@RequestParam(value = "jobId") Long jobId
            , @RequestParam(value = "page", required = false) Integer page) {
        return dataStoryService.queryJobStatus(jobId, page);
    }

    @GetMapping("task/sub_job")
    public List<Long> getSubJobId(@RequestParam(value = "jobId") Long jobId, @RequestParam(value = "activeDate", required = false) String activeDate) {
        return dataStoryService.getSubJobId(jobId, activeDate);
    }

    @GetMapping("task/rerun")
    public DSJobRerunResponse jobRerun(@RequestParam(value = "id") Long id) {
        return dataStoryService.jobRerun(id);
    }

    @GetMapping("task/count")
    public DSJobCountResponse jobCount(@RequestParam(value = "jobId") Long jobId
            , @RequestParam(value = "startTime") String startTime
            , @RequestParam(value = "endTime") String endTime) {
        return dataStoryService.queryJobCount(jobId, startTime, endTime);
    }

    @GetMapping("task/search")
    public DSJobSearchResponse jobSearch(
            @RequestParam(value = "jobId") Long jobId
            , @RequestParam(value = "startTime") String startTime
            , @RequestParam(value = "endTime") String endTime
            , @RequestParam(value = "scrollId", required = false) String scrollId) {
        return dataStoryService.queryJobSearch(jobId, startTime, endTime, scrollId);
    }

    @GetMapping("task/refresh/token")
    public BaseResponseBean refreshLoginToken() {
        if (dataStoryService.refreshLoginToken()) {
            return BaseResponseBean.SUCCESS;
        }
        return new BaseResponseBean(10001, "刷新token失败");

    }
}
