package com.bluefocus.kolmonitorservice.application.service.monitor;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.basebean.exception.ResponseException;
import com.bluefocus.kolmonitorinterface.dto.ppt.PPTData;
import com.bluefocus.kolmonitorinterface.dto.ppt.PPTemplate;
import com.bluefocus.kolmonitorinterface.dto.ppt.PptChart;
import com.bluefocus.kolmonitorinterface.dto.ppt.PptChartData;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleTrend;
import com.bluefocus.kolmonitorinterface.dto.res.PieChartResp;
import com.bluefocus.kolmonitorinterface.dto.res.PlatformReportData;
import com.bluefocus.kolmonitorinterface.dto.res.TrendValue;
import com.bluefocus.kolmonitorservice.base.enums.MimeEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import com.bluefocus.ppttemplateengine.Configurator;
import com.bluefocus.ppttemplateengine.PPTCreator;
import com.bluefocus.ppttemplateengine.SimpleCatalogConverter;
import com.bluefocus.ppttemplateengine.SimpleDataConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.ConcurrentSkipListMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.bluefocus.kolmonitorservice.base.common.PPtConstant.FILE_NAME;

/**
 * @author: yjLiu
 * @date: 0016 2025/6/16 18:59
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PptExport {

    public void creatPptFile(String allFileName, String templateDir, String tmpDir, ConcurrentSkipListMap<String, JSONObject> pageMaps, CopyOnWriteArrayList<PPTemplate> templates) {

        Configurator configurator = new Configurator();
        JSONObject data = new JSONObject();
        data.put("pageMaps", pageMaps);
        JSONObject originCatalog = new JSONObject();
        originCatalog.put("templates", templates.stream().sorted(Comparator.comparing(PPTemplate::getPageKey)).collect(Collectors.toList()));

        configurator.setPptName(allFileName)
                .setTemplateDir(templateDir)
                .setTmpDir(tmpDir)
                .setOriginCatalog(originCatalog.toString())
                .setOriginDataDict(data.toString())
                .setDictionaryConverter(new SimpleDataConverter())
                .setCatalogConverter(new SimpleCatalogConverter());

        PPTCreator pptCreator = new PPTCreator(configurator);
        pptCreator.run();
        log.info("ppt生成完成");
    }

    public void downPpt(String fileName, String filePath) {

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        ServletOutputStream outputStream = null;
        try {
            HttpServletResponse response = requestAttributes.getResponse();
            response.setContentType(MimeEnum.PPTX.getType());
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharsetUtil.UTF_8));

            outputStream = response.getOutputStream();
            BufferedInputStream inputStream = FileUtil.getInputStream(filePath);
            byte[] bytes = new byte[1024 * 10];
            int read;
            while ((read = inputStream.read(bytes, 0, 1024 * 10)) != -1) {
                outputStream.write(bytes, 0, read);
            }
            inputStream.close();
            outputStream.close();
        } catch (IOException e) {
            throw new ResponseException("生成ppt异常");
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("关流异常:", e);
                }
            }
        }
    }

    public void handleHomePageData(CopyOnWriteArrayList<PPTemplate> templates, ConcurrentSkipListMap<String, JSONObject> pageMaps
            , String userName, String monitorPeriod, String projectName) {
        JSONObject maps = new JSONObject();
        maps.put("w1", new PPTData("text", projectName));
        maps.put("w2", new PPTData("text", monitorPeriod));
        maps.put("w3", new PPTData("text", userName));
        JSONObject fileMaps = new JSONObject();
        fileMaps.put("maps", maps);
        pageMaps.put(FILE_NAME.get(PlatformEnum.ALL.getIndex()).get(0), fileMaps);
        templates.add(new PPTemplate(FILE_NAME.get(PlatformEnum.ALL.getIndex()).get(0)));
        templates.add(new PPTemplate(FILE_NAME.get(PlatformEnum.ALL.getIndex()).get(2)));
    }

    public static List<String> getTableData(PlatformReportData platData) {
        List<String> dataList = new ArrayList<>();
        dataList.add(toWestNumFormat(platData.getArticleNum().longValue()));
        dataList.add(toWestNumFormat(platData.getTotalInteractionNum()));
        dataList.add(platData.getInteractionRate() + "%");
        dataList.add(toWestNumFormat(platData.getTotalReadNum()));
        dataList.add(toWestNumFormat(platData.getTotalLikeNum()));
        dataList.add(toWestNumFormat(platData.getTotalCollectionNum()));
        dataList.add(toWestNumFormat(platData.getTotalCommentNum()));
        return dataList;
    }

    public static String toWestNumFormat(Long number) {
        if (null == number) {
            return null;
        }
        DecimalFormat df = new DecimalFormat("#,###");
        return df.format(number);
    }

    public static PPTData<PptChart> getPieChartData(String plantRate, String rename, String col1, String col2) {

        List<String> platNames = new ArrayList<>();
        List<PieChartResp> notesRateList = JSON.parseArray(plantRate, PieChartResp.class);

        List<String> numValues = new ArrayList<>();
        List<String> rateValues = new ArrayList<>();
        List<PptChartData> chartDataList = new ArrayList<>();

        PptChartData pptChartData1 = new PptChartData();
        PptChartData pptChartData2 = new PptChartData();
        notesRateList.forEach(notesRate -> {
            platNames.add(PlatformEnum.codeOf(notesRate.getPlatType()));
            numValues.add(notesRate.getValue().toString());
            rateValues.add(ObjectUtil.isNotNull(notesRate.getRate()) ? notesRate.getRate() + "%" : "0.00%");
        });

        pptChartData1.setName(Collections.singletonList(col1));
        pptChartData1.setValues(numValues);
        pptChartData2.setName(Collections.singletonList(col2));
        pptChartData2.setValues(rateValues);

        chartDataList.add(pptChartData1);
        chartDataList.add(pptChartData2);

        PptChart pptChart = new PptChart();
        pptChart.setRename(rename);
        pptChart.setSeries(platNames);
        pptChart.setChartData(chartDataList);
        return new PPTData<>("pieChart", pptChart);

    }


    public void handleLineData(CopyOnWriteArrayList<PPTemplate> templates, ConcurrentSkipListMap<String, JSONObject> pageMaps
            , PlatformReportData platData, List<String> table, PPTData<PptChart> pieChart1, PPTData<PptChart> pieChart2, PlatformEnum platform) {
        List<String> fileNameList = FILE_NAME.get(platform.getIndex());

        fileNameList.forEach(fileName -> {
            PptChart pptChart = new PptChart();
            JSONObject maps = new JSONObject();
            ArticleTrend trend = null;
            if (fileName.contains("pie")) {
                trend = platData.getReadTrend();
                maps.put("w11", new PPTData("text", table.get(0)));
                maps.put("w12", new PPTData("text", table.get(1)));
                maps.put("w13", new PPTData("text", table.get(2)));
                maps.put("w14", new PPTData("text", table.get(3)));
                maps.put("w15", new PPTData("text", table.get(4)));
                maps.put("w16", new PPTData("text", table.get(5)));
                maps.put("w17", new PPTData("text", table.get(6)));
                maps.put("c3", pieChart1);
                maps.put("c4", pieChart2);
            } else if (fileName.contains("read")) {
                trend = platData.getReadTrend();
                if (trend != null && fileName.contains("read2")) {
                    return;
                }
                if (trend == null && fileName.contains("read2")) {
                    trend = new ArticleTrend();
                }
                pptChart.setRename("阅读量趋势");
//                maps.put("t1", new PPTData("table", Collections.singletonList(table.getData())));
                maps.put("w11", new PPTData("text", table.get(0)));
                maps.put("w12", new PPTData("text", table.get(1)));
                maps.put("w13", new PPTData("text", table.get(2)));
                maps.put("w14", new PPTData("text", table.get(3)));
                maps.put("w15", new PPTData("text", table.get(4)));
                maps.put("w16", new PPTData("text", table.get(5)));
                maps.put("w17", new PPTData("text", table.get(6)));
            } else if (fileName.contains("like")) {
                trend = platData.getLikeTrend();
                pptChart.setRename("点赞量趋势");
            } else if (fileName.contains("comment")) {
                trend = platData.getCommentTrend();
                pptChart.setRename("评论量趋势");
            } else if (fileName.contains("collect")) {
                trend = platData.getCollectionTrend();
                pptChart.setRename("收藏量趋势");
            } else if (fileName.contains("intnum")) {
                trend = platData.getInteractionTrend();
                pptChart.setRename("互动量趋势");
            }

            if (null == trend) {
                return;
            }

            // 构建多条折线图数据
            if (null != trend.getDataArr() && !trend.getDataArr().isEmpty()) {
                buildMultiLineChartData(pptChart, trend);
                maps.put("c1", new PPTData("lineChart", pptChart));
            }

            JSONObject fileMaps = new JSONObject();
            fileMaps.put("maps", maps);
            pageMaps.put(fileName, fileMaps);
            templates.add(new PPTemplate(fileName));
        });

    }

    /**
     * 构建多条折线图数据
     * 根据新的需求，现在需要支持多条折线图
     * 假设 ArticleTrend.dataArr 中的数据需要按照某种规则分组，每组形成一条折线
     *
     * @param pptChart PPT图表对象
     * @param trend 趋势数据
     */
    private void buildMultiLineChartData(PptChart pptChart, ArticleTrend trend) {
        if (trend.getDataArr() == null || trend.getDataArr().isEmpty()) {
            return;
        }

        // 收集所有日期并排序作为x轴
        Set<String> allDatesSet = new TreeSet<>();
        for (TrendValue trendValue : trend.getDataArr()) {
            if (ObjectUtil.isNotEmpty(trendValue.getKey())) {
                allDatesSet.add(trendValue.getKey());
            }
        }
        List<String> sortedDates = new ArrayList<>(allDatesSet);
        pptChart.setSeries(sortedDates);

        // 方案1: 如果数据结构变化，现在每个TrendValue代表一条完整的折线
        // 这种情况下，我们需要重新解析数据结构

        // 方案2: 如果仍然是时间序列数据，但需要按某种规则分组
        // 目前先实现单条折线，后续可以根据实际数据结构调整

        List<PptChartData> chartDataList = new ArrayList<>();

        // 创建一条折线，包含所有时间点的数据
        PptChartData pptChartData = new PptChartData();

        // 设置折线名称
        String lineName = ObjectUtil.isNotEmpty(trend.getName()) ? trend.getName() : "数据";
        pptChartData.setName(Collections.singletonList(lineName));

        // 创建日期到数值的映射
        Map<String, Long> dateValueMap = trend.getDataArr().stream()
                .filter(tv -> ObjectUtil.isNotEmpty(tv.getKey()) && tv.getValue() != null)
                .collect(Collectors.toMap(TrendValue::getKey, TrendValue::getValue, (v1, v2) -> v2));

        // 为每个日期填充对应的值，如果某个日期没有数据则填充0
        List<String> values = new ArrayList<>();
        for (String date : sortedDates) {
            Long value = dateValueMap.get(date);
            values.add(value != null ? value.toString() : "0");
        }
        pptChartData.setValues(values);
        chartDataList.add(pptChartData);

        // TODO: 如果需要多条折线，可以在这里添加更多的 PptChartData
        // 例如：根据不同的KOL、文章或其他维度创建多条折线

        pptChart.setChartData(chartDataList);
    }

    /**
     * 构建多指标折线图数据（可选方案）
     * 将多个不同类型的趋势合并到一个图表中显示
     *
     * @param pptChart PPT图表对象
     * @param platData 平台报告数据
     * @param chartTitle 图表标题
     */
    private void buildMultiMetricLineChartData(PptChart pptChart, PlatformReportData platData, String chartTitle) {
        pptChart.setRename(chartTitle);

        // 收集所有可用的趋势数据
        List<ArticleTrend> trends = new ArrayList<>();
        if (platData.getReadTrend() != null) {
            trends.add(platData.getReadTrend());
        }
        if (platData.getLikeTrend() != null) {
            trends.add(platData.getLikeTrend());
        }
        if (platData.getCommentTrend() != null) {
            trends.add(platData.getCommentTrend());
        }
        if (platData.getCollectionTrend() != null) {
            trends.add(platData.getCollectionTrend());
        }
        if (platData.getInteractionTrend() != null) {
            trends.add(platData.getInteractionTrend());
        }

        if (trends.isEmpty()) {
            return;
        }

        // 收集所有日期并排序作为x轴
        Set<String> allDatesSet = new TreeSet<>();
        for (ArticleTrend trend : trends) {
            if (trend.getDataArr() != null) {
                for (TrendValue trendValue : trend.getDataArr()) {
                    if (ObjectUtil.isNotEmpty(trendValue.getKey())) {
                        allDatesSet.add(trendValue.getKey());
                    }
                }
            }
        }
        List<String> sortedDates = new ArrayList<>(allDatesSet);
        pptChart.setSeries(sortedDates);

        // 为每个趋势创建一条折线
        List<PptChartData> chartDataList = new ArrayList<>();
        for (ArticleTrend trend : trends) {
            if (trend.getDataArr() == null || trend.getDataArr().isEmpty()) {
                continue;
            }

            PptChartData pptChartData = new PptChartData();
            String lineName = ObjectUtil.isNotEmpty(trend.getName()) ? trend.getName() : "数据";
            pptChartData.setName(Collections.singletonList(lineName));

            // 创建日期到数值的映射
            Map<String, Long> dateValueMap = trend.getDataArr().stream()
                    .filter(tv -> ObjectUtil.isNotEmpty(tv.getKey()) && tv.getValue() != null)
                    .collect(Collectors.toMap(TrendValue::getKey, TrendValue::getValue, (v1, v2) -> v2));

            // 为每个日期填充对应的值
            List<String> values = new ArrayList<>();
            for (String date : sortedDates) {
                Long value = dateValueMap.get(date);
                values.add(value != null ? value.toString() : "0");
            }
            pptChartData.setValues(values);
            chartDataList.add(pptChartData);
        }

        pptChart.setChartData(chartDataList);
    }

}
