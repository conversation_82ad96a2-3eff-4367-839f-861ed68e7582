package com.bluefocus.kolmonitorservice.application.service.monitor;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.bluefocus.basebean.exception.ResponseException;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorinterface.dto.excel.ExcelArticleData;
import com.bluefocus.kolmonitorinterface.dto.excel.ExcelTrendValue;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.enums.MimeEnum;
import com.bluefocus.kolmonitorservice.base.enums.ParameterEnum;
import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.KolVideoCloudDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleDetail;
import com.bluefocus.kolmonitorservice.domain.article.entity.KolVideoCloud;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0016 2025/6/16 18:53
 * @description:
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ArticleExport {

    private static final Map<String, List<String>> HASH_MAP = new HashMap<>();

    static {
        HASH_MAP.put(PlatformEnum.XHS.getName(), Arrays.asList(
                "id", "kolName", "kolHomePageUrl", "followerCount", "articleTitle", "articleUrl", "publishDate", "platformName", "monitorDate",
                "followerCount", "exposure", "playCount", "likeNum", "commentNum", "collectionNum", "forwardCount", "followNum", "interactionNum", "interactionRate"
        ));
        HASH_MAP.put(PlatformEnum.DY.getName(), Arrays.asList(
                "id", "kolName", "kolHomePageUrl", "followerCount", "articleTitle", "articleUrl", "publishDate", "platformName", "monitorDate",
                "followerCount", "playCount", "likeNum", "commentNum", "collectionNum", "forwardCount", "interactionNum", "interactionRate", "finishRate",
                "searchAfterViewPv", "searchAfterViewUv", "searchAfterPv", "searchAfterUv", "pluginShowNum", "pluginClickNum", "pluginClickRate"
        ));
        HASH_MAP.put(PlatformEnum.KS.getName(), Arrays.asList(
                "id", "kolName", "kolHomePageUrl", "followerCount", "articleTitle", "articleUrl", "publishDate", "platformName", "monitorDate",
                "followerCount", "playCount", "likeNum", "commentNum", "collectionNum", "forwardCount", "interactionNum", "interactionRate"
        ));
        HASH_MAP.put(PlatformEnum.BLI.getName(), Arrays.asList(
                "id", "kolName", "kolHomePageUrl", "followerCount", "articleTitle", "articleUrl", "publishDate", "platformName", "monitorDate",
                "followerCount", "playCount", "likeNum", "commentNum", "collectionNum", "coinNum", "bulletNum", "forwardCount", "interactionNum", "interactionRate"
        ));
    }

    private final ThreadPoolExecutor threadPoolExecutor;
    private final ArticleDomainService articleDomainService;
    private final KolVideoCloudDomainService kolVideoCloudDomainService;

    public void exportReport(Project project, List<Article> articleList) {

        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        Set<String> collect = articleList.stream().filter(article -> article.getPlatType().equals(PlatformEnum.DY.getIndex())).map(Article::getArticlePlatId).collect(Collectors.toSet());
        List<KolVideoCloud> kolVideoClouds = kolVideoCloudDomainService.findByVideoIds(collect);
        Map<String, KolVideoCloud> kolVideoCloudMap = kolVideoClouds.stream().collect(Collectors.toMap(KolVideoCloud::getVideoId, Function.identity()));

        List<CompletableFuture<List<ExcelArticleData>>> results = articleList.stream().map(article -> CompletableFuture.supplyAsync(() -> {
            List<ExcelArticleData> articleExcelList = new ArrayList<>();
            ArticleDetail articleDetail = articleDomainService.findDetailDataById(article.getId());
            if (ObjectUtil.isNotEmpty(articleDetail) && ObjectUtil.isNotEmpty(articleDetail.getTendObj())) {

                List<ExcelTrendValue> excelTrendValues = JSON.parseArray(articleDetail.getTendObj(), ExcelTrendValue.class);
                // 转发 曝光
                Map<String, ExcelTrendValue> trendValueMap = excelTrendValues.stream().collect(Collectors.toMap(ExcelTrendValue::getName, t -> t));
                Set<String> keys = excelTrendValues.stream()
                        .flatMap(t -> t.getDataArr().stream())
                        .map(KeyValue::getKey)
                        .sorted()
                        .collect(Collectors.toCollection(LinkedHashSet::new));

                for (String key : keys) {
                    ExcelArticleData rowData = new ExcelArticleData();
                    rowData.setMonitorDate(key);
                    rowData.setId(article.getId());
                    rowData.setKolName(article.getKolName());
                    rowData.setKolHomePageUrl(article.getHomeUrl());
                    if (ObjectUtil.isNotEmpty(article.getFansNum())) {
                        rowData.setFollowerCount(article.getFansNum().toString());
                    }
                    rowData.setArticleTitle(article.getArticleTitle());
                    rowData.setArticleUrl(article.getArticleUrl());
                    rowData.setPublishDate(article.getPostTime());
                    rowData.setPlatformName(PlatformEnum.codeOf(article.getPlatType()));
                    rowData.setPlayCount(getNumData(trendValueMap.get(ParameterEnum.READ.getName()), key));
                    rowData.setLikeNum(getNumData(trendValueMap.get(ParameterEnum.LIKE.getName()), key));
                    rowData.setCollectionNum(getNumData(trendValueMap.get(ParameterEnum.COLLECTION.getName()), key));
                    rowData.setCommentNum(getNumData(trendValueMap.get(ParameterEnum.COMMENT.getName()), key));
                    rowData.setForwardCount(getNumData(trendValueMap.get(ParameterEnum.SHARE.getName()), key));
                    rowData.setInteractionNum(getNumData(trendValueMap.get(ParameterEnum.INTERACTION.getName()), key));
                    String interactionRate = getNumData(trendValueMap.get(ParameterEnum.ArticleParameterEnum.INTERACTION_RATE.getName()), key);
                    rowData.setInteractionRate(StringUtils.isEmpty(interactionRate) ? "" : interactionRate.concat("%"));
                    String finishRate = getNumData(trendValueMap.get(ParameterEnum.ArticleParameterEnum.FINISH_RATE.getName()), key);
                    rowData.setFinishRate(getRate(finishRate));


                    if (article.getPlatType().equals(PlatformEnum.DY.getIndex())) {
                        KolVideoCloud kolVideoCloud = kolVideoCloudMap.get(article.getArticlePlatId());
                        if (kolVideoCloud != null) {
                            rowData.setPluginClickNum(kolVideoCloud.getPluginClickNum());
                            rowData.setPluginClickRate(getRate(kolVideoCloud.getPluginClickRate() != null ? kolVideoCloud.getPluginClickRate().toString() : null));
                            rowData.setPluginShowNum(kolVideoCloud.getPluginShowNum());
                            rowData.setSearchAfterPv(kolVideoCloud.getSearchAfterPv());
                            rowData.setSearchAfterPv(kolVideoCloud.getSearchAfterPv());
                            rowData.setSearchAfterUv(kolVideoCloud.getSearchAfterUv());
                            rowData.setSearchAfterViewPv(kolVideoCloud.getSearchAfterViewPv());
                            rowData.setSearchAfterViewUv(kolVideoCloud.getSearchAfterViewUv());
                        }
                    }
                    articleExcelList.add(rowData);
                }
            }

            return articleExcelList;
        }, threadPoolExecutor)).collect(Collectors.toList());

        CompletableFuture.allOf(results.toArray(new CompletableFuture[0]));
        Map<String, List<ExcelArticleData>> listMap = results.stream().map(result -> {
            try {
                return result.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("多线程执行中断异常，数据组装内容【{}】,ERR=", result, e);
                throw new ResponseException(ResponseException.UNDEFINED_EXCEPTION, "数据组装异常");
            }
        }).flatMap(Collection::stream).collect(Collectors.groupingBy(ExcelArticleData::getPlatformName));

        Assert.notEmpty(listMap, "项目数据还未生成");

        if (requestAttributes == null || requestAttributes.getResponse() == null) {
            throw new ResponseException(ResponseException.UNDEFINED_EXCEPTION, "数据请求异常");
        }

        HttpServletResponse response = requestAttributes.getResponse();
        try (ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream(), ExcelArticleData.class).build()) {
            String fileName = project.getName() + "-" + LocalDate.now().toString().replaceAll("-", "");
            response.setContentType(MimeEnum.XLSX.getType());
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, CharsetUtil.UTF_8));

            for (Map.Entry<String, List<ExcelArticleData>> entry : listMap.entrySet()) {
                List<String> list = HASH_MAP.get(entry.getKey());

                WriteSheet sheet = EasyExcel
                        .writerSheet(entry.getKey())
                        .head(ExcelArticleData.class)
                        .includeColumnFieldNames(list)
                        .build();
                excelWriter.write(entry.getValue(), sheet);
            }
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new ResponseException("导出失败");
        }

    }

    private String getRate(String finishRate) {
        if (ObjectUtil.isNotEmpty(finishRate)) {
            return new BigDecimal(finishRate).divide(new BigDecimal(100), 4, RoundingMode.HALF_UP).toString().concat("%");
        }
        return "";
    }

    private String getNumData(ExcelTrendValue excelTrendValue, String key) {
        if (ObjectUtil.isNotNull(excelTrendValue)) {
            List<KeyValue<String, Number>> dataArr = excelTrendValue.getDataArr();
            for (KeyValue<String, Number> keyValue : dataArr) {
                if (keyValue.getKey().equals(key)) {
                    return String.valueOf(keyValue.getValue());
                }
            }
        }
        return "";
    }

}
