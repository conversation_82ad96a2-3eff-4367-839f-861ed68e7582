package com.bluefocus.kolmonitorservice.base.common;

/**
 * @author: yjLiu
 * @date: 0014 2024/6/14 16:04
 * @description:
 */
public class RedisKeyComm {

    public final static String TASK_COUNT_KEY = "MEDIA:CHANNEL:STRATEGY";
    public final static String CRAWLER_DAY_LIMIT_COUNT = "MEDIA:CRAWLER:DAY:LIMIT:COUNT";
    public final static String MEDIA_API_LIMIT_RETRY = "MEDIA:API:LIMIT:RETRY";
    public final static String MEDIA_API_HANDLE_NOT_ = "MEDIA:API:HANDLE:NOT:";
    public final static String MEDIA_API_DAY_LIMIT_RETRY = "MEDIA:API:DAY:LIMIT:RETRY";
    public final static String MEDIA_CRAWLER_LIMIT_RETRY = "MEDIA:CRAWLER:LIMIT:RETRY";
    public final static String LOCK_MEDIA_ANALYZE_ = "LOCK:MEDIA:ANALYZE:";
    public final static String LOCK_MEDIA_HANDLE_API_ = "LOCK:MEDIA:HANDLE:API:%s";
    public final static String LOCK_MEDIA_WORDART_ = "LOCK:MEDIA:WORDART:";

    /**
     * 爬虫限制分钟数
     */
    public final static String MEDIA_API_TIMEOUT_ = "MEDIA:API:TIMEOUT";
    public final static String MEDIA_CRAWLER_WAIT_TIME = "MEDIA:CRAWLER:WAIT:TIME";
    public final static String MEDIA_CRAWLER_RETRY_TIME = "MEDIA:CRAWLER:RETRY:TIME";
    public final static String MEDIA_CRAWLER_HANDLE_NOT_ = "MEDIA:CRAWLER:HANDLE:NOT:";
    public final static String MEDIA_CRAWLER_TIMEOUT_TIME = "MEDIA:CRAWLER:TIMEOUT:TIME";
    public final static String MEDIA_CRAWLER_LOCK_ = "LOCK:MEDIA:CRAWLER:%s";
    public final static String MEDIA_API_LOCK_ = "LOCK:MEDIA:API:";
    public final static String DATA_STORY_CRAWLER_REDIS_COUNT_ = "DATA:STORY:CRAWLER:REDIS:COUNT:";

    public final static String DEFAULT_IMG = "MEDIA:CONSTANT:DEFAULT:IMG";
    public final static String PROMPT_CONSTANT = "MEDIA:CONSTANT:PROMPT:DALLE3";

    /**
     * 媒体重试次数
     */
    public final static String MEDIA_ALGORITHM_TIME_LIMIT = "MEDIA:ALGORITHM:LIMIT:TIME";
    /**
     * 媒体分析笔记数
     */
    public final static String MEDIA_ALGORITHM_LIMIT_NOTES = "MEDIA:ALGORITHM:LIMIT:NOTES";

    /**
     * 媒体分析最小时间
     */
    public final static String MEDIA_ALGORITHM_LIMIT_MIN_TIME_MS = "MEDIA:ALGORITHM:LIMIT:MIN:TIME:MS";

    public static final String DEFAULT_ALGORITHM_TIMES = "2";
    public static final String DEFAULT_ALGORITHM_NOTES = "10";
    public static final String DEFAULT_ALGORITHM_MIN_TIME_MS = String.valueOf(1000 * 60 * 60 * 24 * 5);


    public static final String DEFAULT_GPT_COUNT = "MEDIA:GPT:COUNT";
    public static final String CHAT_BOT_TOKEN = "CHAT:BOT:TOKEN";
}
