package com.bluefocus.kolmonitorservice.base.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yjLiu
 * @date: 0005 2024/8/5 14:34
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TokenCommon {

    public TokenCommon(String platform, String userName, String token) {
        this.platform = platform;
        this.userName = userName;
        this.token = token;
        if (null != token) {
            this.success = true;
        }
    }

    /**
     * 平台
     */
    private String platform;
    /**
     * 账号
     */
    private String userName;

    /**
     * token
     */
    private String token;

    /**
     * expireTime
     */
    private Long expireTime;

    /**
     * success
     */
    private Boolean success = false;
}
