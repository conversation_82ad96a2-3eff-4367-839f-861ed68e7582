package com.bluefocus.kolmonitorservice.base.util;

import org.redisson.api.*;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/23 11:43
 * @Description:
 */
@Component
public class RedisUtils {

    private final ConcurrentHashMap<String, RRateLimiter> limiterMap = new ConcurrentHashMap<>(8);

    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取 字符串
     *
     * @param name 名称
     * @return 返回 值
     */
    public RBucket<Object> getString(String name) {
        RBucket<Object> bucket = redissonClient.getBucket(name);
        Assert.notNull(bucket, "bucket is null");
        return bucket;
    }

    /**
     * add 字符串
     *
     * @param name 名称
     */
    public void addString(String name, String value) {
        getString(name).set(value);
    }

    /**
     * 获取 字符串 的 RSet
     *
     * @param name 名称
     * @return 返回 值
     */
    public RSet<String> getSet(String name) {
        RSet<String> rSet = redissonClient.getSet(name, StringCodec.INSTANCE);
        Assert.notNull(rSet, "rSet is null");
        return rSet;
    }

    /**
     * 新增 set 数据
     *
     * @param name  名称
     * @param value 值
     * @return 返回 是否新增成功
     */
    public boolean addSet(String name, String value) {
        return this.getSet(name).add(value);
    }

    /**
     * 新增批量set 数据
     *
     * @param name   名称
     * @param values 值
     * @return 返回 是否新增成功
     */
    public boolean addBatchSet(String name, Set<String> values) {
        return this.getSet(name).addAll(values);
    }

    /**
     * 读取 set 的数据
     *
     * @param name 名称
     * @return 返回 是否新增成功
     */
    public Set<String> readSetAll(String name) {
        return this.getSet(name).readAll();
    }

    /**
     * 验证 是否包含
     *
     * @param name  名称
     * @param value 值
     * @return 返回 是否 包含
     */
    public boolean containsSet(String name, String value) {
        return this.getSet(name).contains(value);
    }

    /**
     * 验证 是否包含all
     *
     * @param name   名称
     * @param values 值
     * @return 返回 是否 包含
     */
    public boolean containsSetAll(String name, List<String> values) {
        return this.getSet(name).containsAll(values);
    }


    public RLock getLock(String key) {
        return redissonClient.getLock(key);
    }

    /**
     * 获取 RateLimiter
     *
     * @param name 名称
     * @return 返回 值
     */
    public RRateLimiter getRateLimiter(String name) {
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(name);
        Assert.notNull(rateLimiter, "bucket is null");
        return rateLimiter;
    }

    public RRateLimiter getRateLimiter(String key, int count, int range, RateIntervalUnit unit) {
        RRateLimiter rateLimiter = limiterMap.get(key);
        if (unit == null) {
            unit = RateIntervalUnit.SECONDS;
        }
        if (rateLimiter == null || !rateLimiter.isExists()) {
            rateLimiter = getRateLimiter(key);
            rateLimiter.trySetRate(RateType.OVERALL, count, range, unit);
            limiterMap.put(key, rateLimiter);
        }
        return rateLimiter;

    }

    public <T, F> RMap<T, F> getMap(String key) {
        return redissonClient.getMap(key);
    }

    public RQueue<String> getQueue(String key) {
        return redissonClient.getQueue(key);
    }

    public RBlockingQueue<String> getBlockQueue(String key) {
        return redissonClient.getBlockingQueue(key);
    }

    public boolean addQueue(String key, String value) {
        RQueue<String> queue = redissonClient.getQueue(key);
        // 放回队列
        return queue.add(value);
    }
}


