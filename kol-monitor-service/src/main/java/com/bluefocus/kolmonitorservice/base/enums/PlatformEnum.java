package com.bluefocus.kolmonitorservice.base.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@Getter
@RequiredArgsConstructor
public enum PlatformEnum {

    /**
     * 全平台
     */
    ALL(0, "全平台"),
    XHS(1, "小红书"),
    BL<PERSON>(2, "哔哩哔哩"),
    KS(3, "快手"),
    DY(4, "抖音"),
    ;

    private final Integer index;
    private final String name;

    public static String codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (PlatformEnum e : PlatformEnum.values()) {
            if (e.getIndex().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }
}