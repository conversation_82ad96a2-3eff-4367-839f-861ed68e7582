package com.bluefocus.kolmonitorservice.base.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@RequiredArgsConstructor
@Getter
public enum ParameterEnum {

    /**
     * 参数枚举
     */
    READ("阅读量"),
    LIKE("点赞量"),
    COMMENT("评论量"),
    COLLECTION("收藏量"),
    INTERACTION("互动量"),
    SHARE("转发量"),
    BULLET("弹幕数"),
    FOLLOW("关注数"),
    COIN("投币数"),

    ;

    private final String name;


    @RequiredArgsConstructor
    @Getter
    public enum ArticleParameterEnum {
        /**
         * 枚举参数
         */

        INTERACTION_RATE("互动率"),
        FINISH_RATE("完播率"),
        ;

        private final String name;
    }
}