package com.bluefocus.kolmonitorservice.base.util;

import org.im4java.core.ConvertCmd;
import org.im4java.core.IMOperation;
import org.im4java.core.Stream2BufferedImage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;

public class MagickUtils {
    private static final Logger logger = LoggerFactory.getLogger(MagickUtils.class);

    public static BufferedImage imageTransparentColor(String input) {
        return imageTransparentColor(input, "white", 30D);
    }

    /**
     * 图片透明
     *
     * @param input 图片地址
     * @param color 要扣除的颜色
     * @param fuzz  相似颜色读
     * @return 图片流
     */
    public static BufferedImage imageTransparentColor(String input, String color, Double fuzz) {
        try {
            IMOperation im = new IMOperation();
            im.addImage(input);
            im.fuzz(fuzz, true);
            im.transparent(color);
            im.addImage("png:-");
            ConvertCmd cmd = new ConvertCmd();
            Stream2BufferedImage ImageStream = new Stream2BufferedImage();
            cmd.setOutputConsumer(ImageStream);
            cmd.run(im);
            return ImageStream.getImage();
        } catch (Exception e) {
            logger.error("URL: {} color: {} fuzz: {} 抠图异常 ", input, color, fuzz, e);
        }
        return null;
    }


    public static void main(String[] args) {
        try {
            String input = "https://cdn.wordart.com/static/creator/images/shapes/thumbs/cloud3.png";
            String output = "./tmpfile.png";
            BufferedImage image = imageTransparentColor(input,"white", 30D);
            ImageIO.write(image, "PNG", new File(output));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
