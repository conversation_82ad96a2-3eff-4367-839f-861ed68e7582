package com.bluefocus.kolmonitorservice.base.enums;

/**
 * 删除状态
 *
 * <AUTHOR>
 */
public enum DeleteStatusEnum {
    UNDELETE(0, "未删除"),
    DELETE(1, "已删除");

    private int code;
    private String desc;

    DeleteStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
