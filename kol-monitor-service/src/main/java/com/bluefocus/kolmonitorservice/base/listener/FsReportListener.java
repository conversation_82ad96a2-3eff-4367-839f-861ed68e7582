package com.bluefocus.kolmonitorservice.base.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDataTrendDomainService;
import com.bluefocus.kolmonitorservice.domain.project.entity.ProjectDataTrend;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;


@Service
@Log4j2
@RequiredArgsConstructor
public class FsReportListener implements TopicStrategy {

    private static final String TOPIC_NAME = "dws-fs-report";

    private final ProjectDataTrendDomainService projectDataTrendDomainService;

    @Override
    public String getTopicType() {
        return TOPIC_NAME;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {
        Map map = (Map) JSON.parse(message);
        if (ObjectUtil.isEmpty(map.get("project_id"))) {
            log.error("topic=dws-fs-report,有空数据，data：[{}]", message);
            return;
        }
        Long projectId = Long.valueOf(map.get("project_id").toString());

        ProjectDataTrend projectDataTrend = projectDataTrendDomainService.getById(projectId);

        if (ObjectUtil.isEmpty(projectDataTrend)) {
            projectDataTrend = new ProjectDataTrend();
            projectDataTrend.setProjectId(projectId);
            projectDataTrend.setCreateTime(LocalDateTime.now());
        }
        projectDataTrend.setPlantData(ObjectUtil.isEmpty(map.get("plant_data")) ? String.valueOf(Collections.EMPTY_LIST) : map.get("plant_data").toString());
        projectDataTrend.setPlantNotesRate(ObjectUtil.isEmpty(map.get("plant_notes_rate")) ? String.valueOf(Collections.EMPTY_LIST) : map.get("plant_notes_rate").toString());
        projectDataTrend.setPlantReadRate(ObjectUtil.isEmpty(map.get("plant_read_rate")) ? String.valueOf(Collections.EMPTY_LIST) : map.get("plant_read_rate").toString());
        projectDataTrend.setUpdateTime(LocalDateTime.now());
        projectDataTrend.setConsumeTime(ObjectUtil.isEmpty(map.get("create_time")) ? null : Long.valueOf(map.get("create_time").toString()));

        projectDataTrendDomainService.saveOrUpdate(projectDataTrend);
    }
}