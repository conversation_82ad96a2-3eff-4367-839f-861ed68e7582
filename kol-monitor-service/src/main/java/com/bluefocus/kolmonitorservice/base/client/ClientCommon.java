package com.bluefocus.kolmonitorservice.base.client;

import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;

public class ClientCommon {

    public static <T extends Response> T check(boolean needCheckRequest, Request<T> request) throws ApiException {
        if (needCheckRequest) {
            try {
                request.check();
            } catch (ApiRuleException e) {
                T tRsp;
                try {
                    tRsp = request.getResponseClass().newInstance();
                } catch (Exception xe) {
                    throw new ApiException(xe);
                }
                tRsp.setCode(e.getErrCode());
                tRsp.setMsg(e.getErrMsg());
                return tRsp;
            }
        }
        return null;
    }
}
