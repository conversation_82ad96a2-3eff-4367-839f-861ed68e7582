//package com.bluefocus.kolmonitorservice.base.util;
//
//import com.google.gson.TypeAdapter;
//import com.google.gson.stream.JsonReader;
//import com.google.gson.stream.JsonWriter;
//
//import java.io.IOException;
//import java.util.TreeMap;
//
///**
// * @author: yjLiu
// * @date: 0007 2024/8/7 19:05
// * @description:
// */
//public class TreeMapTypeAdapter extends TypeAdapter<TreeMap<String, Object>> {
//    @Override
//    public void write(JsonWriter out, TreeMap<String, Object> value) throws IOException {
//        out.beginObject();
//        for (String key : value.keySet()) {
//            Object value1 = value.get(key);
//            if (value1 instanceof Integer) {
//                value1 = ((Integer) value1).doubleValue();
//                out.name(key).value(Integer.valueOf(value1.toString()));
//            } else {
//                out.name(key).value(value1);
//            }
//
//        }
//        out.endObject();
//    }
//
//    @Override
//    public TreeMap<String, Object> read(JsonReader in) throws IOException {
//        TreeMap<String, Object> map = new TreeMap<>();
//        in.beginObject();
//        while (in.hasNext()) {
//            String key = in.nextName();
//            Object value = in.nextDouble();
//            if (value.equals(Integer.valueOf(value.toString()))) {
//                value = Integer.valueOf(value.toString());
//            }
//            map.put(key, value);
//        }
//        in.endObject();
//        return map;
//    }
//}
