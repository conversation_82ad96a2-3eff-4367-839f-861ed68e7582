package com.bluefocus.kolmonitorservice.base.listener.media;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.application.service.media.impl.ApiMediaService;
import com.bluefocus.kolmonitorservice.application.service.media.impl.DataHandleState;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.HandleTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.TopicStrategy;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTask;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.MessageHeaders;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @author: yjLiu
 * @date: 0012 2024/6/12 17:37
 * @description: api队列
 * <p>
 * 一个平台的数据
 * 声量趋势、互动量趋势 - 并行2
 * 总声量 - 串行1
 * 笔记原帖、情感分布、笔记词云 -并行3
 * 耗时：3 * 3 = 9 秒
 * <p>
 * 平台 并行度 6
 * </p>
 * <p>
 * 一个对象三个平台 需要并发执行
 * 耗时 9秒
 * 总 并行度 6*3 = 18
 * </p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaHandleApiListener implements TopicStrategy {

    private final ApiMediaService apiMediaService;
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final MediaTaskDomainService mediaTaskDomainService;
    private final ThreadPoolTaskScheduler scheduledExecutorPool;
    private final FsRobotUtil fsRobotUtil;
    private final RedisUtils redisUtils;

    public final static String TOPIC = "media-handle-api";

    @Value("${robot.url}")
    private String robotUrl;
    @Value("${robot.developer}")
    private String devRobotUrl;

    @Override
    public String getTopicType() {
        return TOPIC;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {

        MediaMsgBody mediaMsgBody = JSON.parseObject(message, MediaMsgBody.class);

        Long objId = mediaMsgBody.getId();
        Long taskId = mediaMsgBody.getMediaTaskId();
        Long handId = mediaMsgBody.getHandId();
        if (handId == null && objId == null) {
            log.error("数说api消费当前任务对象为null：跳过,message={}", message);
            return;
        }

        MediaTaskHandle mediaTaskHandle = mediaTaskHandleDomainService.getById(handId);
        // 主从事务校验
        if (ObjectUtil.isNull(mediaTaskHandle)) {
            RBucket<Object> notCountBucket = redisUtils.getString(RedisKeyComm.MEDIA_API_HANDLE_NOT_);
            int notCount = 0;
            if (!notCountBucket.isExists()) {
                notCountBucket.set(1, 10, TimeUnit.MINUTES);
            } else {
                notCount = Integer.parseInt(notCountBucket.get().toString());
                notCountBucket.set(notCount + 1, 10, TimeUnit.MINUTES);
            }

            if (notCount > getRetryLimit()) {
                MediaObjects obj = mediaObjectsDomainService.getById(objId);
                MediaTaskHandle retryHandle = mediaTaskHandleDomainService.getById(handId);
                if (null == obj || null == retryHandle) {
                    fsRobotUtil.sendRobotMsg(devRobotUrl, String.format("超时预警(api消费次数过多)：api队列%s次,，当前处理id=%s", notCount, handId));
                    return;
                } else {
                    handFail(retryHandle, obj, mediaMsgBody, retryHandle.getApiRetry());
                }
                return;
            } else {
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException ignored) {
                } finally {
                    apiMediaService.handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
                }
            }
            log.warn("数说api消费当前任务对象为null：跳过,handle={}", mediaMsgBody.getHandId());
            return;
        }
        mediaMsgBody = new MediaMsgBody(objId, taskId, this.getClass().getSimpleName(), mediaTaskHandle.getId());

        MediaObjects mediaObject = mediaObjectsDomainService.getById(mediaTaskHandle.getMediaObjectsId());
        if (!mediaTaskHandle.getStatus().equals(FinishStatusEnum.CRAWLER.getCode()) || MediaEditStatusEnum.sucCheck(mediaObject.getStatus())) {
            apiMediaService.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
            log.info("数说api当前任务对象采集部分已经结束,直接跳过.任务对象={}", message);
            return;
        }

        Integer apiRetry = mediaTaskHandle.getApiRetry();
        if (apiRetry > getRetryLimit()) {
            // 预警 任务排队失败
            handFail(mediaTaskHandle, mediaObject, mediaMsgBody, apiRetry);
            return;
        }

        boolean requireTask = false;
        boolean requireRetryApi = false;
        RLock handleLock = null;
        try {
            apiRetry++;

            handleLock = redisUtils.getLock(String.format(RedisKeyComm.LOCK_MEDIA_HANDLE_API_, mediaMsgBody.getHandId()));
            if (handleLock.isLocked() || !handleLock.tryLock(30, TimeUnit.SECONDS)) {
                requireRetryApi = true;
                log.warn("数说api当前对象handle竞争锁失败，跳过;handleId={},task={},当前次数={}", mediaTaskHandle.getId(), mediaMsgBody.getMediaTaskId(), mediaTaskHandle.getApiRetry());
                return;
            }
            long startTime = System.currentTimeMillis();
            DataHandleState handle = apiMediaService.handle(mediaObject, mediaTaskHandle);

            // 重新获取数据
            mediaTaskHandle = mediaTaskHandleDomainService.getById(mediaTaskHandle.getId());
            mediaTaskHandle.setApiTime(System.currentTimeMillis() - startTime);
            if (mediaTaskHandle.getStatus().equals(FinishStatusEnum.FAIL.getCode())) {
                // 任务失败
                if (mediaTaskHandle.getType().equals(HandleTypeEnum.CREAT.getType())) {
                    apiMediaService.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
                } else {
                    MediaTask mediaTask = mediaTaskDomainService.getById(mediaObject.getMediaTaskId());
                    apiMediaService.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
                    sendRobotMsg(mediaTaskHandle, apiRetry, mediaTask);
                }
                return;
            }

            if (handle.getCloudWord() && handle.getVolume()) {
                apiMediaService.handleMq(mediaMsgBody, MediaHandleWordCloudListener.TOPIC);
            }

            // 所有成功
            if (handle.checkAllState()) {
                mediaTaskHandle.setStatus(FinishStatusEnum.ANALY.getCode());
                requireTask = true;
                mediaTaskHandle.setUpdateTime(LocalDateTime.now());
                mediaTaskHandle.setCralwerFinishTime(LocalDateTime.now());
            } else {
                // 回转自己
                requireRetryApi = true;
//                mediaTaskHandle.setApiErrMsg(JSON.toJSONString(handle));
            }
            mediaTaskHandle.setApiRetry(apiRetry);
            mediaTaskHandleDomainService.updateById(mediaTaskHandle);
        } catch (Exception e) {
            requireRetryApi = true;
            log.warn("Ai影响力media-handle-api消息消费失败：" + e);
            mediaTaskHandle.setApiRetry(apiRetry);
            mediaTaskHandleDomainService.updateById(mediaTaskHandle);
        } finally {
            try {
                if (handleLock != null && handleLock.isLocked()) {
                    handleLock.unlock();
                }
            } catch (Exception ignored) {
            }
            if (requireTask) {
                // 发送请求去判断当前任务所有对象是否跑完
                apiMediaService.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
                apiMediaService.handleMq(mediaMsgBody, MediaHandleNoteDayListener.TOPIC);
                apiMediaService.handleMq(mediaMsgBody, MediaHandleWordCloudListener.TOPIC);
            }
            if (requireRetryApi) {
                MediaMsgBody finalMediaMsgBody = mediaMsgBody;
                scheduledExecutorPool.schedule(() -> apiMediaService.handleMq(finalMediaMsgBody, MediaHandleApiListener.TOPIC)
                        , new Date(System.currentTimeMillis() + 1000 * 60));
            }
        }
    }

    private void handFail(MediaTaskHandle mediaTaskHandle, MediaObjects mediaObject, MediaMsgBody mediaMsgBody, Integer apiRetry) {
        mediaTaskHandle.setStatus(FinishStatusEnum.FAIL.getCode());
        mediaTaskHandle.setUpdateTime(LocalDateTime.now());
        mediaTaskHandleDomainService.updateById(mediaTaskHandle);

        mediaObject.setStatus(mediaTaskHandle.getType().equals(HandleTypeEnum.CREAT.getType()) ? MediaEditStatusEnum.FAIL.getCode() : MediaEditStatusEnum.EDIT_FAIL.getCode());
        mediaObjectsDomainService.updateById(mediaObject);
        apiMediaService.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
        MediaTask mediaTask = mediaTaskDomainService.getById(mediaObject.getMediaTaskId());
        sendRobotMsg(mediaTaskHandle, apiRetry, mediaTask);
    }

    private void sendRobotMsg(MediaTaskHandle handle, Integer apiRetry, MediaTask task) {
        long between = ChronoUnit.MINUTES.between(handle.getCreateTime(), LocalDateTime.now());
        fsRobotUtil.sendRobotMsg(robotUrl
                , String.format("当前分析对象采集数据失败：任务id[%s],任务名称[%s] \nhandleId=%s,处理类型=%s,关键词=%s \n对象分析已进行%s分钟,api队列失败%s次,爬虫队列失败%s次"
                        , handle.getTaskId(), task.getName(), handle.getId(), handle.getType(), handle.getKeyword(), between, apiRetry, handle.getCralwerRetry()));
    }

    private int getRetryLimit() {
        RBucket<Object> string = redisUtils.getString(RedisKeyComm.MEDIA_API_LIMIT_RETRY);
        if (!string.isExists()) {
            string.set(3);
        }
        return Integer.parseInt(string.get().toString());
    }

}
