package com.bluefocus.kolmonitorservice.base.listener.media;

import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAlgorithmStart;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.TopicStrategy;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

/**
 * @author: yjLiu
 * @date: 0012 2024/6/12 17:37
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaHandleAnalyzeListener implements TopicStrategy {

    public final static String TOPIC = "media-handle-analyze";
    private final MediaAlgorithmStart mediaAlgorithmStart;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;

    @Override
    public String getTopicType() {
        return TOPIC;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {
        try {
            // 失败也要生成分析
            MediaMsgBody mediaMsgBody = new Gson().fromJson(message, MediaMsgBody.class);
            if (mediaMsgBody.getHandId() == null) {
                log.error("传参错误，message={}", message);
                return;
            }
            MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(mediaMsgBody.getHandId());
            if (!taskHandle.getPause() || taskHandle.getStatus() >= FinishStatusEnum.FINISH.getCode()) {
                log.info("算法分析已经结束，跳过, handleId={}", taskHandle.getId());
                return;
            }
            mediaAlgorithmStart.handle(taskHandle, null);
        } catch (Exception e) {
            log.error("消费消息异常: {}", message, e);
        }
    }
}
