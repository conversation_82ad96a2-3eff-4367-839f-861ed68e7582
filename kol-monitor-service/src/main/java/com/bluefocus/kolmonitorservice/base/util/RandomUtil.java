package com.bluefocus.kolmonitorservice.base.util;

import java.util.UUID;

/**
 * @Describe:
 * @Author: liuyj
 * @Date: 2022-07-19 15:59
 */
public class RandomUtil {

    //生成签名的随机串
    public static String createNonceStr() {
        return UUID.randomUUID().toString();
    }

    //生成签名的时间戳
    public static String createTimestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }
}
