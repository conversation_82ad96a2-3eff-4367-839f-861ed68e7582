package com.bluefocus.kolmonitorservice.base.listener.media;

import com.bluefocus.kolmonitorservice.application.service.media.impl.CrawlerMediaService;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.TopicStrategy;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.MessageHeaders;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * @author: yjLiu
 * @date: 0012 2024/6/12 17:37
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaHandleCrawlerListener implements TopicStrategy {

    public final static String TOPIC = "media-handle-crawler";

    private final CrawlerMediaService crawlerMediaService;
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final ThreadPoolTaskScheduler scheduledExecutorPool;
    private final FsRobotUtil fsRobotUtil;
    @Value("${robot.developer}")
    private String robotUrl;

    private final RedisUtils redisUtils;

    @Override
    public String getTopicType() {
        return TOPIC;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {

        MediaMsgBody mediaMsgBody = new Gson().fromJson(message, MediaMsgBody.class);
        Long objId = mediaMsgBody.getId();
        Long taskId = mediaMsgBody.getMediaTaskId();
        Long handId = mediaMsgBody.getHandId();
        if (handId == null) {
            log.error("数说爬虫消费当前任务对象为null：跳过,message={}", message);
            return;
        }

        MediaTaskHandle mediaTaskHandle = mediaTaskHandleDomainService.getById(handId);
        MediaObjects mediaObject = mediaObjectsDomainService.getById(objId);
        // 事务校验
        if (null == mediaTaskHandle || mediaObject == null) {
            try {
                int notCount = getHandleNotCountTry();
                if (notCount > getRetryCount()) {
                    MediaObjects obj = mediaObjectsDomainService.getById(objId);
                    long between = ChronoUnit.MINUTES.between(obj.getCreateTime(), LocalDateTime.now());
                    fsRobotUtil.sendRobotMsg(robotUrl, String.format("超时预警(爬虫消费次数过多)：任务分析已进行%s分钟,爬虫队列%s次，当前任务id=%s，分析对象id=%s"
                            , between, notCount, obj.getMediaTaskId(), obj.getId()));
                }
                Thread.sleep(1000L);
            } catch (InterruptedException ignored) {
            } finally {
                crawlerMediaService.handleMq(mediaMsgBody, MediaHandleCrawlerListener.TOPIC);
            }
            log.warn("数说爬虫消费当前任务对象为null：跳过,handle={}", mediaMsgBody.getHandId());
            return;
        }
        mediaMsgBody = new MediaMsgBody(objId, taskId, this.getClass().getSimpleName(), mediaTaskHandle.getId());

        if (!mediaTaskHandle.getStatus().equals(FinishStatusEnum.CRAWLER.getCode()) || MediaEditStatusEnum.sucCheck(mediaObject.getStatus())) {
            crawlerMediaService.handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
            return;
        }

        try {
            // 校验爬虫重试时间 次数
            long between = ChronoUnit.MINUTES.between(mediaObject.getCreateTime(), LocalDateTime.now());
            if (between > getRetryTime() || mediaTaskHandle.getCralwerRetry() > getRetryCount()) {
                fsRobotUtil.sendRobotMsg(robotUrl, String.format("调用爬虫数说聚合重试%s次,不再调用爬虫处理,后续已转发API：\n任务id=%s,处理单元=%s,关键词=%s", mediaTaskHandle.getCralwerRetry()
                        , mediaMsgBody.getMediaTaskId()
                        , mediaTaskHandle.getId(), mediaTaskHandle.getKeyword()));
                crawlerMediaService.handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
                return;
            }
            crawlerMediaService.doHandle(mediaObject, mediaTaskHandle);
        } catch (Exception e) {
            log.warn("Ai影响力爬虫media-crawler-data消息消费失败:", e);
            MediaMsgBody finalMediaMsgBody = mediaMsgBody;
            scheduledExecutorPool.schedule(() -> crawlerMediaService.handleMq(finalMediaMsgBody, MediaHandleCrawlerListener.TOPIC)
                    , new Date(System.currentTimeMillis() + 1000 * 60));
        } finally {
            mediaTaskHandle.setCralwerRetry(mediaTaskHandle.getCralwerRetry() + 1);
            mediaTaskHandleDomainService.updateById(mediaTaskHandle);
        }
    }

    private int getHandleNotCountTry() {
        RBucket<Object> notCountBucket = redisUtils.getString(RedisKeyComm.MEDIA_CRAWLER_HANDLE_NOT_);
        int notCount = 0;
        if (!notCountBucket.isExists()) {
            notCountBucket.set(1, 10, TimeUnit.MINUTES);
        } else {
            notCount = Integer.parseInt(notCountBucket.get().toString());
            notCountBucket.set(notCount + 1, 10, TimeUnit.MINUTES);
        }
        return notCount;
    }

    private int getRetryTime() {
        RBucket<Object> string = redisUtils.getString(RedisKeyComm.MEDIA_CRAWLER_RETRY_TIME);
        if (!string.isExists()) {
            string.set(10);
        }
        return Integer.parseInt(string.get().toString());
    }

    private int getRetryCount() {
        RBucket<Object> string = redisUtils.getString(RedisKeyComm.MEDIA_CRAWLER_LIMIT_RETRY);
        if (!string.isExists()) {
            string.set(2);
        }
        return Integer.parseInt(string.get().toString());
    }
}
