package com.bluefocus.kolmonitorservice.base.common;

import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * @author: yjLiu
 * @date: 0020 2024/6/20 17:52
 * @description:
 */
@Getter
@Setter
@AllArgsConstructor
public class MediaMsgBody {

    /**
     * 主键 对象id
     */
    private Long id;

    /**
     * 媒体任务ID
     */
    private Long mediaTaskId;

    public MediaMsgBody(Long id, Long mediaTaskId, String classSource,Long handId) {
        this.id = id;
        this.mediaTaskId = mediaTaskId;
        this.classSource = classSource;
        this.handId = handId;
    }

    /**
     * 消息消费次数
     */
    private Long times;

    /**
     * 消息来源
     */
    private String classSource;

    /**
     * handId 处理单元id
     */
    private Long handId;

    public static MediaMsgBody getMediaMsgBody(MediaTaskHandle handle, String className) {
        return new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), className, handle.getId());
    }
}
