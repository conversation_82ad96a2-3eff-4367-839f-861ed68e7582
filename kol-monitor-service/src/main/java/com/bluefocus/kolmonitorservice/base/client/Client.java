package com.bluefocus.kolmonitorservice.base.client;

import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;

/**
 * <AUTHOR>
 */
public interface Client {

    /**
     * 执行接口.
     *
     * @param request 请求参数
     * @param <V>     请求类
     * @param <W>     响应类
     * @return w
     * @throws ApiException api异常
     * @detail 三方调用
     */
    <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException;
}
