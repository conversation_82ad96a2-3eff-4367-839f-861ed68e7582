package com.bluefocus.kolmonitorservice.base.listener;

import com.bluefocus.kolmonitorservice.base.listener.media.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.handler.annotation.Headers;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

import java.util.concurrent.Semaphore;


@Component
//@ConditionalOnProperty(name = "kafka.listener.enabled", havingValue = "true")
public class ListenerQueue {
    private final static Logger logger = LoggerFactory.getLogger(ListenerQueue.class);
    public static final int LIMIT_NUM = 2;
    private final Semaphore semaphore;

    public ListenerQueue() {
        this.semaphore = new Semaphore(LIMIT_NUM);
    }

    public void listenerQueue(String message, MessageHeaders headers) {
        String topicType = String.valueOf(headers.get("kafka_receivedTopic"));
        try {
            semaphore.acquire();//获取共享资源，如果计数器为0会等待
            logger.info("获取成功，消息进入队列处理开始..... ");
            TopicStrategy strategy = TopicFactory.getTopic(topicType);
            strategy.consumptionData(message, headers);

        } catch (Exception e) {
            logger.info("TOPIC: {}, 消息处理异常 {}", topicType, e);
        } finally {
            logger.info("处理完毕，释放队列..... ");
            semaphore.release();//放在finally语句块表示不管发不发生异常都会执行，都会释放资源。
        }
    }

    @KafkaListener(topics = "#{'${spring.kafka.topics}'.split('\\\\ ')}", concurrency = "3")
    public void platformSimilarKolListen(@Payload String message, @Headers MessageHeaders headers) {
        logger.info("Topic= {} 收到消息  message= {}", headers.get("kafka_receivedTopic"), message);
        listenerQueue(message, headers);
        logger.info("Topic= {} 消息处理完毕", headers.get("kafka_receivedTopic"));
    }

    @KafkaListener(topics = MediaHandleWordCloudListener.TOPIC, concurrency = "1")
    public void mediaWordCloudListener(@Payload String message, @Headers MessageHeaders headers) {
        mediaQueue(message, headers);
    }

    @KafkaListener(topics = MediaHandleAnalyzeListener.TOPIC, concurrency = "1")
    public void mediaAnalyzeListener(@Payload String message, @Headers MessageHeaders headers) {
        mediaQueue(message, headers);
    }

    @KafkaListener(topics = MediaHandleApiListener.TOPIC, concurrency = "1")
    public void mediaHandleApiListener(@Payload String message, @Headers MessageHeaders headers) {
        mediaQueue(message, headers);
    }

    @KafkaListener(topics = MediaHandleCrawlerListener.TOPIC, concurrency = "1")
    public void mediaHandleCrawlerListener(@Payload String message, @Headers MessageHeaders headers) {
        mediaQueue(message, headers);
    }

    @KafkaListener(topics = MediaHandleTaskListener.TOPIC, concurrency = "1")
    public void mediaTaskListener(@Payload String message, @Headers MessageHeaders headers) {
        mediaQueue(message, headers);
    }

    @KafkaListener(topics = MediaHandleNoteDayListener.TOPIC, concurrency = "1")
    public void mediaNoteDayListener(@Payload String message, @Headers MessageHeaders headers) {
        mediaQueue(message, headers);
    }


    public void mediaQueue(String message, MessageHeaders headers) {
        String topicType = String.valueOf(headers.get("kafka_receivedTopic"));
        try {
            semaphore.acquire();//获取共享资源，如果计数器为0会等待
            logger.info("TOPIC= {} 收到消息  message= {}", topicType, message);
            TopicStrategy strategy = TopicFactory.getTopic(topicType);
            strategy.consumptionData(message, headers);

        } catch (Exception e) {
            logger.error("TOPIC: {}, 消息处理异常 {}", topicType, e);
        } finally {
            logger.info("TOPIC= {} 消息处理完毕", topicType);
            semaphore.release();//放在finally语句块表示不管发不发生异常都会执行，都会释放资源。
        }
    }
}