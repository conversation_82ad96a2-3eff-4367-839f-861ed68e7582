package com.bluefocus.kolmonitorservice.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: yjLiu
 * @date: 0019 2024/11/19 11:12
 * @description:
 */
@Getter
@AllArgsConstructor
public enum SentimentEnum {
    /**
     * 情感
     */
    POSITIVE("1", "正向"),
    NEGATIVE("-1", "负向"),
    NEUTRAL("0", "中性");
    private final String code;
    private final String desc;
    private final static SentimentEnum[] VALUES = SentimentEnum.values();

    public static String codeOf(String code) {
        for (SentimentEnum sentimentEnum : VALUES) {
            if (sentimentEnum.getCode().equals(code)) {
                return sentimentEnum.getDesc();
            }
        }
        return null;
    }
}
