package com.bluefocus.kolmonitorservice.base.listener.media;

import cn.hutool.core.util.ObjectUtil;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAlgorithmStart;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaWordArtStart;
import com.bluefocus.kolmonitorservice.application.service.media.impl.StrategyFactory;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.HandleTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.TopicStrategy;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTask;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0012 2024/6/12 17:37
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaHandleTaskListener implements TopicStrategy {

    public final static String TOPIC = "media-handle-task";
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final MediaTaskDomainService mediaTaskDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final FsRobotUtil fsRobotUtil;
    private final StrategyFactory strategyFactory;

    private final MediaWordArtStart mediaWordArtStart;
    private final MediaAlgorithmStart mediaAlgorithmStart;

    @Value("${robot.developer}")
    private String devRobotUrl;

    @Override
    public String getTopicType() {
        return TOPIC;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {
        MediaMsgBody mesObj = new Gson().fromJson(message, MediaMsgBody.class);
        mesObj.setClassSource(this.getClass().getSimpleName());

        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(mesObj.getHandId());
        MediaObjects obj = mediaObjectsDomainService.getById(mesObj.getId());
        MediaTask taskObj = mediaTaskDomainService.getById(obj.getMediaTaskId());

        if (ObjectUtil.isEmpty(mesObj.getHandId()) || ObjectUtil.isEmpty(handle)
                || ObjectUtil.isEmpty(obj)
                || ObjectUtil.isEmpty(taskObj)) {
            fsRobotUtil.sendRobotMsg(devRobotUrl, String.format("media-handle-task 消息处理失败 message=【%s】 在MediaTaskHandle中不存在", message));
            return;
        }

        if (!handle.getPause()) {
            log.warn("media-handle-task: handle已经失效，直接跳过, message={}", message);
            return;
        }

        if (handle.getType().equals(HandleTypeEnum.CREAT.getType())) {
            creatTask(taskObj, handle, mesObj);
        } else {
            editTask(taskObj, handle);
        }

    }

    private void editTask(MediaTask task, MediaTaskHandle handle) {
        if (handle.getStatus() <= FinishStatusEnum.FINISH.getCode()) {
            checkAlgWord(handle);
        }
        List<MediaTaskHandle> handleList = mediaTaskHandleDomainService.getRunningTaskHandleByTask(task.getId(), null);
        if (task.getStatus().equals(FinishStatusEnum.FAIL.getCode())) {
            if (handleList.stream().allMatch(h -> h.getStatus().equals(FinishStatusEnum.FINISH.getCode()))) {
                task.setStatus(FinishStatusEnum.FINISH.getCode());
                mediaTaskDomainService.updateById(task);
            }
        } else if (task.getStatus().equals(FinishStatusEnum.FINISH.getCode())) {
            if (handleList.stream().anyMatch(h -> h.getStatus().equals(FinishStatusEnum.FAIL.getCode()))) {
                task.setStatus(FinishStatusEnum.FAIL.getCode());
                mediaTaskDomainService.updateById(task);
            }
        }
    }

    private void creatTask(MediaTask task, MediaTaskHandle handle, MediaMsgBody mesObj) {
        checkHandleFinish(handle, mesObj);

        if (task.getStatus().equals(FinishStatusEnum.FAIL.getCode()) || task.getStatus().equals(FinishStatusEnum.FINISH.getCode())) {
            log.info("当前处理任务已经完成，直接跳过，handle={}", handle.getId());
            return;
        }

        // status = 0
        if (task.getStatus() == FinishStatusEnum.CRAWLER.getCode()) {
            // 判断分析对象 数说数据是否采集完整 如果完整 更新任务状态
            strategyFactory.convertTaskState(task);
            task = mediaTaskDomainService.getById(handle.getTaskId());
        }

        // status = 1 分析中
        if (task.getStatus() == FinishStatusEnum.ANALY.getCode()) {
            List<MediaTaskHandle> handleList = mediaTaskHandleDomainService.getRunningTaskHandleByTask(task.getId(), null);

            List<MediaTaskHandle> creatHandleList = handleList.stream().filter(x -> HandleTypeEnum.CREAT.getType().equals(x.getType())).collect(Collectors.toList());
            int taskAllCount = creatHandleList.size();
            long failCount = creatHandleList.stream().filter(e -> e.getStatus() == FinishStatusEnum.FAIL.getCode()).count();
            long finishCount = creatHandleList.stream().filter(e -> e.getStatus() != FinishStatusEnum.CRAWLER.getCode())
                    .filter(e -> this.checkHandleFinish(e, mesObj)).count();

            // 状态更新为 2
            if (finishCount + failCount == taskAllCount) {
                MediaTask mediaTask = new MediaTask();
                mediaTask.setId(task.getId());
                mediaTask.setStatus(FinishStatusEnum.FINISH.getCode());
                mediaTask.setUpdateTime(LocalDateTime.now());
                mediaTaskDomainService.updateById(mediaTask);
            }
        }
    }

    private boolean checkHandleFinish(MediaTaskHandle handle, MediaMsgBody msg) {
        // 状态=完成/失败 任务状态=已失效
        if (handle.getStatus() == FinishStatusEnum.FINISH.getCode()
                || handle.getStatus() == FinishStatusEnum.FAIL.getCode()) {
            return true;
        }

        // 判断wordArt  是否全部初始化完成
        boolean isArtSuc = mediaWordArtStart.checkTaskWordArtIsInitByHandleIds(Collections.singletonList(handle.getId()));
        boolean isAlgorithmSuc = mediaAlgorithmStart.checkAlgorithmSuccess(handle);

        if (isArtSuc && isAlgorithmSuc) {
            updateHandleSuccess(handle);
            return true;
        }

        // 如果状态未完成 或 更新词云时间失败 则重发mq
        if (!isArtSuc || !mediaWordArtStart.updateArtStatus(msg)) {
            mediaWordArtStart.handleMq(msg);
        }

        return false;
    }

    private void checkAlgWord(MediaTaskHandle handle) {
        // 判断wordArt  是否全部初始化完成
        boolean isArtSuc = mediaWordArtStart.checkTaskWordArtIsInitByHandleIds(Collections.singletonList(handle.getId()));
        boolean isAlgorithmSuc = mediaAlgorithmStart.checkAlgorithmSuccess(handle);
        if (isArtSuc && isAlgorithmSuc) {
            updateHandleSuccess(handle);
        }
    }

    /**
     * 分析成功
     */
    private void updateHandleSuccess(MediaTaskHandle handle) {
        MediaTaskHandle entity = new MediaTaskHandle();
        entity.setId(handle.getId());
        entity.setTaskId(handle.getTaskId());
        entity.setMediaObjectsId(handle.getMediaObjectsId());
        entity.setStatus(FinishStatusEnum.FINISH.getCode());
        entity.setFinishTime(LocalDateTime.now());
        mediaTaskHandleDomainService.updateById(entity);

        MediaObjects obj = mediaObjectsDomainService.getById(handle.getMediaObjectsId());
        obj.setStatus(handle.getType().equals(HandleTypeEnum.CREAT.getType()) ? MediaEditStatusEnum.FINISH.getCode() : MediaEditStatusEnum.EDIT_FINISH.getCode());
        obj.setFinishTime(LocalDateTime.now());
        mediaObjectsDomainService.updateById(obj);
    }
}