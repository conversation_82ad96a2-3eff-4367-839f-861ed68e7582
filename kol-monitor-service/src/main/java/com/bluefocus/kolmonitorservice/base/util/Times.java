package com.bluefocus.kolmonitorservice.base.util;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * @ClassName Times
 * @Description 时间格式转换
 */
public final class Times {

    public static String DATE_PATTERN = "yyyy-MM-dd";
    public static String DATE_PATTERN_FORM = "yyyyMMdd";
    public static String TIME_PATTREN = "yyyy-MM-dd HH:mm:ss";
    private static final String TIME_STR = "yyyyMMddHHmmss";
    public static String DATE_CHINE = "yyyy年MM月dd日";
    public static String TIME_PATTERN = "HH:mm";

    public static long toEpochMilli(LocalDateTime time) {
        return time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static long toEpochSecond(LocalDateTime time) {
        return time.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    public static LocalDateTime toLocalDateTime(long epochMilli) {
        return Instant.ofEpochMilli(epochMilli).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static Date toDate(LocalDateTime time) {
        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String dateTimeToStringFormat(LocalDateTime time) {
        DateTimeFormatter dtf2 = DateTimeFormatter.ofPattern(TIME_PATTREN);
        return dtf2.format(time);
    }

    public static Date stringToDate(String dateTime) {
        LocalDate localDate = LocalDate.parse(dateTime, DateTimeFormatter.ofPattern(TIME_PATTREN));
        ZoneId zone = ZoneId.systemDefault();
        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();
        Date date = Date.from(instant);
        return date;
    }

    public static Date getLongToDate(Long dateTime) {
        Date date = new Date(dateTime);
        return date;
    }

    /**
     * @param date 格式 yyyy-MM-dd
     * @return LocalDate
     */
    public static LocalDate strToLocalDate(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern(DATE_PATTERN));
    }

    /**
     * @param date 格式 yyyyMMdd
     * @return LocalDate
     */
    public static LocalDate strToLocalDay(String date) {
        return LocalDate.parse(date, DateTimeFormatter.ofPattern(DATE_PATTERN_FORM));
    }

    /**
     * @param dateTime 格式 yyyy-MM-dd HH:mm:ss
     * @return LocalDateTime
     */
    public static LocalDateTime strToLocalDateTime(String dateTime) {
        return LocalDateTime.parse(dateTime, DateTimeFormatter.ofPattern(TIME_PATTREN));
    }

    /**
     * @param  date    日期.
     * @return String  格式 yyyy年MM月dd日
     */
    public static String dateToChineString(LocalDate date) {
        return date.format(DateTimeFormatter.ofPattern(DATE_CHINE));
    }

   /**
     * @param  time    时间.
     * @return String  格式  HH:mm
     */
    public static String timeToChineString(LocalTime time) {
        return time.format(DateTimeFormatter.ofPattern(TIME_PATTERN));
    }


    public static void main(String[] args) {

//        List<String> list = new ArrayList<String>();
////        list.add("a");
////        list.add("b");
////        list.add("c");
//
//
//        String str = Joiner.on(",").join(list);
    }

}
