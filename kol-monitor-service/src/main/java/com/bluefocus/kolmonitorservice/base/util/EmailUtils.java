package com.bluefocus.kolmonitorservice.base.util;

import com.bluefocus.kolmonitorservice.base.common.EmailCommon;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeUtility;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;


@Component
public class EmailUtils {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    ThreadPoolExecutor threadPoolExecutor;
    @Resource
    private JavaMailSender sender;
    @Resource
    private KafkaSender kafkaSender;
    @Value("${spring.mail.username}")
    private String from;
    @Value("${spring.mail.retry}")
    private int retry;

    public void sendEmail(EmailCommon emailCommon) {
        threadPoolExecutor.execute(() -> {
            try {
                MimeMessage message = sender.createMimeMessage();

                //true表示需要创建一个multipart message
                MimeMessageHelper helper = new MimeMessageHelper(message, true);
                helper.setFrom(from);
                helper.setTo(emailCommon.getReceiveMailbox());
                helper.setSubject(emailCommon.getSubject());
                helper.setText(emailCommon.getContent(), true);
                sender.send(message);
                logger.info("html邮件已经发送{},标题:{}", emailCommon.getReceiveMailbox(), emailCommon.getSubject());

            } catch (Exception e) {

                if (this.retry > 0) {
                    logger.info("发送html邮件时发生异常！剩余重试次数:{}", this.retry--);
                    sendEmail(emailCommon);
                } else {
                    logger.error("发送html邮件时发生异常:收件人邮箱【{}】,内容:【{}】,异常细节:{}", emailCommon.getReceiveMailbox(), emailCommon.getContent(), e.getMessage());
                }
            }
        });
    }

    @Deprecated
    public void sendGBKEmail(EmailCommon emailCommon, String fileName, InputStreamSource fileStream) {
        sendEmail(emailCommon, fileName, fileStream, "GBK", "B");
    }

    /**
     * 流方式实现邮件附件发送，注意需要保证同步发送，异步时流会关闭 需打破双因子认证
     */
    @Deprecated
    public void sendEmail(EmailCommon emailCommon, String fileName, InputStreamSource fileStream
            , String charset, String encoding) {
        try {
            MimeMessage message = sender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom(from);
            helper.setTo(emailCommon.getReceiveMailbox());
            helper.setSubject(emailCommon.getSubject());
            helper.setText(emailCommon.getContent(), true);
            if (null != fileName && null != fileStream) {
                String attachmentName = null != charset ? MimeUtility.encodeText(fileName, charset, encoding) : fileName;
                helper.addAttachment(attachmentName, fileStream);
            }
            sender.send(message);

            logger.info("带附件邮件已经发送{},标题:{}", emailCommon.getReceiveMailbox(), emailCommon.getSubject());

        } catch (Exception e) {
            if (this.retry > 0) {
                logger.info("发送附件邮件时发生异常！剩余重试次数:{}", this.retry--);
                sendEmail(emailCommon, fileName, fileStream, charset, encoding);
            } else {
                logger.error("发送附件邮件时发生异常:收件人邮箱【{}】,内容:【{}】,异常细节:{}", emailCommon.getReceiveMailbox(), emailCommon.getContent(), e.getMessage());
            }
        }
    }

    public void sendHtmlEmail(EmailCommon emailCommon) {
        Map<String, Object> msg = new HashMap<>(8);
        msg.put("toMails", Arrays.stream(emailCommon.getReceiveMailbox()).collect(Collectors.toList()));
        msg.put("type", "Html");
        msg.put("appName", "KOL-MONITOR");
        msg.put("content", emailCommon.getContent());
        msg.put("subject", emailCommon.getSubject());
        msg.put("fromMail", "<EMAIL>");
        kafkaSender.send("email_send_topic", msg);
    }
}

