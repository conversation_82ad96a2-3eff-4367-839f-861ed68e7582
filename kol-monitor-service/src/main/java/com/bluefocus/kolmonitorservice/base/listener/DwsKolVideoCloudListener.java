package com.bluefocus.kolmonitorservice.base.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.article.KolVideoCloudDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.KolVideoCloud;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DwsKolVideoCloudListener implements TopicStrategy {

    private static final String TOPIC_NAME = "dws_dy_cloud_videos";

    @Resource
    KolVideoCloudDomainService kolVideoCloudDomainService;

    @Override
    public String getTopicType() {
        return TOPIC_NAME;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) throws Exception {
        if (StringUtils.isNotEmpty(message)) {
            Map map = (Map) JSON.parse(message);
            if (ObjectUtil.isEmpty(map.get("kol_dy_id")) || ObjectUtil.isEmpty(map.get("create_time"))) {
                return;
            }

            String kolDyAccount = map.get("kol_dy_id").toString();
            LocalDateTime createTime = ObjectUtil.isNotEmpty(map.get("create_time")) ?
                    Times.toLocalDateTime(Long.parseLong(map.get("create_time").toString())) : LocalDateTime.now();

            JSONArray notes = ObjectUtil.isEmpty(map.get("videos")) ? null : JSON.parseArray(map.get("videos").toString());

            if (ObjectUtil.isNull(notes) || notes.size() == 0) {
                log.warn("kol云图视频数据为空，直接跳过");
                return;
            }

            List<KolVideoCloud> list = kolVideoCloudDomainService.findIdListByKolDyId(kolDyAccount);
            Set<String> existingNoteSet = list.stream().map(note -> note.getKolDyAccount() + "_" + note.getVideoId())
                    .collect(Collectors.toSet());

            List<KolVideoCloud> saveKolVideos = new ArrayList<>();
            List<KolVideoCloud> updateKolVideos = new ArrayList<>();
            for (int i = 0; i < notes.size(); i++) {
                JSONObject note = notes.getJSONObject(i);
                String videoId = ObjectUtil.isEmpty(note.get("video_id")) ? null : note.getString("video_id");
                if (null == videoId) {
                    continue;
                }
                KolVideoCloud cloud = new KolVideoCloud();
                String key = kolDyAccount + "_" + videoId;
                if (existingNoteSet.contains(key)) {
                    updateKolVideos.add(cloud);
                } else {
                    saveKolVideos.add(cloud);
                }

                cloud.setKolDyAccount(kolDyAccount);
                cloud.setVideoId(videoId);
                cloud.setSearchAfterPv(ObjectUtil.isEmpty(note.get("search_after_pv")) ? null : note.getIntValue("search_after_pv"));
                cloud.setSearchAfterUv(ObjectUtil.isEmpty(note.get("search_after_uv")) ? null : note.getIntValue("search_after_uv"));
                cloud.setSearchAfterRate(ObjectUtil.isEmpty(note.get("search_after_rate")) ? null : note.getBigDecimal("search_after_rate"));
                cloud.setSearchAfterViewPv(ObjectUtil.isEmpty(note.get("search_after_view_pv")) ? null : note.getIntValue("search_after_view_pv"));
                cloud.setSearchAfterViewUv(ObjectUtil.isEmpty(note.get("search_after_view_uv")) ? null : note.getIntValue("search_after_view_uv"));
                cloud.setSearchAfterViewRate(ObjectUtil.isEmpty(note.get("search_after_view_rate")) ? null : note.getBigDecimal("search_after_view_rate"));

                cloud.setAddRateA3(ObjectUtil.isEmpty(note.get("add_rate_a3")) ? null : note.getBigDecimal("add_rate_a3"));
                cloud.setPluginType(ObjectUtil.isEmpty(note.get("plugin_type")) ? null : note.getString("plugin_type"));
                cloud.setPluginClickRate(ObjectUtil.isEmpty(note.get("plugin_click_rate")) ? null : note.getBigDecimal("plugin_click_rate"));
                cloud.setPluginShowNum(ObjectUtil.isEmpty(note.get("plugin_show_num")) ? null : note.getIntValue("plugin_show_num"));
                cloud.setPluginClickNum(ObjectUtil.isEmpty(note.get("plugin_click_num")) ? null : note.getIntValue("plugin_click_num"));
                cloud.setIsHot(ObjectUtil.isEmpty(note.get("is_hot")) ? null : note.getIntValue("is_hot"));
                cloud.setHotType(ObjectUtil.isEmpty(note.get("hot_type")) ? null : note.getIntValue("hot_type"));
                cloud.setState(1);
                cloud.setCreateTime(createTime);


                if (saveKolVideos.size() >= 32) {
                    kolVideoCloudDomainService.insertBatch(saveKolVideos);
                    saveKolVideos = new ArrayList<>();
                }
                if (updateKolVideos.size() >= 16) {
                    kolVideoCloudDomainService.updateBatchById(updateKolVideos);
                    updateKolVideos = new ArrayList<>();
                }
            }
            if (CollectionUtil.isNotEmpty(saveKolVideos)) {
                kolVideoCloudDomainService.saveBatch(saveKolVideos);
            }
            if (CollectionUtil.isNotEmpty(updateKolVideos)) {
                kolVideoCloudDomainService.updateBatchById(updateKolVideos);
            }
        }
    }
}