package com.bluefocus.kolmonitorservice.base.util;

import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import javax.swing.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;


@Slf4j
public class PictureUtils {
    public static int color_range = 200;

    // 图片透明度
    public static int alpha = 0;


    /**
     * 强制压缩/放大图片到固定的大小
     *
     * @param w int 新宽度
     * @param h int 新高度
     */
    public static BufferedImage resize(Image img, int w, int h) {
        try {
            // SCALE_SMOOTH 的缩略算法 生成缩略图片的平滑度的 优先级比速度高 生成的图片质量比较好 但速度慢
            BufferedImage image = new BufferedImage(w, h, BufferedImage.TYPE_INT_RGB);
            image.getGraphics().drawImage(img, 0, 0, w, h, null); // 绘制缩小后的图
            return image;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;

    }

    // 将BufferedImage转换为字节数组
    public static byte[] compressPictureUpload(byte[] bytes, double reductionFactor){
        try {
            ByteArrayInputStream in = new ByteArrayInputStream(bytes);
            BufferedImage image = ImageIO.read(in);
            if (null == image){
                return null;
            }
            int width = image.getWidth();
            int height = image.getHeight();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            long maxSizeBytes = 300 * 1024; // 300KB
            BufferedImage compressedImage = image;
            byte[] imageBytes = new byte[0];
            while (true) {
                // 将当前压缩后的图像写入到输出流
                ImageIO.write(compressedImage, "png", out);
                out.flush();
                imageBytes = out.toByteArray();
                long fileLength = imageBytes.length;
                if (fileLength <= maxSizeBytes) {
                    break;
                }

                // 计算新的尺寸
                width = (int) (width * reductionFactor);
                height = (int) (height * reductionFactor);

                // 创建一个新的 BufferedImage 对象，用于放置调整尺寸后的图像
                BufferedImage resizedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g = resizedImage.createGraphics();

                // 设置渲染质量
                g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

                // 绘制调整尺寸后的图像
                g.drawImage(compressedImage, 0, 0, width, height, null);
                g.dispose();

                // 准备下一次循环
                compressedImage = resizedImage;
                out.reset();
            }
            // 从输出流中重新读取图像
            out.flush();
            out.close();
            in.close();
            return imageBytes;
        } catch (Exception e) {
            log.error("compressPicture error", e);
            return bytes;
        }
    }

    public static byte[] compressPicture(byte[] bytes, double reductionFactor){
        try {
            ByteArrayInputStream in = new ByteArrayInputStream(bytes);
            BufferedImage image = ImageIO.read(in);
            int width = image.getWidth();
            int height = image.getHeight();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            long maxSizeBytes = 300 * 1024; // 300KB
            BufferedImage compressedImage = image;
            byte[] imageBytes = new byte[0];
            while (true) {
                // 将当前压缩后的图像写入到输出流
                ImageIO.write(compressedImage, "png", out);
                out.flush();
                imageBytes = out.toByteArray();
                long fileLength = imageBytes.length;
                if (fileLength <= maxSizeBytes) {
                    break;
                }

                // 计算新的尺寸
                width = (int) (width * reductionFactor);
                height = (int) (height * reductionFactor);

                // 创建一个新的 BufferedImage 对象，用于放置调整尺寸后的图像
                BufferedImage resizedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
                Graphics2D g = resizedImage.createGraphics();

                // 设置渲染质量
                g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);

                // 绘制调整尺寸后的图像
                g.drawImage(compressedImage, 0, 0, width, height, null);
                g.dispose();

                // 准备下一次循环
                compressedImage = resizedImage;
                out.reset();
            }
            // 从输出流中重新读取图像
            out.flush();
            out.close();
            in.close();
            return imageBytes;
        } catch (Exception e) {
            log.error("compressPicture error", e);
            return bytes;
        }
    }


    public static byte[] transAlpha(byte[] bytes) {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        try {
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));

            /*背景透明时,直接返回原图*/
            if (noBackground(image)) {
                return bytes;
            }

            // 高度和宽度
            int height = image.getHeight();
            int width = image.getWidth();

            // 生产背景透明和内容透明的图片
            ImageIcon imageIcon = new ImageIcon(image);
            BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_4BYTE_ABGR);
            Graphics2D g2D = (Graphics2D) bufferedImage.getGraphics(); // 获取画笔
            g2D.drawImage(imageIcon.getImage(), 0, 0, null); // 绘制Image的图片，使用了imageIcon.getImage()，目的就是得到image,直接使用image就可以的

            // 取图片边缘颜色作为对比对象
            String removeRgb = convertRgbStr(bufferedImage.getRGB(bufferedImage.getMinX(), bufferedImage.getMinY()));

            // 外层遍历是Y轴的像素
            for (int y = bufferedImage.getMinY(); y < bufferedImage.getHeight(); y++) {
                // 内层遍历是X轴的像素
                for (int x = bufferedImage.getMinX(); x < bufferedImage.getWidth(); x++) {
                    int rgb = bufferedImage.getRGB(x, y);
                    // 设置为透明背景
                    if (removeRgb.equals(convertRgbStr(rgb)) || colorInRange(rgb, color_range)) {
                        rgb = (alpha << 24) | (rgb & 0x00ffffff);
                        bufferedImage.setRGB(x, y, rgb);
                    }
                }
            }
            // 绘制设置了RGB的新图片,这一步感觉不用也可以只是透明地方的深浅有变化而已，就像蒙了两层的感觉
            g2D.drawImage(bufferedImage, 0, 0, null);
            ImageIO.write(bufferedImage, "png", outStream);
        } catch (Exception e) {
            log.error("处理图片抠图异常",e);
            return bytes;
        }
        return outStream.toByteArray();
    }


    /**
     * 判断是否有背景颜色
     *
     * @return true 无背景颜色;false 有背景颜色
     */
    public static boolean noBackground(BufferedImage bufferedImage) {
        if (bufferedImage == null) return false;
        int height = bufferedImage.getHeight();
        int width = bufferedImage.getWidth();

        for (int w = 0; w < width; w++) {
            for (int h = 0; h < height; h++) {
                int rgb = bufferedImage.getRGB(w, h);
                if (rgb >> 24 == 0) {
                    return true;
                }
            }
        }
        return false;
    }

    public static String convertRgbStr(int color) {
        int red = (color & 0xff0000) >> 16;// 获取color(RGB)中R位
        int green = (color & 0x00ff00) >> 8;// 获取color(RGB)中G位
        int blue = (color & 0x0000ff);// 获取color(RGB)中B位
        return red + "," + green + "," + blue;
    }

    // 判断是背景还是内容
    public static boolean colorInRange(int color, int color_range) {
        // 获取color(RGB)中R位
        int red = (color & 0xff0000) >> 16;
        // 获取color(RGB)中G位
        int green = (color & 0x00ff00) >> 8;
        // 获取color(RGB)中B位
        int blue = (color & 0x0000ff);
//        System.out.println("red=="+red   +"green=="+green  +"blue=="+blue);
        // 通过RGB三分量来判断当前颜色是否在指定的颜色区间内
        if (red >= color_range && green >= color_range && blue >= color_range) {
            return true;
        }
        return false;
    }

    // 判断是背景还是内容
    public static boolean colorInRange(int color) {
        return colorInRange(color, color_range);
    }

}
