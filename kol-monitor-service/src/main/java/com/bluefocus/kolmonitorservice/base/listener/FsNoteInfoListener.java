package com.bluefocus.kolmonitorservice.base.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;


@Service
@Log4j2
@RequiredArgsConstructor
public class FsNoteInfoListener implements TopicStrategy {

    private static final String TOPIC_NAME = "dws-fs-note-info";

    private final ArticleDomainService articleDomainService;

    @Override
    public String getTopicType() {
        return TOPIC_NAME;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {
        Map map = (Map) JSON.parse(message);
        if (ObjectUtil.isEmpty(map.get("article_id"))) {
            log.error("topic=dws-fs-note-info,有空数据，data：[{}]", message);
            return;
        }

        Long articleId = Long.valueOf(map.get("article_id").toString());
        articleDomainService.deleteArticleDetail(articleId);
        ArticleDetail articleDetail = new ArticleDetail();

        articleDetail.setArticleId(articleId);
        articleDetail.setTendObj(ObjectUtil.isEmpty(map.get("tend")) ? null : map.get("tend").toString());
        articleDetail.setArticleTitle(ObjectUtil.isEmpty(map.get("article_title")) ? null : map.get("article_title").toString());

        articleDetail.setConsumeTime(ObjectUtil.isEmpty(map.get("create_time")) ? null : Long.valueOf(map.get("create_time").toString()));
        articleDetail.setUpdateTime(LocalDateTime.now());

        articleDomainService.saveArticleDetail(articleDetail);
    }
}