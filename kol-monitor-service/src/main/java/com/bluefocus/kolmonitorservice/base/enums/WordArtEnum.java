package com.bluefocus.kolmonitorservice.base.enums;


import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum WordArtEnum {

    /**
     * 词云状态
     */
    ZERO(0, "成功"),
    ONE(1, "没有词频数据"),
    TWO(2, "图片异常，请重新尝试！"),
    THREE(3, "词云生成异常，请换一张图片上传！不支持纯色图片！"),
    FOUR(4, "词云生成未知异常"),
    ;

    private final Integer code;
    private final String desc;

    WordArtEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String descByCode(Integer code) {
        for (WordArtEnum value : WordArtEnum.values()) {
            if (value.code.equals(code)) {
                return value.desc;
            }
        }
        return null;
    }
}
