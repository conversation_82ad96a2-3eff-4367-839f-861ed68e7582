package com.bluefocus.kolmonitorservice.base.enums;


public enum FinishStatusEnum {

    /**
     * 状态
     */
    CRAWLER(0, "采集中"),
    ANALY(1, "分析中"),
    FINISH(2, "已完成"),
    FAIL(3, "失败"),
    ;

    private int code;
    private String desc;

    FinishStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String descOf(Integer code) {
        for (FinishStatusEnum value : FinishStatusEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "";
    }
}
