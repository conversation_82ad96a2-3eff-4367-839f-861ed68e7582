package com.bluefocus.kolmonitorservice.base.enums;


import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

@Getter
public enum MediaEditStatusEnum {

    /**
     * 对象编辑状态
     */
    CRAWLER(10, "采集中"),
    ANALY(11, "分析中"),
    FINISH(12, "已完成"),
    FAIL(13, "失败"),
    EDIT_CRAWLER(20, "编辑采集中"),
    EDIT_ANALY(21, "编辑分析中"),
    EDIT_FINISH(22, "编辑完成"),
    EDIT_FAIL(23, "编辑失败"),
    ;

    private final int code;
    private final String desc;

    MediaEditStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private final static MediaEditStatusEnum[] VALUES = MediaEditStatusEnum.values();
    private final static List<MediaEditStatusEnum> FAIL_LIST = new ArrayList<>();
    private final static List<MediaEditStatusEnum> SUC_LIST = new ArrayList<>();

    static {
        FAIL_LIST.add(CRAWLER);
        FAIL_LIST.add(FAIL);
        FAIL_LIST.add(EDIT_CRAWLER);
        FAIL_LIST.add(EDIT_FAIL);

        SUC_LIST.add(ANALY);
        SUC_LIST.add(FINISH);
        SUC_LIST.add(EDIT_ANALY);
        SUC_LIST.add(EDIT_FINISH);
    }

    public static boolean failCheck(Integer code) {
        for (MediaEditStatusEnum mediaEditStatusEnum : FAIL_LIST) {
            if (mediaEditStatusEnum.code == code) {
                return true;
            }
        }
        return false;
    }

    public static boolean sucCheck(Integer code) {
        for (MediaEditStatusEnum mediaEditStatusEnum : SUC_LIST) {
            if (mediaEditStatusEnum.code == code) {
                return true;
            }
        }
        return false;
    }

    public static boolean isFinish(Integer code) {
        return FINISH.getCode() == code || EDIT_FINISH.getCode() == code;
    }

    public static boolean isFail(Integer code) {
        return FAIL.getCode() == code || EDIT_FAIL.getCode() == code;
    }
}
