package com.bluefocus.kolmonitorservice.base.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Getter
public enum ProjectStatusEnum {

    /**
     * 监测中
     */
    MONITORING(2, "监测中"),
    MONITORED(3, "监测完成"),
    FAILED(4, "监测失败"),

    ;
    private final int index;
    private final String name;

    public static String codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProjectStatusEnum e : ProjectStatusEnum.values()) {
            if (e.getIndex() == code) {
                return e.getName();
            }
        }
        return null;
    }

}