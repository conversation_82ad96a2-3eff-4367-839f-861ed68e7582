package com.bluefocus.kolmonitorservice.base.listener;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * topicFactory
 *
 * <AUTHOR>
 * @date 2020/12/10
 */
@Component
public class TopicFactory implements InitializingBean, ApplicationContextAware {

    private static final Map<String, TopicStrategy> topicMap = new HashMap<>();

    private ApplicationContext appContext;

    public static TopicStrategy getTopic(String topicType) {

        if (topicType == null) {
            throw new IllegalArgumentException("topic type is empty.");
        }
        if (!topicMap.containsKey(topicType)) {
            throw new IllegalArgumentException("topic type not supported.");
        }
        return topicMap.get(topicType);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 将 Spring 容器中所有的 TopicStrategy 接口实现类注册到 topicMap
        appContext.getBeansOfType(TopicStrategy.class)
                .values()
                .forEach(topic -> topicMap.put(topic.getTopicType(), topic));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        appContext = applicationContext;
    }
}
