package com.bluefocus.kolmonitorservice.base.common;

import com.bluefocus.kolmonitorservice.base.enums.PlatformEnum;

import java.util.*;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/15 15:27
 * @Description:
 */
public class PPtConstant {

    public static final Map<Integer, List<String>> FILE_NAME = new HashMap<>();
//
//    static {
//        FILE_NAME.put(PlatformEnum.ALL.getIndex(), Arrays.asList("01-all-title", "02-all-pie", "03-all-read", "04-all-like", "05-all-comment", "06-all-collect", "07-all-intnum","28-all-end"));
//        FILE_NAME.put(PlatformEnum.DY.getIndex(),  Arrays.asList("08-dy-read","08-dy-read2", "09-dy-like", "10-dy-comment", "11-dy-collect", "12-dy-intnum"));
//        FILE_NAME.put(PlatformEnum.XHS.getIndex(), Arrays.asList("13-xhs-read","13-xhs-read2", "14-xhs-like", "15-xhs-comment", "16-xhs-collect", "17-xhs-intnum"));
//        FILE_NAME.put(PlatformEnum.BLI.getIndex(), Arrays.asList("18-bli-read","18-bli-read2", "19-bli-like", "20-bli-comment", "21-bli-collect", "22-bli-intnum"));
//        FILE_NAME.put(PlatformEnum.KS.getIndex(),  Arrays.asList("23-ks-read","23-ks-read2", "24-ks-like", "25-ks-comment", "26-ks-collect", "27-ks-intnum"));
//    }


    static {
        FILE_NAME.put(PlatformEnum.ALL.getIndex(), Arrays.asList("01-all-title", "02-all-pie","28-all-end"));
        FILE_NAME.put(PlatformEnum.DY.getIndex(), Collections.singletonList("08-dy-read"));
        FILE_NAME.put(PlatformEnum.XHS.getIndex(), Collections.singletonList("13-xhs-read"));
        FILE_NAME.put(PlatformEnum.BLI.getIndex(), Collections.singletonList("18-bli-read"));
        FILE_NAME.put(PlatformEnum.KS.getIndex(), Collections.singletonList("23-ks-read"));
    }

    public static final String TEMPLATE_DIR = "D:\\tmp\\template2025";

    public static final String TMP_DIR = "D:\\tmp\\template2025\\{pId}";

    public static final String PIE_NOTE_RENAME = "各平台笔记量分布";
    public static final String PIE_NOTE_EXCEL_COL_1 = "笔记数量分平台分布占比";
    public static final String PIE_NOTE_EXCEL_COL_2 = "占比";

    public static final String PIE_READ_RENAME = "各平台阅读量分布";
    public static final String PIE_READ_EXCEL_COL_1 = "阅读量分布";
    public static final String PIE_READ_EXCEL_COL_2 = "占比";

}
