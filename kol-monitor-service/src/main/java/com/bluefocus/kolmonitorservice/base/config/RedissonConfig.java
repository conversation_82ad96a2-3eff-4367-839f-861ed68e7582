package com.bluefocus.kolmonitorservice.base.config;

import io.netty.channel.nio.NioEventLoopGroup;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.config.Config;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.ClassUtils;

/**
 * Created by kl on 2017/09/26.
 * redisson 客户端配置
 */
@Slf4j
@Data
@ConfigurationProperties(prefix = "spring.redisson")
@Configuration
public class RedissonConfig {

    private String address;
    private String masterName;
    private String[] sentinelAddresses;
    private int connectionMinimumIdleSize = 10;
    private int idleConnectionTimeout=10000;
    private int pingTimeout=1000;
    private int connectTimeout=10000;
    private int timeout=3000;
    private int retryAttempts=3;
    private int retryInterval=1500;
    private String password = null;
    private int subscriptionsPerConnection=5;
    private String clientName = "report-monitor-api";
    private int subscriptionConnectionMinimumIdleSize = 1;
    private int subscriptionConnectionPoolSize = 50;
    private int connectionPoolSize = 64;
    private int database; // 类加载时默认为0
    private int dnsMonitoringInterval = 5000;
    private int thread = 4;
    private String codec="org.redisson.codec.JsonJacksonCodec";

    @Bean(destroyMethod = "shutdown")
    RedissonClient redisson() throws Exception {
        Config config = new Config();

        if (masterName != null && !masterName.equals("")) {
            config.useSentinelServers()
                    .setMasterName(masterName)
                    .addSentinelAddress(sentinelAddresses)
                    .setDatabase(database)
                    .setDnsMonitoringInterval(dnsMonitoringInterval)
                    .setSubscriptionConnectionMinimumIdleSize(subscriptionConnectionMinimumIdleSize)
                    .setSubscriptionConnectionPoolSize(subscriptionConnectionPoolSize)
                    .setSubscriptionsPerConnection(subscriptionsPerConnection)
                    .setClientName(clientName)
                    .setRetryAttempts(retryAttempts)
                    .setRetryInterval(retryInterval)
                    .setTimeout(timeout)
                    .setConnectTimeout(connectTimeout)
                    .setIdleConnectionTimeout(idleConnectionTimeout)
                    .setPingTimeout(pingTimeout)
                    .setPassword(password);
        } else {
            config.useSingleServer().setAddress(address)
                    .setConnectionMinimumIdleSize(connectionMinimumIdleSize)
                    .setConnectionPoolSize(connectionPoolSize)
                    .setDatabase(database)
                    .setDnsMonitoringInterval(dnsMonitoringInterval)
                    .setSubscriptionConnectionMinimumIdleSize(subscriptionConnectionMinimumIdleSize)
                    .setSubscriptionConnectionPoolSize(subscriptionConnectionPoolSize)
                    .setSubscriptionsPerConnection(subscriptionsPerConnection)
                    .setClientName(clientName)
                    .setRetryAttempts(retryAttempts)
                    .setRetryInterval(retryInterval)
                    .setTimeout(timeout)
                    .setConnectTimeout(connectTimeout)
                    .setIdleConnectionTimeout(idleConnectionTimeout)
                    .setPingTimeout(pingTimeout)
                    .setPassword(password);
        }

        Codec codec = (Codec) ClassUtils.forName(getCodec(), ClassUtils.getDefaultClassLoader()).newInstance();
        config.setCodec(codec);
        config.setThreads(thread);
        config.setEventLoopGroup(new NioEventLoopGroup());
        return Redisson.create(config);
    }


}