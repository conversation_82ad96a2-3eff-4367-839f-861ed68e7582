package com.bluefocus.kolmonitorservice.base.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum ChatStatusEnum {

    /**
     * created completed
     * in_progress(中间过程不记录)
     */
    CREATED(0, "created", "创建"),
    COMPLETED(1, "completed", "对话完成"),
    FAILURE(2, "failed", "对话失败"),
    CHAT_LOSE(3, "chat_lose", "对话失效"),
    CHAT_CANCELED(4, "canceled", "对话取消"),
    ;

    private final Integer code;
    private final String status;
    private final String desc;

    public static String getNameByCode(Integer code) {
        for (ChatStatusEnum value : ChatStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.name();
            }
        }
        return null;
    }

    public static String getStatusByCode(Integer code) {
        for (ChatStatusEnum value : ChatStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getStatus();
            }
        }
        return null;
    }

    public static Integer getCodeByStatus(String status) {
        for (ChatStatusEnum value : ChatStatusEnum.values()) {
            if (value.getStatus().equals(status)) {
                return value.getCode();
            }
        }
        return null;
    }
}