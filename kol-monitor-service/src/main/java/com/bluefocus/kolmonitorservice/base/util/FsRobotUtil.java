package com.bluefocus.kolmonitorservice.base.util;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0023 2024/5/23 18:01
 * @description:
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FsRobotUtil {

    private final RestTemplate restTemplate;

    @Value("${spring.profiles.active}")
    private String dev;
    @Value("${robot.url}")
    private String robotUrl;

    public void sendRobotMsg(String url, String content) {

        Map<String, Object> requestBody = new HashMap<>(4);
        requestBody.put("msg_type", "text");
        Map<String, Object> contentMap = new HashMap<>(4);
        String envContent = getEnvContent(content);
        contentMap.put("text", envContent);
        requestBody.put("content", contentMap);
        String reqUrl = url == null ? robotUrl : url;
        log.info("请求飞书机器人={}，内容=【{}】", reqUrl, envContent);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            restTemplate.postForEntity(reqUrl, requestEntity, Map.class);
        } catch (Exception e) {
            log.warn("发送机器人消息异常，内容为：{}", content);
        }
    }

    private String getEnvContent(String content) {
        String text = "【AI社媒影响力】" + "\n" + content;
        return "dev".equals(dev) ? "测试环境" + "\n" + text : text;
    }

    public void sendKolRobotMsg(String url, String content) {

        Map<String, Object> requestBody = new HashMap<>(4);
        requestBody.put("msg_type", "text");
        Map<String, Object> contentMap = new HashMap<>(4);
        String envContent = getKolEnvContent(content);

        contentMap.put("text", envContent);
        requestBody.put("content", contentMap);
        String reqUrl = url == null ? robotUrl : url;
        log.info("请求飞书机器人={}，内容=【{}】", reqUrl, envContent);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);

        try {
            restTemplate.postForEntity(reqUrl, requestEntity, Map.class);
        } catch (Exception e) {
            log.warn("发送机器人消息异常，内容为：{}", content);
        }
    }

    private String getKolEnvContent(String content) {
        String text = "【新媒体监测】" + "\n" + content;
        return "dev".equals(dev) ? "测试环境" + "\n" + text : text;
    }

    public void sendRouteRobotMsg(String url, String content) {

        Map<String, Object> requestBody = new HashMap<>(4);
        requestBody.put("msg_type", "text");
        Map<String, Object> contentMap = new HashMap<>(4);
        String envContent = getRouteEnvContent(content);

        contentMap.put("text", envContent);
        requestBody.put("content", contentMap);
        String reqUrl = url == null ? robotUrl : url;
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
        log.info("请求飞书机器人={}，内容=【{}】", reqUrl, envContent);
        try {
            restTemplate.postForEntity(reqUrl, requestEntity, Map.class);
        } catch (Exception e) {
            log.warn("发送机器人消息异常，内容为：{}", content);
        }
    }

    private String getRouteEnvContent(String content) {
        String text = "【数据路由转发】" + "\n" + content;
        return "dev".equals(dev) ? "测试环境" + "\n" + text : text;
    }
}
