package com.bluefocus.kolmonitorservice.base.listener.media;

import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaWordArtStart;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.TopicStrategy;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

/**
 * @author: yjLiu
 * @date: 0012 2024/6/12 17:37
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaHandleWordCloudListener implements TopicStrategy {

    public final static String TOPIC = "media-handle-word";
    private final MediaWordArtStart mediaWordArtStart;
    private final MediaObjectsDomainService mediaObjectsDomainService;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;


    @Override
    public String getTopicType() {
        return TOPIC;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {
        try {
            MediaMsgBody mediaMsgBody = new Gson().fromJson(message, MediaMsgBody.class);
            if (mediaMsgBody.getHandId() == null) {
                log.error("传参错误，message={}", message);
                return;
            }
            MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(mediaMsgBody.getHandId());
            if (!taskHandle.getPause() || taskHandle.getStatus() >= FinishStatusEnum.FINISH.getCode()) {
                log.info("词云已经结束，跳过, handleId={}", taskHandle.getId());
                return;
            }

            mediaWordArtStart.handle(taskHandle, null);
        } catch (Exception e) {
            log.error("消费消息异常: {}", message, e);
        }
    }
}
