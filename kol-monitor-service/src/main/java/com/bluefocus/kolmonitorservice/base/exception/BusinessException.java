package com.bluefocus.kolmonitorservice.base.exception;

import com.bluefocus.basebean.exception.ResponseException;

/**
 * <AUTHOR>
 * @Descirption 业务异常
 * @date 2022/3/29 10:42 上午
 */
public class BusinessException extends ResponseException {
    public static final int BUSINESS_EXCEPTION = 10501;
    private int errorCode = 10500;

    public static final String KEY_SENSITIVE ="敏感词";
    public static final String KEY_SENSITIVE_CONTENT ="关键词包含敏感词:%s，请修改后提交";
    public static final String KEY_OTHER ="系统异常，请稍后重试";
    public static final String KEY_TIME_OUT ="查询关键词声量过大，导致任务超时，请更换搜索词或者添加排除词后重新提交";
    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    @Override
    public int getErrorCode() {
        return this.errorCode;
    }
}
