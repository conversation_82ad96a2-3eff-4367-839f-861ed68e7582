package com.bluefocus.kolmonitorservice.base.common;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/15 15:27
 * @Description:
 */
public class EmailConstant {

    public static final String[] emails = {"<EMAIL>"
            , "<EMAIL>"
            , "<EMAIL>"
            , "<EMAIL>"
            , "<EMAIL>"};
    public static final String subject = "【飞书监测工具】";
    public static final String content = "飞书项目监测出现异常，详细信息请查看表格";
    public static final String ERR_EXCEL_NAME = "新媒体监测异常表";

    public static final String[] UPLOADING_EMAILS = {"<EMAIL>", "<EMAIL>"};

    public static final String UPLOADING_CONTENT = "飞书项目链接批量识别出现异常，异常项目id为：";

    public static final String FS_TASK_CONTENT = "项目【%s】";

    public static final String FS_OBJ_CONTENT = "{obj}搜索词{keyword}\n" +
            "{obj}排除词{filterword}\n" +
            "{obj}分析时间段【{time}】\n" +
            "{obj}渠道【{source}】\n" +
            "{obj}任务状态【{status}】\n";

    public static final String FS_USER_CONTENT = "用户【%s】\n" +
            "部门【%s】";
}
