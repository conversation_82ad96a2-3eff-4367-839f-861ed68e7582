package com.bluefocus.kolmonitorservice.base.enums;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @author: yjLiu
 * @date: 0004 2024/6/4 15:45
 * @description:
 */
public enum SortEnum {

    /**
     * 66
     */
    interactionCnt("interactionCnt", "互动量","interaction_cnt"),
    publishTime("publishTime", "发布时间","publish_time"),
    likeCnt("likeCnt", "点赞","like_cnt"),
    collectionCnt("collectionCnt", "收藏","collection_cnt"),
    reviewCnt("reviewCnt", "评论","review_cnt"),
    repostsCnt("repostsCnt", "转发","reposts_cnt"),
    ;

    String name;
    String desc;
    String field;
    private static final List<String> sortList = new ArrayList<>();

    static {
        Arrays.stream(SortEnum.values()).forEach(sortEnum -> sortList.add(sortEnum.getName()));
    }

    SortEnum(String name, String desc, String field) {
        this.name = name;
        this.desc = desc;
        this.field = field;
    }

    public String getName() {
        return name;
    }

    public String getField() {
        return field;
    }

    public static List<String> getList() {
        return sortList;
    }

    public static String getFieldByName(String name) {
        if (null == name) {
            return null;
        }
        for (SortEnum value : SortEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getField();
            }
        }
        return name;
    }
}
