package com.bluefocus.kolmonitorservice.base.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import javax.annotation.PreDestroy;
import java.util.concurrent.*;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/9 14:19
 * @Description:
 */
@Configuration
public class KolThreadConfig {

    private static final int cpuCount = Runtime.getRuntime().availableProcessors();
    private static final int keepAliveTime = 10;

    @Bean
    public ThreadPoolExecutor threadPoolExecutor() {
        return new ThreadPoolExecutor(cpuCount * 2,
                cpuCount * 2,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(10000),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ThreadPoolExecutor mediaThreadPoolExecutor() {
        return new ThreadPoolExecutor(cpuCount * 2,
                cpuCount * 2,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(10000),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ThreadPoolExecutor dalle3ThreadPoolExecutor() {
        return new ThreadPoolExecutor(18,
                18,
                keepAliveTime,
                TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(18),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ThreadPoolExecutor algorithmThreadPoolExecutor() {
        return new ThreadPoolExecutor(25,
                25,
                2,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(10000),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean
    public ThreadPoolExecutor wordArtThreadPoolExecutor() {
        return new ThreadPoolExecutor(4,
                4,
                2,
                TimeUnit.MINUTES,
                new LinkedBlockingDeque<>(10000),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.AbortPolicy());
    }

    @Bean("scheduledExecutorPool")
    public ThreadPoolTaskScheduler scheduledExecutorPool() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(20);
        scheduler.setThreadNamePrefix("scheduled-task-");
        scheduler.setRemoveOnCancelPolicy(true);
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        return scheduler;
    }

    @PreDestroy
    public void shutDownExecutors() {
        scheduledExecutorPool().shutdown();
        algorithmThreadPoolExecutor().shutdown();
        threadPoolExecutor().shutdown();
        mediaThreadPoolExecutor().shutdown();
        dalle3ThreadPoolExecutor().shutdown();
    }
}
