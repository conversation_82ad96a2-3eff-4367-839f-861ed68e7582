package com.bluefocus.kolmonitorservice.base.util;

import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Describe:
 * @Author: liuyj
 * @Date: 2022-06-07 09:41
 */
public class HttpUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);

    public static String sentGet(String url) {
        try {
            HttpClient client = HttpClients.createDefault();
            // 发送get请求
            HttpGet request = new HttpGet(url);
            HttpResponse response = client.execute(request);

            // 请求发送成功，并得到响应
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                // 读取服务器返回过来的json字符串数据
                String strResult = EntityUtils.toString(response.getEntity());
                return strResult;
            }
        } catch (IOException e) {
            log.info("http 请求异常:{}", e);
            e.printStackTrace();
        }
        return "";
    }

    public static String sendPost(String url, String content, ContentType contentType) {
        log.info("请求地址：【{}】,请求内容【{}】", url, content);
        String response = null;
        HttpPost httpPost = new HttpPost(url);
        long startTime = System.currentTimeMillis();
        try {

            HttpEntity entity = new StringEntity(content, contentType);
            httpPost.setEntity(entity);
            HttpClient client = HttpClients.createDefault();
            HttpResponse resp = client.execute(httpPost);
            if (resp != null && resp.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                response = EntityUtils.toString(resp.getEntity());
            }

        } catch (IOException e) {
            log.error("sendPost请求异常,请求路径【{}】,请求时间【{}】ms,异常【{}】", url, System.currentTimeMillis() - startTime, e.toString());
        } finally {
            httpPost.releaseConnection();
        }
        return response;
    }

    public static String sendPost(String url, String content, ContentType contentType, int timout) {
        return sendPost(url, content, contentType, timout, timout);
    }

    public static String sendPost(String url, String content, ContentType contentType, int connectTimeout, int readTimeout) {
        log.info("请求地址：【{}】,请求内容【{}】", url, content);
        String response = null;
        HttpPost httpPost = new HttpPost(url);
        long startTime = System.currentTimeMillis();
        try {

            // 创建RequestConfig实例并设置超时参数
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(connectTimeout).setConnectTimeout(connectTimeout).setSocketTimeout(readTimeout).build();

            // 使用自定义的RequestConfig创建HttpClient实例
            CloseableHttpClient client = HttpClients.custom().setDefaultRequestConfig(requestConfig)

                    .build();
            HttpEntity entity = new StringEntity(content, contentType);
            httpPost.setEntity(entity);
            HttpResponse resp = client.execute(httpPost);
            if (resp != null && resp.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                response = EntityUtils.toString(resp.getEntity());
            } else {
                log.error("sendPost请求异常,请求路径【{}】,请求内容【{}】，请求时间【{}】ms,响应【{}】", url, System.currentTimeMillis() - startTime, content, resp);
            }

        } catch (IOException e) {
            log.error("sendPost请求异常,请求路径【{}】,请求时间【{}】ms,异常【{}】", url, System.currentTimeMillis() - startTime, e.toString());
        } finally {
            httpPost.releaseConnection();
        }
        return response;
    }

    public static String sendPostHeadForm(String url, Map<String, Object> pMap, Map<String, String> headerMap, int timeout) {
        log.info("请求地址：【{}】,请求内容【{}】", url, pMap);
        String response = null;
        HttpPost httpPost = new HttpPost(url);
        long startTime = System.currentTimeMillis();
        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectionRequestTimeout(timeout)
                    .setConnectTimeout(timeout)
                    .setSocketTimeout(timeout).build();

            CloseableHttpClient client = HttpClients.custom().setDefaultRequestConfig(requestConfig)
                    .build();

            // 构建表单实体
            List<NameValuePair> nvps = new ArrayList<>();
            for (Map.Entry<String, Object> entry : pMap.entrySet()) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue().toString()));
            }

            HttpEntity entity = new UrlEncodedFormEntity(nvps, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            httpPost.setEntity(entity);
            headerMap.keySet().forEach(k -> httpPost.setHeader(k, headerMap.get(k)));

            HttpResponse resp = client.execute(httpPost);
            if (resp != null && resp.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                response = EntityUtils.toString(resp.getEntity());
            } else {
                log.error("sendPost请求异常,请求路径【{}】,请求内容【{}】，请求时间【{}】ms,响应【{}】", url, System.currentTimeMillis() - startTime, pMap, resp);
            }

        } catch (IOException e) {
            log.error("sendPost请求异常,请求路径【{}】,请求时间【{}】ms,异常【{}】", url, System.currentTimeMillis() - startTime, e.toString());
        } finally {
            httpPost.releaseConnection();
        }
        return response;
    }
}
