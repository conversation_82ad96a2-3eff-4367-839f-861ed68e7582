package com.bluefocus.kolmonitorservice.base.listener.media;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.application.service.media.impl.ApiMediaService;
import com.bluefocus.kolmonitorservice.application.service.media.impl.DataHandleState;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAlgorithmStart;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaNoteDayStart;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.listener.TopicStrategy;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;


/**
 * @author: yjLiu
 * @date: 0012 2024/6/12 17:37
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaHandleNoteDayListener implements TopicStrategy {

    public final static String TOPIC = "media-handle-note-day";
    private final ApiMediaService apiMediaService;
    private final MediaNoteDayStart mediaNoteDayStart;
    private final MediaTaskHandleDomainService mediaTaskHandleDomainService;
    private final MediaAlgorithmStart mediaAlgorithmStart;

    @Override
    public String getTopicType() {
        return TOPIC;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {

        // 新需求 任务失败也要生成词云
        MediaMsgBody mediaMsgBody = JSON.parseObject(message, MediaMsgBody.class);
        mediaMsgBody.setClassSource(this.getClass().getSimpleName());

        MediaTaskHandle taskHandle = mediaTaskHandleDomainService.getById(mediaMsgBody.getHandId());
        if (!taskHandle.getPause() || taskHandle.getStatus() == FinishStatusEnum.FAIL.getCode()) {
            log.info("热点分析笔记原贴日数据，对象已失效,handle={}", taskHandle.getId());
            return;
        }

        DataHandleState dataHandleState = mediaNoteDayStart.handle(taskHandle, null);
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(taskHandle.getId());
        if (dataHandleState.getAll() || handle.getApiDayRetry() > mediaNoteDayStart.getRetryLimit()) {
            // 修改trend状态
//            mediaNoteDayStart.updateNoteDayState(mediaMsgBody);
            mediaAlgorithmStart.handleMq(mediaMsgBody);
        } else {
            apiMediaService.handleMq(mediaMsgBody, TOPIC);
        }

    }
}
