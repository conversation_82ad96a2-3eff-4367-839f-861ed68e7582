package com.bluefocus.kolmonitorservice.base.config;


import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.*;


@Configuration
@EnableTransactionManagement
public class DataBaseConfig {

    @Value("${media.gpt.serverUrl1}")
    private String serverUrl1;
    @Value("${media.gpt.api-key1}")
    private String apiKey1;

    @Value("${media.gpt.serverUrl2}")
    private String serverUrl2;
    @Value("${media.gpt.api-key2}")
    private String apiKey2;

    @Value("${media.gpt.serverUrl3}")
    private String serverUrl3;
    @Value("${media.gpt.api-key3}")
    private String apiKey3;

    @Primary
    @Bean
    public DataSource dataSourceUser() {
        return DruidDataSourceConfig.config(DruidDataSourceBuilder.create().build());
    }

    @Bean("delle3GptMap")
    public Map<String, String> delle3GptMap() {
        TreeMap<String, String> hashMap = new TreeMap<>();

        hashMap.put(serverUrl1, apiKey1);
        hashMap.put(serverUrl2, apiKey2);
        hashMap.put(serverUrl3, apiKey3);
        return hashMap;
    }

    @Bean("delle3GptList")
    public List<String> delle3GptList() {
        ArrayList<String> list = new ArrayList<>();
        list.add(serverUrl1);
        list.add(serverUrl2);
        list.add(serverUrl3);
        return list;
    }
}
