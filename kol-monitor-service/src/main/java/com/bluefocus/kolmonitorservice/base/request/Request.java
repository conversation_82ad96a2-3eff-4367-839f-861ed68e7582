package com.bluefocus.kolmonitorservice.base.request;


import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.response.Response;

import java.util.Map;

public interface Request<T extends Response> {

    /**
     * API名称
     */
    String getApiMethodName();

    /**
     * 请求参数集合
     */
    String getParams();

    /**
     * 获取请求时间戳（为空则用系统当前时间）
     */
    String getTimestamp();

    /**
     * 获取具体响应实现类的定义
     */
    Class<T> getResponseClass();

    /**
     * 获取自定义HTTP请求头参数
     */
    Map<String, String> getHeaderMap();

    /**
     * 客户端参数检查，减少服务端无效调用
     */
    void check() throws ApiRuleException;

    /**
     * 请求方式
     */
    String getMethod();

    /**
     * 请求api全地址
     */
    String getApi();

}
