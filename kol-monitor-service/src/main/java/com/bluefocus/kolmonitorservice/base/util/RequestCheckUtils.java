package com.bluefocus.kolmonitorservice.base.util;


import cn.hutool.core.util.StrUtil;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;

import java.util.List;

public class RequestCheckUtils {
    public static final String ERROR_CODE_ARGUMENTS_MISSING = "20001";
    public static final String ERROR_CODE_ARGUMENTS_INVALID = "20002";

    public static void checkNotEmpty(Object value, String fieldName) throws ApiRuleException {
        if (value == null) {
            throw new ApiRuleException(ERROR_CODE_ARGUMENTS_MISSING, "client-error:Missing required arguments:" + fieldName);
        }
        if (value instanceof String) {
            if (((String) value).trim().isEmpty()) {
                throw new ApiRuleException(ERROR_CODE_ARGUMENTS_MISSING, "client-error:Missing required arguments:" + fieldName);
            }
        }
        if (value instanceof List) {
            if (((List<?>) value).isEmpty()) {
                throw new ApiRuleException(ERROR_CODE_ARGUMENTS_MISSING, "client-error:Missing required arguments:" + fieldName);
            }
        }
    }

    public static void checkMaxLength(String value, int maxLength, String fieldName) throws ApiRuleException {
        if (value != null) {
            if (value.length() > maxLength) {
                throw new ApiRuleException(ERROR_CODE_ARGUMENTS_INVALID, "client-error:Invalid arguments:the string length of " + fieldName + " can not be larger than " + maxLength + ".");
            }
        }
    }

    public static void checkMinNum(Long value, Long min, String fieldName) throws ApiRuleException {
        if (value != null) {
            if (value < min) {
                throw new ApiRuleException(ERROR_CODE_ARGUMENTS_INVALID, "client-error:Invalid arguments:the number of " + fieldName + " can not be Less than " + min + ".");
            }
        }
    }

    public static void checkAllNotEmpty(List<String> fieldName, String... value) throws ApiRuleException {
        if (StrUtil.isAllNotEmpty(value)) {
            throw new ApiRuleException(ERROR_CODE_ARGUMENTS_INVALID, "client-error:" + String.join(" and ", fieldName) + " cannot exist simultaneously ");
        }
    }
}
