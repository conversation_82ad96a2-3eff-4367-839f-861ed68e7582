package com.bluefocus.kolmonitorservice.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ChatRoleEnum {

    /**
     * 角色enum
     */
    SYSTEM("system", "系统"),
    USER("user", "用户"),
    ASSISTANT("assistant", "智能体"),
    TOOL("tool", "插件工具"),
    FUNCTION("function", "自有函数"),
    ;

    private final String role;
    private final String desc;

}
