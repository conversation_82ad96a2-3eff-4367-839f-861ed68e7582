package com.bluefocus.kolmonitorservice.base.common;

import com.bluefocus.basebean.exception.ResponseException;

/**
 * @author: yj<PERSON>iu
 * @date: 0023 2024/6/23 16:54
 * @description:
 */
public class TimeOutException extends ResponseException {
    public static final int TimeOut_EXCEPTION = 10408;
    private int errorCode = 10408;

    public TimeOutException(String message) {
        super(message);
    }

    public TimeOutException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public TimeOutException(String message, Throwable cause) {
        super(message, cause);
    }

    public TimeOutException(int errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    @Override
    public int getErrorCode() {
        return this.errorCode;
    }
}
