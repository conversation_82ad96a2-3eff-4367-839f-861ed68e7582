package com.bluefocus.kolmonitorservice.base.enums;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @Describe: mimeTable
 * @Author: liuyj
 * @Date: 2022-07-22 15:17
 */
public enum MimeEnum {
    APK("apk","application/vnd.android.package-archive"),
    AVI("avi","video/x-msvideo"),
    BUFFER("buffer","application/octet-stream"),
    CER("cer","application/pkix-cert"),
    CHM("chm","application/vnd.ms-htmlhelp"),
    CONF("conf","text/plain"),
    CPP("cpp","text/x-c"),
    CRT("crt","application/x-x509-ca-cert"),
    CSS("css","text/css"),
    CSV("csv","text/csv"),
    DOC("doc","application/msword"),
    DOCX("docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
    EXE("exe","application/x-msdownload"),
    FLAC("flac","audio/x-flac"),
    FLV("flv","video/x-flv"),
    GIF("gif","image/gif"),
    H263("h263","video/h263"),
    H264("h264","video/h264"),
    HTM("htm","text/html"),
    HTML("html","text/html"),
    ICO("ico","image/x-icon"),
    INI("ini","text/plain"),
    INK("ink","application/inkml+xml"),
    ISO("iso","application/x-iso9660-image"),
    JAR("jar","application/java-archive"),
    JAVA("java","text/x-java-source"),
    JPEG("jpeg","image/jpeg"),
    JPG("jpg","image/jpeg"),
    JS("js","application/javascript"),
    JSON("json","application/json"),
    JSON5("json5","application/json5"),
    JSX("jsx","text/jsx"),
    LIST("list","text/plain"),
    LNK("lnk","application/x-ms-shortcut"),
    LOG("log","text/plain"),
    M3U8("m3u8","application/vnd.apple.mpegurl"),
    MANIFEST("manifest","text/cache-manifest"),
    MAP("map","application/json"),
    MARKDOWN("markdown","text/x-markdown"),
    MD("md","text/x-markdown"),
    MOV("mov","video/quicktime"),
    MP3("mp3","audio/mpeg"),
    MP4("mp4","video/mp4"),
    MPEG("mpeg","video/mpeg"),
    MPG("mpg","video/mpeg"),
    MSI("msi","application/x-msdownload"),
    OGG("ogg","audio/ogg"),
    OGV("ogv","video/ogg"),
    OTF("otf","font/opentype"),
    PDF("pdf","application/pdf"),
    PNG("png","image/png"),
    PPT("ppt","application/vnd.ms-powerpoint"),
    PPTX("pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"),
    PSD("psd","image/vnd.adobe.photoshop"),
    RAR("rar","application/x-rar-compressed"),
    RM("rm","application/vnd.rn-realmedia"),
    RMVB("rmvb","application/vnd.rn-realmedia-vbr"),
    ROFF("roff","text/troff"),
    SASS("sass","text/x-sass"),
    SCSS("scss","text/x-scss"),
    SH("sh","application/x-sh"),
    SQL("sql","application/x-sql"),
    SVG("svg","image/svg+xml"),
    SWF("swf","application/x-shockwave-flash"),
    TAR("tar","application/x-tar"),
    TEXT("text","text/plain"),
    TORRENT("torrent","application/x-bittorrent"),
    TTF("ttf","application/x-font-ttf"),
    TXT("txt","text/plain"),
    WAV("wav","audio/x-wav"),
    WEBM("webm","video/webm"),
    WM("wm","video/x-ms-wm"),
    WMA("wma","audio/x-ms-wma"),
    WMX("wmx","video/x-ms-wmx"),
    WOFF("woff","application/font-woff"),
    WOFF2("woff2","application/font-woff2"),
    WPS("wps","application/vnd.ms-works"),
    XHTML("xhtml","application/xhtml+xml"),
    XLS("xls","application/vnd.ms-excel"),
    XLSX("xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
    XML("xml","application/xml"),
    XZ("xz","application/x-xz"),
    YAML("yaml","text/yaml"),
    YML("yml","text/yaml"),
    ZIP("zip","application/zip");


    String ext;
    String type;
    private static final Map<String, String> mimeTable = new HashMap<>();

    static {
        Arrays.stream(MimeEnum.values()).forEach(mimeEnum -> mimeTable.put(mimeEnum.getExt(), mimeEnum.getType()));
    }

    MimeEnum(String ext, String type){
        this.ext =ext;
        this.type =type;
    }

    public String getExt() {
        return ext;
    }

    public String getType() {
        return type;
    }

    public static String getContentType(String fileExtension) {
        return mimeTable.get(fileExtension);
    }
}
