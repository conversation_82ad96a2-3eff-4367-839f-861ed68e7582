package com.bluefocus.kolmonitorservice.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ChatMsgTypeEnum {

    /**
     * 聊天消息类型enum
     */
    QUESTION("question", "问题"),
    ANSWER("answer", "回答"),
    FOLLOW_UP("follow_up", "建议问题"),
    VERBOSE("verbose", "多answer结束标志体"),
    ;

    private final String type;
    private final String desc;

}
