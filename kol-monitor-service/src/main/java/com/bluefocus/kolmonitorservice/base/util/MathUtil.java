package com.bluefocus.kolmonitorservice.base.util;

import java.util.Formatter;

/**
 * @Describe:
 * @Author: liuyj
 * @Date: 2022-07-19 16:02
 */
public class MathUtil {

    public static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash) {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }
}
