package com.bluefocus.kolmonitorservice.base.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class Response implements Serializable {

    private static final long serialVersionUID = -6092523588044573398L;

    @JSONField(name = "code")
    private String code;
    @JSONField(alternateNames = {"msg", "message"})
    private String msg;
    @JSONField(name = "success")
    private boolean success = false;
    /**
     * API响应JSON或XML串
     */
    @JsonIgnore
    private String body;
    /**
     * 响应头
     */
    @JsonIgnore
    private Map<String, List<String>> headerContent;
    /**
     * API请求URL(不包含body)
     */
    @JsonIgnore
    private String requestUrl;

}
