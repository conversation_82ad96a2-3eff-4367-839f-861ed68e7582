package com.bluefocus.kolmonitorservice.base.util;

import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Base64;

/**
 * 图片工具类
 */
@Component
public class ImageUtils {

    public static void saveByteArrayToLocalFile(byte[] byteArray, String outputPath) {
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            fos.write(byteArray);
            fos.flush();
            System.out.println("Image saved to " + outputPath);
        } catch (Exception e) {
            System.err.println("Error saving image: " + e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public static byte[] imageUrlToBytes(String imageUrl) {
        return imageInputStreamToBytes(getImageStream(imageUrl));
    }

    public static byte[] imageInputStreamToBytes(InputStream inputStream) {

        try {
            if (inputStream == null) {
                return null;
            }
            byte[] buffer = new byte[1024];
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            int num = inputStream.read(buffer);
            while (num != -1) {
                baos.write(buffer, 0, num);
                num = inputStream.read(buffer);
            }
            baos.flush();
            return baos.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static String imageUrlToBase64(String imageUrl) {
        return imageToBase64(getImageStream(imageUrl));
    }

    public static String imageToBase64(InputStream inputStream) {
        return bytesToBase64(imageInputStreamToBytes(inputStream));
    }

    public static String bytesToBase64(byte[] bytes) {
        return bytesToBase64(bytes, null);
    }

    public static String bytesToBase64(byte[] bytes, Charset charsets) {
        if (bytes == null) {
            return null;
        }
        return new String(Base64.getEncoder().encode(bytes), charsets == null ? StandardCharsets.UTF_8 : charsets);
    }

    public static InputStream getImageStream(String imageUrl) {
        try {
            if (imageUrl == null) {
                return null;
            }
            SSLContext ctx = SSLContext.getInstance("TLS");
            ctx.init(new KeyManager[0], new TrustManager[]{new DefaultTrustManager()}, new SecureRandom());
            SSLSocketFactory ssf = ctx.getSocketFactory();
            URL url = new URL(imageUrl);
            HttpsURLConnection httpsConn = (HttpsURLConnection) url.openConnection();
            httpsConn.setSSLSocketFactory(ssf);
            httpsConn.setHostnameVerifier(new HostnameVerifier() {
                @Override
                public boolean verify(String arg0, SSLSession arg1) {
                    return true;
                }
            });
            return httpsConn.getInputStream();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static byte[] base64Decode2Bytes(String base64) {
        return Base64.getDecoder().decode(base64);
    }


    private static final class DefaultTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }
    }
}
