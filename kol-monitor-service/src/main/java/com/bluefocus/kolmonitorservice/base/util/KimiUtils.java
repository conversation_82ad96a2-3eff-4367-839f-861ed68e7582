package com.bluefocus.kolmonitorservice.base.util;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.domain.gptalk.entity.ChatMessage;
import lombok.Setter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * Kimi接口工具类
 */
@Component
public class KimiUtils {

    private String API_KEY;
    private String API_URL = "https://api.moonshot.cn/v1/chat/completions";

    @Setter
    private String model = "moonshot-v1-128k";

    @Setter
    private int n = 1;

    @Setter
    private List<ChatMessage> messages;

    @Setter
    private double temperature = 0.3;

    public KimiUtils() {

    }

    public KimiUtils(String API_KEY) {
        this.API_KEY = API_KEY;
    }

    public KimiUtils(String API_KEY, String API_URL) {
        this.API_KEY = API_KEY;
        this.API_URL = API_URL;
    }

    public String callKimiAPI() {

        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + this.API_KEY);
        headerMap.put("Content-Type", ContentType.JSON.getValue());
        HashMap<String, Object> params = new HashMap<>();
        params.put("model", this.model);
        params.put("n", this.n);
        params.put("messages", this.messages);
        params.put("temperature", temperature);
        HttpResponse execute = HttpUtil.createPost(this.API_URL).headerMap(headerMap, true).body(JSON.toJSONString(params)).execute();
        return execute.body();
    }

}
