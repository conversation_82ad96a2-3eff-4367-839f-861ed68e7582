package com.bluefocus.kolmonitorservice.base.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.enums.DeleteStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.ProjectStatusEnum;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDataTotalDomainService;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDomainService;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import com.bluefocus.kolmonitorservice.domain.project.entity.ProjectDataTotal;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.redisson.api.RBucket;
import org.springframework.messaging.MessageHeaders;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 */
@Service
@Log4j2
@RequiredArgsConstructor
public class FsNotesListener implements TopicStrategy {

    private static final String TOPIC_NAME = "dws-fs-notes";
    private static final String REDIS_KEY = "kol_monitor_url_";
    private static final int RETRY_COUNT = 2;

    private final ArticleDomainService articleDomainService;

    private final ProjectDomainService projectDomainService;

    private final ProjectDataTotalDomainService projectDataTotalDomainService;

    private final RedisUtils redisUtils;


    @Override
    public String getTopicType() {
        return TOPIC_NAME;
    }

    @Override
    public void consumptionData(String message, MessageHeaders headers) {
        JSONObject map = (JSONObject) JSON.parse(message);
        if (ObjectUtil.isEmpty(map.get("project_id")) || ObjectUtil.isEmpty(map.get("notes")) ||
                ObjectUtil.isEmpty(map.get("create_time"))) {
            log.error("topic=dws-fs-notes,有空数据，data：[{}]", message);
            return;
        }
        Long projectId = Long.valueOf(map.get("project_id").toString());
        Project project = projectDomainService.getById(projectId);
        if (ObjectUtil.isNull(project)) {
            log.error("dws-fs-notes消费项目为空,id={}", projectId);
            return;
        }
        String monitorPeriod = project.getMonitorPeriod();
        LocalDate endDay = LocalDate.parse(monitorPeriod.split("~")[1]);
        boolean isExpiration = LocalDate.now().isEqual(endDay) || LocalDate.now().isAfter(endDay);

        if (project.getDeleteStatus().equals(DeleteStatusEnum.DELETE.getCode())
                || project.getStatus().equals(ProjectStatusEnum.MONITORED.getIndex())) {
            log.error("项目[{}]已经结束监测，不能继续消费数据，data：[{}]", projectId, message);
            return;
        }
        if (project.getStatus().equals(ProjectStatusEnum.MONITORING.getIndex()) && isExpiration) {
            saveProjectData(project);
        }
        saveProjectDataTotal(projectId, map);

        JSONArray dataList = JSON.parseArray(map.getString("notes"));
        if (CollectionUtil.isNotEmpty(dataList)) {
            for (int i = 0; i < dataList.size(); i++) {
                JSONObject articleData = dataList.getJSONObject(i);
                if (ObjectUtil.isEmpty(articleData.get("article_id"))) {
                    continue;
                }
                Long articleId = Long.valueOf(articleData.getString("article_id"));
                Article article = articleDomainService.getById(articleId);
                if (article.getDeleteStatus().equals(DeleteStatusEnum.DELETE.getCode())) {
                    continue;
                }
                article.setKolName(ObjectUtil.isEmpty(articleData.get("kol_media_name")) ? null : articleData.getString("kol_media_name"));
                article.setKolMediaId(ObjectUtil.isEmpty(articleData.get("kol_media_id")) ? null : articleData.getString("kol_media_id"));
                article.setArticleTitle(ObjectUtil.isEmpty(articleData.get("article_title")) ? null : articleData.getString("article_title"));

                String code = checkCode(articleData, article);
                if (isExpiration && code.equals(MonitorStatusEnum.MONITORING.getCode())) {
                    code = MonitorStatusEnum.MONITOR_FINISH.getCode();
                }
                if (isExpiration && (code.equals(MonitorStatusEnum.MONITOR_ING_ERROR.getCode()) || code.equals(MonitorStatusEnum.MONITOR_END_ERROR.getCode()))) {
                    code = MonitorStatusEnum.MONITOR_FINISH_ERROR.getCode();
                }

                article.setStatus(code);
                article.setReadNum(ObjectUtil.isEmpty(articleData.get("read_num")) ? null : Integer.valueOf(articleData.getString("read_num")));
                article.setLikeNum(ObjectUtil.isEmpty(articleData.get("like_num")) ? null : Integer.valueOf(articleData.getString("like_num")));
                article.setCommentNum(ObjectUtil.isEmpty(articleData.get("comment_num")) ? null : Integer.valueOf(articleData.getString("comment_num")));
                article.setShareNum(ObjectUtil.isEmpty(articleData.get("share_num")) ? null : Integer.valueOf(articleData.getString("share_num")));
                article.setCollectionNum(ObjectUtil.isEmpty(articleData.get("collection_num")) ? null : Integer.valueOf(articleData.getString("collection_num")));
                article.setInteractionNum(ObjectUtil.isEmpty(articleData.get("interaction_num")) ? null : Integer.valueOf(articleData.getString("interaction_num")));
                article.setConsumeDate(ObjectUtil.isEmpty(articleData.get("update_day")) ? null : LocalDate.parse(articleData.getString("update_day")));
                article.setUpdateTime(LocalDateTime.now());
                article.setCrawlerStateDesc(ObjectUtil.isEmpty(articleData.get("state_desc")) ? null : articleData.getString("state_desc"));

                article.setArticlePlatId(articleData.getString("article_plat_id"));
                article.setImagesCover(ObjectUtil.isEmpty(articleData.get("images_cover")) ? null : articleData.getString("images_cover"));
                article.setHeadUrl(ObjectUtil.isEmpty(articleData.get("head_url")) ? null : articleData.getString("head_url"));
                article.setHomeUrl(ObjectUtil.isEmpty(articleData.get("home_url")) ? null : articleData.getString("home_url"));
                article.setPostTime(ObjectUtil.isEmpty(articleData.get("post_time")) ? null : articleData.getString("post_time"));
                article.setExposure(ObjectUtil.isEmpty(articleData.get("exposure")) ? null : Integer.valueOf(articleData.getString("exposure")));
                article.setFansNum(ObjectUtil.isEmpty(articleData.get("fans_num")) ? null : Integer.valueOf(articleData.getString("fans_num")));
                article.setFollowNum(ObjectUtil.isEmpty(articleData.get("follow_num")) ? null : Integer.valueOf(articleData.getString("follow_num")));
                article.setInteractionRate(ObjectUtil.isEmpty(articleData.get("interaction_rate")) ? null : new BigDecimal(articleData.getString("interaction_rate")));
                article.setFinishRate(ObjectUtil.isEmpty(articleData.get("finish_rate")) ? null : new BigDecimal(articleData.getString("finish_rate")));
                article.setBulletNum(ObjectUtil.isEmpty(articleData.get("bullet_num")) ? null : Integer.valueOf(articleData.getString("bullet_num")));
                article.setCoinNum(ObjectUtil.isEmpty(articleData.get("coin_num")) ? null : Integer.valueOf(articleData.getString("coin_num")));

                articleDomainService.update(article);
            }
        }

    }

    // 爬虫状态，0失败，1成功，2无链接，4未入驻蒲公英，5抖音没有阅读量, 6手动补充数据，9初始状态
    private String checkCode(JSONObject articleData, Article article) {
        String code;
        switch (articleData.getString("state")) {
            case "0":
            case "9":
                boolean beforeDayFlag = LocalTime.MIN.until(LocalTime.now(), ChronoUnit.HOURS) >= 8;
                LocalDateTime poleTime = LocalDateTime.parse(beforeDayFlag ? LocalDate.now().plusDays(1) + "T08:00:00" : LocalDate.now() + "T08:00:00");
                long timeToLive = LocalDateTime.now().until(poleTime, ChronoUnit.SECONDS);
                String key = REDIS_KEY + article.getId();
                RBucket<Object> rBucket = redisUtils.getString(key);
                int count = 0;
                if (!rBucket.isExists()) {
                    rBucket.set(count, timeToLive, TimeUnit.SECONDS);
                    code = MonitorStatusEnum.MONITOR_ING_ERROR.getCode();
                } else {
                    count = Integer.parseInt(rBucket.get().toString());
                    if (count >= RETRY_COUNT) {
                        code = MonitorStatusEnum.MONITOR_END_ERROR.getCode();
                    } else {
                        code = MonitorStatusEnum.MONITOR_ING_ERROR.getCode();
                    }
                    rBucket.set(++count, timeToLive, TimeUnit.SECONDS);
                    log.error("链接异常重试第[{}]次,articleId：[{}]", count, article.getId());
                }
                break;
            case "2":
                code = MonitorStatusEnum.MONITOR_VAIL_ERROR.getCode();
                break;
            case "3":
                code = MonitorStatusEnum.MONITOR_ING_ERROR.getCode();
                break;
            case "4":
                code = MonitorStatusEnum.MONITOR_END_ERROR.getCode();
                break;
            case "1":
            case "5":
            case "6":
            default:
                code = MonitorStatusEnum.MONITORING.getCode();
        }
        return code;
    }

    private void saveProjectData(Project project) {
        project.setStatus(ProjectStatusEnum.MONITORED.getIndex());
        project.setUpdateTime(LocalDateTime.now());
        projectDomainService.updateById(project);
    }

    private void saveProjectDataTotal(Long projectId, JSONObject map) {
        ProjectDataTotal projectDataTotal = projectDataTotalDomainService.getById(projectId);
        if (ObjectUtil.isEmpty(projectDataTotal)) {
            projectDataTotal = new ProjectDataTotal();
            projectDataTotal.setProjectId(projectId);
            projectDataTotal.setCreateTime(LocalDateTime.now());
        }
        Integer noteNum = map.getInteger("total_url_num") == null ? 0 : map.getInteger("total_url_num");
        projectDataTotal.setTotalKolNum(map.getInteger("total_kol_num"));
        projectDataTotal.setTotalUrlNum(noteNum);
        projectDataTotal.setTotalPlantNum(map.getInteger("total_plant_num"));
        Long totalCollectionNum = map.getLong("total_collection_num");
        projectDataTotal.setTotalCollectionNum(totalCollectionNum);
        Long totalCommentNum = map.getLong("total_comment_num");
        projectDataTotal.setTotalCommentNum(totalCommentNum);
        Long interactionNum = map.getLong("total_interaction_num");
        Long totalReadNum = map.getLong("total_read_num");
        projectDataTotal.setTotalInteractionNum(interactionNum);
        projectDataTotal.setTotalInteractionRate(getRate(interactionNum, totalReadNum));
        Long totalLikeNum = map.getLong("total_like_num");
        projectDataTotal.setTotalLikeNum(totalLikeNum);
        Long totalShareNum = map.getLong("total_share_num");
        projectDataTotal.setTotalReadNum(totalReadNum);
        projectDataTotal.setTotalShareNum(totalShareNum);
        Long totalFollowNum = map.getLong("total_follow_num");
        projectDataTotal.setTotalFollowNum(totalFollowNum);
        Long totalExposure = map.getLong("total_exposure");
        projectDataTotal.setTotalExposure(totalExposure);
        Long totalBulletNum = map.getLong("total_bullet_num");
        projectDataTotal.setTotalBulletNum(totalBulletNum);
        Long totalCoinNum = map.getLong("total_coin_num");
        projectDataTotal.setTotalCoinNum(totalCoinNum);
        projectDataTotal.setConsumeTime(map.getLong("create_time"));
        projectDataTotal.setUpdateTime(LocalDateTime.now());
        projectDataTotal.setAvgReadNum(noteNum > 0 && totalReadNum != null ? totalReadNum / noteNum : null);
        projectDataTotal.setAvgExposureNum(noteNum > 0 && totalExposure != null ? totalExposure / noteNum : null);
        projectDataTotal.setAvgLikeNum(noteNum > 0 && totalLikeNum != null ? totalLikeNum / noteNum : null);
        projectDataTotal.setAvgCommentNum(noteNum > 0 && totalCommentNum != null ? totalCommentNum / noteNum : null);
        projectDataTotal.setAvgInteractionNum(noteNum > 0 && interactionNum != null ? interactionNum / noteNum : null);
        projectDataTotal.setAvgCollectionNum(noteNum > 0 && totalCollectionNum != null ? totalCollectionNum / noteNum : null);
        projectDataTotal.setAvgShareNum(noteNum > 0 && totalShareNum != null ? totalShareNum / noteNum : null);
        projectDataTotal.setAvgFollowNum(noteNum > 0 && totalFollowNum != null ? totalFollowNum / noteNum : null);
        projectDataTotal.setAvgBulletNum(noteNum > 0 && totalBulletNum != null ? totalBulletNum / noteNum : null);
        projectDataTotal.setAvgCoinNum(noteNum > 0 && totalCoinNum != null ? totalCoinNum / noteNum : null);
        projectDataTotalDomainService.saveOrUpdate(projectDataTotal);
    }

    private BigDecimal getRate(Long interactionNum, Long totalReadNum) {
        if (interactionNum == null || totalReadNum == null || totalReadNum <= 0) {
            return null;
        }
        return new BigDecimal(interactionNum).divide(new BigDecimal(totalReadNum), 4, RoundingMode.HALF_UP);
    }


}