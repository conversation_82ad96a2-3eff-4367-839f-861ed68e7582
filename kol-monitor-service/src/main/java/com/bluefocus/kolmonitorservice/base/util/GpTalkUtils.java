package com.bluefocus.kolmonitorservice.base.util;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.domain.gptalk.entity.ChatMessage;
import com.bluefocus.kolmonitorservice.domain.gptalk.entity.GPTalkFinetuneEntity;
import com.bluefocus.kolmonitorservice.domain.gptalk.response.GPTalkChatMessageResponse;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

@Component
public class GpTalkUtils {

    @Value("azure.serverUrl")
    private String serverUrl;

    @Value("azure.api-key")
    private String APIKEY;


    @Value("azure.model-chat")
    private String model = "gpt-4";

    @Setter
    private GPTalkFinetuneEntity finetune;

    public GpTalkUtils() {

    }

    public GpTalkUtils(String serverUrl, String key) {
        this.serverUrl = serverUrl;
        this.APIKEY = key;
    }

    public GpTalkUtils(String endPoint, String key, String model) {
        this(endPoint, key);
        this.model = model;
    }

    public GPTalkFinetuneEntity getFinetune() {
        if (this.finetune == null) {
            finetune = new GPTalkFinetuneEntity();
        }
        return this.finetune;
    }

    public GPTalkChatMessageResponse execute() {
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("api-key", this.APIKEY);
        headerMap.put("Content-Type", ContentType.JSON.getValue());
        HttpResponse execute = HttpUtil.createPost(this.serverUrl + this.model).headerMap(headerMap, true).body(JSON.toJSONString(finetune)).execute();
        String body = execute.body();
        System.out.println(body);
        GPTalkChatMessageResponse tRsp = new GPTalkChatMessageResponse();
        if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
            tRsp = JSON.parseObject(body, GPTalkChatMessageResponse.class);
        }
        return tRsp;
    }

    public String chat(List<ChatMessage> chatMessages) {
        getFinetune().setMessages(chatMessages);
        GPTalkChatMessageResponse execute = execute();
        return execute.getChoices().get(0).getMessage().getContent();
    }

    public String chat(String prompt) {
        getFinetune().setPrompt(prompt);
        GPTalkChatMessageResponse execute = execute();
        return execute.getChoices().get(0).getMessage().getContent();
    }

    public void image(List<ChatMessage> chatMessages) {
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("api-key", this.APIKEY);
        headerMap.put("Content-Type", ContentType.JSON.getValue());

        GPTalkFinetuneEntity ft = getFinetune();
        ft.setMessages(chatMessages);
        HttpResponse execute = HttpUtil.createPost(this.serverUrl + this.model).headerMap(headerMap, true).body(JSON.toJSONString(ft)).execute();
        String body = execute.body();

        System.out.println(body);
    }

}
