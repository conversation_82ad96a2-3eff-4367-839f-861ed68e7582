package com.bluefocus.kolmonitorservice.base.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */

@RequiredArgsConstructor
@Getter
public enum MonitorStatusEnum {

    /**
     * 监测状态
     */
    PRE_MONITOR("00", "校验通过"),
    MONITORING("01", "监测中"),
    MONITOR_FINISH("02", "监测完成"),
    MONITOR_URL_REPEAT("03", "链接重复自动过滤"),
    MONITOR_CHECK_ERROR("04", "格式无法识别"),
    USER_PROFILE_ERROR("05", "不支持监测达人主页链接"),
    DYNAMIC_PROFILE_ERROR("06", "暂不支持监测达人动态"),

    MONITOR_ING_ERROR("11", "链接监测中异常"),
    MONITOR_VAIL_ERROR("12", "链接无效"),
    MONITOR_END_ERROR("13", "获取数据异常"),
    MONITOR_FINISH_ERROR("20", "获取数据异常"),
    ;

    private final String code;
    private final String desc;

    public static String getDescByCode(String code) {
        for (MonitorStatusEnum value : MonitorStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static Boolean suc(String code) {
        if (MONITORING.getCode().equals(code) || PRE_MONITOR.getCode().equals(code)) {
            return true;
        }
        return false;
    }
}