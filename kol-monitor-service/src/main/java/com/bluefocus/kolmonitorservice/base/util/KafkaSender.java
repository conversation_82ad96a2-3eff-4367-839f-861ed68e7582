package com.bluefocus.kolmonitorservice.base.util;

import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class KafkaSender<T> {

    private final static Logger LOGGER = LoggerFactory.getLogger(KafkaSender.class);

    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;

    /**
     * 发送消息
     *
     * @param topic
     * @param data
     */
    public void send(String topic, T data) {
        LOGGER.info("开始发送消息");
        kafkaTemplate.send(topic, data);
        LOGGER.info("消息topic={}, data={}", topic, JSON.toJSONString(data));
        LOGGER.info("发送消息成功");
    }

    public void sendByKey(String topic, String key, T data) {
        LOGGER.info("开始发送消息 topic={}, data={}", topic, JSON.toJSONString(data));
        kafkaTemplate.send(topic, key, data);
        LOGGER.info("发送消息成功");
    }

}
