package com.bluefocus.kolmonitorservice.base.util;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.application.service.route.impl.DataStoryPlatform;
import com.bluefocus.kolmonitorservice.base.common.TokenCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.concurrent.TimeUnit;

/**
 * 数说接口工具类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataStoryUtils {

    @Value("${dataStory.login.serverUrl}")
    private String serverUrl;

    @Value("${dataStory.login.username}")
    private String username;

    @Value("${dataStory.login.password}")
    private String password;

    @Value("${dataStory.login.redisKey}")
    private String dataStoryLoginToken;

    @Resource
    RedisUtils redisUtils;

    private int connectTimeout = 15000;
    private int readTimeout = 30000;

    public DataStoryUtils() {
    }

    public DataStoryUtils(String username, String password) {
        this.username = username;
        this.password = password;
    }

    public DataStoryUtils(String username, String password, String serverUrl) {
        this(username, password);
        this.serverUrl = serverUrl;
    }

    public DataStoryUtils(String username, String password, String serverUrl, int connectTimeout, int readTimeout) {
        this(username, password, serverUrl);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public TokenCommon login(String platform) {

        HashMap<String, String> queryMap = new HashMap<>(4);
        queryMap.put("username", this.username);
        queryMap.put("password", this.password);

        UrlQuery urlQuery = UrlQuery.of(queryMap);
        HashMap<String, String> headerMap = new HashMap<>(4);
        headerMap.put("Content-Type", ContentType.FORM_URLENCODED.getValue());

        HttpResponse execute = HttpUtil.createPost(this.serverUrl)
                .setConnectionTimeout(this.connectTimeout)
                .setReadTimeout(this.readTimeout)
                .headerMap(headerMap, true)
                .body(urlQuery.build(Charset.defaultCharset())).execute();

        String body = execute.body();
        JSONObject res = JSON.parseObject(body);
        if (res.getBoolean("success") && res.getString("data") != null) {
            String token = res.getString("data");
            return new TokenCommon(DataStoryPlatform.DATA_STORY, this.username, token);
        }
        log.error("请求数说聚合token接口异常,e={}", res);
        return new TokenCommon(DataStoryPlatform.DATA_STORY, this.username, null);
    }

    public TokenCommon getToken() {
        RBucket<Object> redis = redisUtils.getString(dataStoryLoginToken + ":" + LocalDate.now());
        if (redis.isExists()) {
            return new TokenCommon(DataStoryPlatform.DATA_STORY, null, redis.get().toString());
        }
        TokenCommon token = login(DataStoryPlatform.DATA_STORY);
        if (token.getSuccess()) {
            redis.set(token.getToken(), 1, TimeUnit.DAYS);
        }
        return token;
    }

    public TokenCommon refreshLogin() throws ApiException {
        RBucket<Object> redis = redisUtils.getString(dataStoryLoginToken);
        if (redis.isExists()) {
            long ttl = redis.remainTimeToLive();
            long time = 24 * 60 * 60 * 100L;
            if (ttl > time) {
                return new TokenCommon(DataStoryPlatform.DATA_STORY, this.username, redis.get().toString());
            } else {
                TokenCommon token = login(DataStoryPlatform.DATA_STORY);
                if (token.getSuccess()) {
                    redis.set(token.getToken(), 6, TimeUnit.DAYS);
                }
                return token;
            }
        }
        return login(DataStoryPlatform.DATA_STORY);
    }

}
