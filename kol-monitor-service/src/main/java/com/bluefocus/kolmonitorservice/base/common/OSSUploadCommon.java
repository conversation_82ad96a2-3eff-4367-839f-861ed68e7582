package com.bluefocus.kolmonitorservice.base.common;

import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.bluefocus.basebean.exception.ResponseException;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorservice.base.enums.MimeEnum;
import com.bluefocus.kolmonitorservice.base.util.ImageUtils;
import com.bluefocus.kolmonitorservice.base.util.PictureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

/**
 * @author: yjLiu
 * @date: 2024/5/22 10:42
 * @description:
 */
@Slf4j
@Component
public class OSSUploadCommon {
    @Resource
    private OSSClient ossClient;

    @Value("${aliyun.bucketName}")
    private String bucketName;

    /**
     * @param filePath 全路径
     * @param fileName 下载文件名称
     */
    public String compressAndUpload(MultipartFile file, String filePath, String fileName) {

        Assert.notNull(file, "上传文件不能为空");
        Assert.isFalse(null == file.getOriginalFilename() && !filePath.contains(StrUtil.DOT), "上传文件没有原始文件名，且路径没有自设名称");

        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            byte[] bytes = ImageUtils.imageInputStreamToBytes(inputStream);
            bytes = PictureUtils.compressPictureUpload(bytes, 0.9);
            putFile(new ByteArrayInputStream(bytes), filePath, fileName);
        } catch (Exception e) {
            log.error("阿里云OSS请求异常【{}】", e.toString());
            throw new ResponseException("阿里云OSS请求异常" + e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }

            } catch (Exception e) {
                throw new ResponseException("阿里云OSS请求异常" + e);
            }
        }
        return getImgUrl(filePath);
    }

    /**
     * @param filePath 全路径
     * @param fileName 下载文件名称
     */
    public String uploadImg2Oss(MultipartFile file, String filePath, String fileName) {

        Assert.notNull(file, "上传文件不能为空");
        Assert.isFalse(null == file.getOriginalFilename() && !filePath.contains(StrUtil.DOT), "上传文件没有原始文件名，且路径没有自设名称");

        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
            PutObjectResult putResult = putFile(inputStream, filePath, fileName);
        } catch (Exception e) {
            log.error("阿里云OSS请求异常【{}】", e.toString());
            throw new ResponseException("阿里云OSS请求异常" + e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }

            } catch (Exception e) {
                throw new ResponseException("阿里云OSS请求异常" + e);
            }
        }
        return getImgUrl(filePath);
    }

    public String uploadImg2Oss(InputStream inputStream, String filePath, String fileName) {

        Assert.notNull(inputStream, "上传文件不能为空");
        Assert.isFalse(!filePath.contains(StrUtil.DOT), "上传文件没有原始文件名，且路径没有自设名称");
        try {
            PutObjectResult putResult = putFile(inputStream, filePath, fileName);

        } catch (Exception e) {
            log.error("阿里云OSS请求异常【{}】", e.toString());
            throw new ResponseException("阿里云OSS请求异常" + e);
        } finally {
            try {
                inputStream.close();
            } catch (Exception e) {
                throw new ResponseException("阿里云OSS请求异常" + e);
            }
        }

        return getImgUrl(filePath);
    }

    private PutObjectResult putFile(InputStream inputStream, String filePath, String fileName) throws IOException {
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(inputStream.available());
        objectMetadata.setCacheControl("no-cache");
        objectMetadata.setHeader("Pragma", "no-cache");
        objectMetadata.setContentType(MimeEnum.getContentType(filePath.substring(filePath.lastIndexOf(StrUtil.DOT))));
        objectMetadata.setContentDisposition("inline;filename=" + fileName);
        return ossClient.putObject(bucketName, filePath, inputStream, objectMetadata);
    }

    /**
     * 获取图片路径
     *
     * @param fileUrl 文件路径
     * @return 路径
     */
    public String getImgUrl(String fileUrl) {
        if (StringUtils.isNotEmpty(fileUrl)) {
            return this.getUrl(fileUrl);
        }
        return null;
    }

    /**
     * 获得url链接
     *
     * @param key key
     * @return url
     */
    public String getUrl(String key) {
        // 设置URL过期时间为10年  3600l* 1000*24*365*10
        Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 365 * 10);
        // 生成URL
        URL url = ossClient.generatePresignedUrl(bucketName, key, expiration);
        if (url != null) {
            return url.toString();
        }
        return null;
    }
}
