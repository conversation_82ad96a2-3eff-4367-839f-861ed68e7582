package com.bluefocus.kolmonitorservice.domain.media.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaWord;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaWordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【media_word(媒体词云表)】的数据库操作Service实现
 * @createDate 2024-05-21 17:51:36
 */
@Service
@RequiredArgsConstructor
public class MediaWordDomainService extends ServiceImpl<MediaWordMapper, MediaWord> {

    public List<MediaWord> findByHandleIds(List<Long> handleIdList) {
        return list(new LambdaQueryWrapper<MediaWord>().in(MediaWord::getHandleId, handleIdList));
    }

    public MediaWord findByHandleIdAndSource(Long handleId, Integer sourceCode) {
        return getOne(new LambdaQueryWrapper<MediaWord>().eq(MediaWord::getHandleId, handleId).eq(MediaWord::getSourceCode, sourceCode));
    }

    public MediaWord findByObjIdAndSource(Long mediaObjectsId, Integer sourceCode) {
        return getOne(new LambdaQueryWrapper<MediaWord>().eq(MediaWord::getMediaObjectsId, mediaObjectsId).eq(MediaWord::getSourceCode, sourceCode));
    }

    public void deleteByMediaObjects(Long objId) {
        if (null != objId) {
            this.lambdaUpdate().eq(MediaWord::getMediaObjectsId, objId).remove();
        }
    }

    public MediaWord updateImgById(MediaWord mediaWord) {

        this.lambdaUpdate().set(MediaWord::getGenImg, null)
                .set(MediaWord::getOldImg, mediaWord.getGenImg()).eq(MediaWord::getId, mediaWord.getId()).update();
        return mediaWord;
    }
}




