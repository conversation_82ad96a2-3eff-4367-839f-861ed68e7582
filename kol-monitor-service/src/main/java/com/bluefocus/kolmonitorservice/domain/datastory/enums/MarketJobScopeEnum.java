package com.bluefocus.kolmonitorservice.domain.datastory.enums;

import lombok.Getter;

@Getter
public enum MarketJobScopeEnum {
    XHS(13, 11);
    /**
     * 微博-6
     * 微信-7
     * 抖音-8
     * 豆瓣-9
     * 知乎-10
     * 今日头条-11
     * 快手-12
     * 小红书-13
     * 哔哩哔哩-14
     * 汽车之家-15
     * 太平洋汽车网-16
     * 易车网-17
     * 爱卡汽车网-18
     * 懂车帝-53
     * 得物-71
     * 车质网-72
     * 酷安-74
     * OPPO社区-75
     * 晋江文学城-76
     * 百度贴吧-77
     * NGA论坛-78
     * 虎扑-83
     * lofter-84
     * 好游快爆-85
     * 黑猫投诉-92
     */
    private final Integer appId;

    /**
     * 豆瓣小组-1
     * 豆瓣电影-2
     * 豆瓣电视剧-3
     * 微博-4
     * 今日头条-5
     * 快手-6
     * 抖音-7
     * 知乎-8
     * 哔哩哔哩-9
     * 微信-10
     * 小红书-11
     * B站动态-56
     * 汽车之家新闻-51
     * 易车网新闻-52
     * 爱卡汽车网新闻-53
     * 懂车帝pc_新闻-54
     * 太平洋汽车网新闻-55
     * 得物笔记-57
     * 车质网-58
     * 得物商品-60
     * 酷安-61
     * OPPO社区-62
     * 晋江文学城-63
     * 百度贴吧-64
     * NGA论坛-65
     * 虎扑-67
     * lofter-68
     * 好游快爆-71
     * 黑猫投诉-76
     */
    private final Integer scopeId;

    private MarketJobScopeEnum(Integer appId, Integer scopeId) {
        this.appId = appId;
        this.scopeId = scopeId;
    }


}
