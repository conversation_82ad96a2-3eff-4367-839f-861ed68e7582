package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTotalVolumeResponse;
import org.springframework.beans.factory.annotation.Value;


/**
 * 数说-总声量统计接口
 */
public class DataStoryTotalVolumeRequest extends DefaultDataStoryRequest<DataStoryTotalVolumeResponse> {

    @Value("${dataStory.api.totalVolume}")
    private String apiMethodName;

    public DataStoryTotalVolumeRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/totalVolume";
    }

    @Override
    public Class<DataStoryTotalVolumeResponse> getResponseClass() {
        return DataStoryTotalVolumeResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSources(), "sources");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
