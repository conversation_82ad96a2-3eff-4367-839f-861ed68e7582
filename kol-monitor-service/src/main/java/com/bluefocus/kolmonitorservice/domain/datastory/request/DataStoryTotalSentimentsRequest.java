package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTotalSentimentsResponse;
import org.springframework.beans.factory.annotation.Value;

/**
 * 数说-总情感度统计接口
 */
public class DataStoryTotalSentimentsRequest extends DefaultDataStoryRequest<DataStoryTotalSentimentsResponse> {

    @Value("${dataStory.api.totalSentiments}")
    private String apiMethodName;

    public DataStoryTotalSentimentsRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/totalSentiments";
    }

    @Override
    public Class<DataStoryTotalSentimentsResponse> getResponseClass() {
        return DataStoryTotalSentimentsResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSources(), "sources");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
