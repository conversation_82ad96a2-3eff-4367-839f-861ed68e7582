package com.bluefocus.kolmonitorservice.domain.wordart;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * word-cloud-构造器
 *
 * <AUTHOR>
 */
@Getter
@Service("wordCloudClient")
public class DefaultWordCloudClient implements Client {
    private final static Logger logger = LoggerFactory.getLogger(DefaultWordCloudClient.class);

    @Value("${word-cloud.serverUrl}")
    protected String serverUrl;
    protected int connectTimeout = 60000;
    protected int readTimeout = 60000;
    protected int timeout = 60000;

    @Setter
    protected boolean needCheckRequest = true;

    public DefaultWordCloudClient() {
    }

    public DefaultWordCloudClient(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public DefaultWordCloudClient(String serverUrl, int connectTimeout, int readTimeout) {
        this(serverUrl);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    /**
     * {'code':401,'data':'图片获取失败'}
     * {'code':400,'data':'词云图生成异常'}
     * {'code':200,'data':'xxxx' } 表示成功
     */
    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {

        W resp = ClientCommon.check(this.needCheckRequest, request);
        if (request == null) {
            return resp;
        }

        HttpResponse httpResponse;
        try {
            long s = System.currentTimeMillis();
            httpResponse = HttpUtil.createPost(this.serverUrl + request.getApiMethodName())
                    .setConnectionTimeout(this.connectTimeout)
                    .setReadTimeout(this.readTimeout)
                    .headerMap(request.getHeaderMap(), true)
                    .body(request.getParams())
                    .execute();

            resp = request.getResponseClass().newInstance();

            if (httpResponse.isOk()) {
                resp = JSON.parseObject(httpResponse.body(), request.getResponseClass());
                resp.setSuccess(httpResponse.isOk());
            } else {
                logger.error("请求wordCloud异常，code={} body={}", httpResponse.getStatus(),httpResponse.body());
                resp.setCode("-1");
                resp.setSuccess(false);
                resp.setMsg(httpResponse.body());
            }

            resp.setRequestUrl(this.serverUrl + request.getApiMethodName());
            logger.info("DefaultWordCloudClient:执行耗时：{}ms", System.currentTimeMillis() - s);
        } catch (Exception e) {
            logger.error("请求wordCloud失败，e=", e);
            try {
                resp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            resp.setCode("-1");
            resp.setMsg(e.getMessage());
            return resp;
        }

        return resp;
    }

}
