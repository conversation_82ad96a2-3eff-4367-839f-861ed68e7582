package com.bluefocus.kolmonitorservice.domain.datastory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import lombok.Setter;

import java.util.*;

public abstract class DefaultDataStoryRequest<T extends Response> implements Request<T> {

    @Setter
    protected DataStoryEntity param;

    protected Map<String, String> headerMap; // HTTP请求头参数
    protected HashMap<String, Object> udfParams; // 自定义表单参数
    protected String timestamp; // 请求时间戳

    public DefaultDataStoryRequest(DataStoryEntity param) {
        this.param = param;
    }

    /**
     * 添加URL自定义请求参数。
     */
    public void putOtherTextParam(String key, Object value) {
        if (this.udfParams == null) {
            this.udfParams = new HashMap<>();
        }
        this.udfParams.put(key, value);
    }

    @Override
    public String getTimestamp() {
        return DateUtil.now();
    }

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
            this.headerMap.put("Content-Type", ContentType.JSON.getValue());
        }
        return this.headerMap;
    }

    @Override
    public String getParams() {
        JSONObject params = new JSONObject();
        params.put("sentiments", Arrays.asList("-1", "0", "1"));
        HashMap<String, Object> extraCondition = new HashMap<>(4);
        buildExtraCondition(extraCondition, ExtraConditionEnum.DOUYIN.getMap(), ",");
        buildExtraCondition(extraCondition, ExtraConditionEnum.XIAOHONGSHU.getMap(), ",");
        buildExtraCondition(extraCondition, ExtraConditionEnum.WEIBO.getMap(), ",");
        buildExtraCondition(extraCondition, ExtraConditionEnum.BLIBLI.getMap(), ",");
        params.put("extra_condition", extraCondition);

        if (this.udfParams != null) {
            params.putAll(this.udfParams);
        }

        JSONObject parseObject = JSON.parseObject(JSON.toJSONString(this.param));
        params.putAll(parseObject);
        return params.toJSONString();
    }

    private void buildExtraCondition(HashMap<String, Object> extraCondition, HashMap<String, Object> siteMap, String splitKey) {
        siteMap.keySet().forEach(key -> {
            HashMap<String, String> sits = (HashMap<String, String>) siteMap.get(key);
            if (extraCondition.containsKey(key)) {
                HashMap<String, String> oldMap = (HashMap<String, String>) extraCondition.get(key);
                sits.keySet().forEach(k -> {
                    String s = sits.get(k);
                    String oldS = oldMap.get(k);
                    if (oldMap.containsKey(k)) {
                        String[] s1 = oldS.split(splitKey);
                        String[] s2 = s.split(splitKey);
                        List<String> sList = new ArrayList<>();
                        sList.addAll(Arrays.asList(s1));
                        sList.addAll(Arrays.asList(s2));
                        Set<String> uniqueSet = new HashSet<>(sList);
                        oldMap.put(k, String.join(splitKey, uniqueSet));
                    } else {
                        oldMap.put(k, s);
                    }
                });
            } else {
                extraCondition.put(key, sits);
            }
        });
    }

    /**
     * 添加头部自定义请求参数。
     */
    public void putHeaderParam(String key, String value) {
        getHeaderMap().put(key, value);
    }

    public void checkDefaultValue() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.param.getKeyword(), "keyword");
        RequestCheckUtils.checkNotEmpty(this.param.getStartTime(), "startTime");
        RequestCheckUtils.checkNotEmpty(this.param.getEndTime(), "endTime");
    }

}
