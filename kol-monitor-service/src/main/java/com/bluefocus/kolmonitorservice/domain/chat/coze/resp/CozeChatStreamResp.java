package com.bluefocus.kolmonitorservice.domain.chat.coze.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @author: yj<PERSON><PERSON>
 * @date: 0021 2024/10/21 17:10
 * @description:
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class CozeChatStreamResp extends Response {

    @JSONField(name = "data")
    private ChatDetail data;

    @NoArgsConstructor
    @Data
    public static class ChatDetail {
        // 消息Id
        @JSONField(name = "id")
        private String id;
        @J<PERSON><PERSON>ield(name = "chat_id", alternateNames = {"id"})
        private String chatId;
        @JSONField(name = "conversation_id")
        private String conversationId;
        @JSONField(name = "bot_id")
        private String botId;
        @JSONField(name = "content")
        private String content;
        @JSONField(name = "content_type")
        private String contentType;
        @J<PERSON><PERSON><PERSON>(name = "role")
        private String role;
        @JSONField(name = "type")
        private String type;
        @JSO<PERSON>ield(name = "status")
        private String status;

        @JSONField(name = "conversation_id")
        private String conversation_id;
        @JSONField(name = "chat_id")
        private String chat_id;
        @JSONField(name = "bot_id")
        private String bot_id;
    }

}
