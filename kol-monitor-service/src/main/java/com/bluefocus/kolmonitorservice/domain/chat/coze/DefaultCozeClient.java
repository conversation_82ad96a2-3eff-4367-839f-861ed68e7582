package com.bluefocus.kolmonitorservice.domain.chat.coze;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import com.bluefocus.kolmonitorservice.domain.chat.coze.req.ChatCreateRequest;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.CozeChatStreamResp;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.function.Consumer;


/**
 * 数说-构造器
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DefaultCozeClient implements Client {

    @Value("${coze.api.host}")
    private String host;

    /**
     * 默认连接超时时间为15秒
     */
    private int connectTimeout = 120000;
    /**
     * 默认响应超时时间为30秒
     */
    private int readTimeout = 120000;

    @Setter
    private boolean needCheckRequest = true;
    private WebClient webClient = null;

    private WebClient gebClient(String host) {
        if (null != webClient) {
            return webClient;
        }
        this.webClient = WebClient.builder()
                .baseUrl(host)
                .build();
        return webClient;
    }

    public DefaultCozeClient() {
    }

    public DefaultCozeClient(String serverUrl) {
        this.host = serverUrl;
    }

    public DefaultCozeClient(String serverUrl, int connectTimeout, int readTimeout) {
        this(serverUrl);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {

        W tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }

        String params = request.getParams();
        HttpRequest httpRequest;
        if ("POST".equals(request.getMethod())) {
            httpRequest = HttpUtil.createPost(this.host + request.getApiMethodName())
                    .body(params);
        } else {
            httpRequest = HttpUtil.createGet(this.host + request.getApiMethodName());
        }

        try {
            HttpResponse httpResponse = httpRequest.setConnectionTimeout(this.connectTimeout)
                    .setReadTimeout(this.readTimeout)
                    .headerMap(request.getHeaderMap(), true)
                    .execute();

            String body = httpResponse.body();
            if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
                tRsp = JSON.parseObject(body, request.getResponseClass());
            } else {
                tRsp = request.getResponseClass().newInstance();
            }
            tRsp.setBody(body);
            tRsp.setRequestUrl(this.host + request.getApiMethodName());
            tRsp.setHeaderContent(httpResponse.headers());

        } catch (Exception e) {
            try {
                tRsp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            tRsp.setCode("-1");
            tRsp.setMsg(e.getMessage());
            return tRsp;
        }
        return tRsp;
    }

    public void doExecute(ChatCreateRequest request, Consumer<CozeChatStreamResp.ChatDetail> responseConsumer) {
        log.info("开始流式请求=======================》");

        Flux<CozeChatStreamResp.ChatDetail> responseFlux = gebClient(host)
                .post()
                .uri(request.getApiMethodName())
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> {
                    Map<String, String> headerMap = request.getHeaderMap();
                    for (String key : headerMap.keySet()) {
                        httpHeaders.put(key, Collections.singletonList(headerMap.get(key)));
                    }
                })
                .body(Mono.just(request.getParams()), String.class)
                .retrieve()
                .bodyToFlux(CozeChatStreamResp.ChatDetail.class);

        responseFlux
                .takeUntil(response -> {
                    if (request.isCancelled()) {
                        return true; // 请求被取消，停止处理
                    }
                    if (null == response.getStatus()) {
                        return false;
                    }
                    return "completed".equals(response.getStatus())
                            || "canceled".equals(response.getStatus())
                            || "verbose".equals(response.getType());
                })
                .doOnNext(response -> {
                    if (completed(response)) {
                        log.info("coze响应结果：{}", response);
                        responseConsumer.accept(response);
                    } else {
                        response.setStatus(null != response.getStatus() ? response.getStatus() : "completed");
                        responseConsumer.accept(response);
                        log.warn("响应完成: 响应内容 = {}", response);
                    }
                })
                .doOnError(error -> log.error("执行请求时发生异常", error))
                .subscribe();
    }

    private boolean completed(CozeChatStreamResp.ChatDetail response) {
        boolean b = "verbose".equals(response.getType())
                || "follow_up".equals(response.getType())
                || "completed".equals(response.getStatus())
                || "failed".equals(response.getStatus());
        return !b;
    }

}
