package com.bluefocus.kolmonitorservice.domain.datastory.entity.job;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
@EqualsAndHashCode()
public class JobSourceConfigEntity implements Serializable {

    @JSONField(name = "input")
    private String input = "keyword";

    /**
     * 内容：content
     * 标题：title
     * 封面：cover_ocr_content
     * 音频：audio_asr_content
     * 花字：video_highlight_content
     * 合作品牌：cooperation_brand_labels
     * 贴纸名称：sticker_name
     */
    @JSONField(name = "matchType")
    private List<String> matchType = Arrays.asList("title", "content");

    /**
     * 输出类型
     * 主贴：POST  必传
     * 评论：COMMENT
     * 互动量：INTERACTION
     * 账号信息：USER
     */
    @JSONField(name = "output")
    private List<String> output = Arrays.asList("POST");

    /**
     * 输入条件
     */
    @JSONField(name = "condition")
    private List<JobConditionEntity> condition;

}

