package com.bluefocus.kolmonitorservice.domain.gptalk.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GPTalkChatMessageResponse extends Response {

    @JSONField(name = "id")
    private String id;

    @JSONField(name = "model")
    private String model;

    @JSONField(name = "object")
    private String object;

    @JSONField(name = "usage")
    private GPTalkChatMessageResponse.GPTalkUsage usage;

    @JSONField(name = "created")
    private Long created;

    @JSONField(name = "choices")
    private List<GPTalkChatMessageResponse.GPTalkChoices> choices;

    @JSONField(name = "error")
    private GPTalkChatMessageResponse.GPTalkError error;

    @Data
    @NoArgsConstructor
    public static class GPTalkError {
        @J<PERSON>NField(name = "code")
        private String code;

        @JSONField(name = "message")
        private String message;
    }

    @Data
    @NoArgsConstructor
    public static class GPTalkChoices {

//        @JSONField(name = "content_filter_results")
//        private HashMap<String, HashMap<String, Object>> contentFilterResults;

        @JSONField(name = "message")
        private GPTalkChatMessageResponse.GPTalkMessage message;

    }


    @Data
    @NoArgsConstructor
    public static class GPTalkMessage {

        @JSONField(name = "content")
        private String content;

        @JSONField(name = "role")
        private String role;

    }


    @Data
    @NoArgsConstructor
    public static class GPTalkUsage {
        @JSONField(name = "completion_tokens")
        private Integer completionTokens;

        @JSONField(name = "prompt_tokens")
        private Integer promptTokens;

        @JSONField(name = "total_tokens")
        private Integer totalTokens;
    }
}
