package com.bluefocus.kolmonitorservice.domain.gptalk.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class GPTalkImageResponse extends Response {

    @JSONField(name = "created")
    private Long created;

    @JSONField(name = "data")
    private List<GPTalkImageResponse.GPTalkData> data;

    @JSONField(name = "error")
    private GPTalkImageResponse.GPTalkError error;


    @Data
    @NoArgsConstructor
    public static class GPTalkData {

        @JSONField(alternateNames = {"url", "b64_json"})
        private String image;

        @J<PERSON>NField(name = "revised_prompt")
        private String revisedPrompt;
//
//        @J<PERSON><PERSON>ield(name = "content_filter_results")
//        private HashMap<String, HashMap<String, Object>> contentFilterResults;
//
//        @J<PERSON>NField(name = "prompt_filter_results")
//        private HashMap<String, HashMap<String, Object>> promptFilterResults;
    }

    @Data
    @NoArgsConstructor
    public static class GPTalkError {
        @JSONField(name = "code")
        private String code;

        @JSONField(name = "message")
        private String message;

        @JSONField(name = "type")
        private String type;
    }
}
