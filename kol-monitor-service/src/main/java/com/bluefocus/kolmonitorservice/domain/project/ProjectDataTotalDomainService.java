package com.bluefocus.kolmonitorservice.domain.project;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.domain.project.entity.ProjectDataTotal;
import com.bluefocus.kolmonitorservice.domain.project.repository.ProjectDataTotalMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/7 17:09
 */
@Service
@RequiredArgsConstructor
public class ProjectDataTotalDomainService extends ServiceImpl<ProjectDataTotalMapper, ProjectDataTotal> {

}
