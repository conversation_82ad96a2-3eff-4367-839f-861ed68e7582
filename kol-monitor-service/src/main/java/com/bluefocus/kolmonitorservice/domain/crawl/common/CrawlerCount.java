package com.bluefocus.kolmonitorservice.domain.crawl.common;


import java.util.concurrent.atomic.AtomicInteger;

public class CrawlerCount {

    private static AtomicInteger counter = null;


    // 增加计数值，并检查是否溢出
    public static synchronized int increment() {
        if (counter == null) {
            counter = new AtomicInteger(0);
        }
        int currentValue = counter.get();
        if (currentValue == 10000) {
            counter.set(0);
        } else {
            return counter.getAndIncrement();
        }
        return currentValue;
    }
}