package com.bluefocus.kolmonitorservice.domain.wordart;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.ContentType;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;

import java.util.HashMap;
import java.util.Map;

public abstract class DefaultWordArtRequest<T extends Response> implements Request<T> {

    protected Map<String, String> headerMap;

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>(4);
            this.headerMap.put("Content-Type", ContentType.FORM_URLENCODED.getValue());
        }
        return this.headerMap;
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

    @Override
    public String getTimestamp() {
        return DateUtil.now();
    }

}
