package com.bluefocus.kolmonitorservice.domain.chat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * bot-token
 * @TableName media_chat_bot
 */
@TableName(value ="media_chat_bot")
@Data
public class MediaChatBot implements Serializable {
    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * botid
     */
    @TableField(value = "bot_id")
    private Long botId;

    /**
     * token
     */
    @TableField(value = "token")
    private String token;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 到期时间
     */
    @TableField(value = "expire_time")
    private LocalDateTime expireTime;

    /**
     * 状态 0生效 1失效
     */
    @TableField(value = "status")
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}