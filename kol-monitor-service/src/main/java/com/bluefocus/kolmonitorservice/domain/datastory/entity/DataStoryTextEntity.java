package com.bluefocus.kolmonitorservice.domain.datastory.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode()
public class DataStoryTextEntity implements Serializable {

    @JSONField(name = "sentiment")
    private Integer sentiment;

    @JSONField(name = "review_cnt")
    private String reviewCnt;

    @J<PERSON><PERSON>ield(name = "interaction_cnt")
    private Integer interactionCnt;

    @JSONField(name = "keywords")
    private List<String> keywords;

    @JSONField(name = "audio_asr_content")
    private String audioAsrContent;

    @JSONField(name = "is_robot")
    private String isRobot;

    @JSONField(name = "title")
    private String title;

    @JSONField(name = "content")
    private String content;

    @J<PERSON><PERSON>ield(name = "note_ats")
    private List<String> noteAts;

    @J<PERSON>NField(name = "uid")
    private String uid;

    @JSONField(name = "update_time")
    private String updateTime;

    @JSONField(name = "publish_time")
    private String publishTime;

    @JSONField(name = "augment_time")
    private String augmentTime;

    @JSONField(name = "post_region")
    private String postRegion;

    @JSONField(name = "fingerprint")
    private String fingerprint;

    @JSONField(name = "id")
    private String id;

    @JSONField(name = "note_stickers")
    private List<String> noteStickers;

    @JSONField(name = "post_state")
    private String postState;

    @JSONField(name = "duration_seconds")
    private Integer durationSeconds;

    @JSONField(name = "is_original")
    private String isOriginal;

    @JSONField(name = "item_id")
    private String itemId;

    @JSONField(name = "is_ad")
    private String isAd;

    @JSONField(name = "other_data")
    private String otherData;

    @JSONField(name = "author")
    private String author;

    @JSONField(name = "topics")
    private List<String> topics;

    @JSONField(name = "fav_cnt")
    private String favCnt;

    @JSONField(name = "pic_urls")
    private List<String> picUrls;

    @JSONField(name = "url")
    private String url;

    @JSONField(name = "reposts_cnt")
    private String repostsCnt;

    @JSONField(name = "site_name")
    private String siteName;

    @JSONField(name = "is_main_post")
    private String isMainPost;

    @JSONField(name = "first_update_timestamp")
    private String firstUpdateTimestamp;

    @JSONField(name = "site_id")
    private String siteId;

    @JSONField(name = "content_mode")
    private String contentMode;

    @JSONField(name = "data_type")
    private String dataType;

    @JSONField(name = "cover_ocr_content")
    private String coverOcrContent;

    @JSONField(name = "head_url")
    private String headUrl;

    @JSONField(name = "search_keyword")
    private String searchKeyword;

    @JSONField(name = "video_urls")
    private String videoUrls;

    @JSONField(name = "video_highlight_content")
    private String videoHighlightContent;

    @JSONField(name = "like_cnt")
    private String likeCnt;

    @JSONField(name = "video_content")
    private String videoContent;
}
