package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTotalInteractionResponse;
import org.springframework.beans.factory.annotation.Value;

/**
 * 数说-总互动量统计接口
 */
public class DataStoryTotalInteractionRequest extends DefaultDataStoryRequest<DataStoryTotalInteractionResponse> {

    @Value("${dataStory.api.totalInteraction}")
    private String apiMethodName;

    public DataStoryTotalInteractionRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/totalInteraction";
    }

    @Override
    public Class<DataStoryTotalInteractionResponse> getResponseClass() {
        return DataStoryTotalInteractionResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSources(), "sources");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
