package com.bluefocus.kolmonitorservice.domain.datastory.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Data
@EqualsAndHashCode()
public class DataStoryEntity implements Serializable {

    @J<PERSON><PERSON>ield(name = "keyword")
    private String keyword;

    @JSONField(name = "filterword")
    private String filterWord;

    @J<PERSON>NField(name = "source")
    private String source;

    @J<PERSON>NField(name = "sources")
    private List<String> sources;

    @JSONField(name = "startTime")
    private Long startTime;

    @J<PERSON><PERSON>ield(name = "endTime")
    private Long endTime;

    @J<PERSON><PERSON><PERSON>(name = "sentiments")
    private List<String> sentiments;

    @JSONField(name = "extra_condition")
    private HashMap<String, Object> extraCondition;
}
