package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 媒体笔记表
 * @TableName media_note
 */
@TableName(value ="media_note")
@Data
public class MediaNote implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 媒体对象ID
     */
    private Long mediaObjectsId;

    /**
     * 处理id
     */
    @TableField("handle_id")
    private Long handleId;

    /**
     * 数据源 1小红书 2抖音 3微博
     */
    private Integer sourceCode;

    /**
     * 头像
     */
    private String headImg;

    /**
     * 作者
     */
    private String author;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 笔记链接
     */
    private String noteUrl;

    /**
     * 发布时间
     */
    private Long publishTime;

    /**
     * 互动量
     */
    private Integer interactionCnt;

    /**
     * 点赞量
     */
    private Integer likeCnt;

    /**
     * 收藏量
     */
    private Integer collectionCnt;

    /**
     * 转发量
     */
    private Integer repostsCnt;

    /**
     * 评论量
     */
    private Integer reviewCnt;

    /**
     * 笔记类型
     */
    private String isOriginal;

    /**
     * 命中关键词
     */
    private String searchKeyword;

    /**
     * 封面命中内容
     */
    private String coverOcrContent;

    /**
     * 音频命中内容
     */
    private String audioOcrContent;

    /**
     * 花字命中内容
     */
    private String highlightOcrContent;

    /**
     * 视频文字识别内容
     */
    private String videoContent;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 266006277464104651L;

}