package com.bluefocus.kolmonitorservice.domain.chat.coze.req;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.domain.chat.coze.DefaultCozeRequest;
import com.bluefocus.kolmonitorservice.domain.chat.coze.dto.CozeChatDo;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.CozeChatResp;
import lombok.Builder;
import lombok.Setter;


/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:06
 * @description:
 */
@Builder
public class ChatStopRequest extends DefaultCozeRequest<CozeChatResp> {

    @Setter
    private String chatId;
    @Setter
    private String conversationId;
    @Setter
    private String apiName;
    @Setter
    private String apiMethod;

    @Override
    public String getApiMethodName() {
        return apiName;
    }

    @Override
    public String getMethod() {
        return apiMethod;
    }

    @Override
    public String getParams() {
        JSONObject p = new JSONObject();
        p.put("chat_id", chatId);
        p.put("conversation_id", conversationId);
        return JSON.toJSONString(p);
    }

    @Override
    public Class<CozeChatResp> getResponseClass() {
        return CozeChatResp.class;
    }

    @Override
    public void check() throws ApiRuleException {

    }
}
