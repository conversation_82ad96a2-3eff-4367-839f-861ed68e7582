package com.bluefocus.kolmonitorservice.domain.article.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * kol云图视频表
 * <AUTHOR>
 * @TableName kol_video_cloud
 */
@TableName(value ="kol_video_cloud")
@Data
public class KolVideoCloud implements Serializable {
    /**
     * 视频ID
     */
    @TableId(value = "video_id")
    private String videoId;

    /**
     * KOL号
     */
    @TableField(value = "kol_dy_account")
    private String kolDyAccount;

    /**
     * 回搜率
     */
    @TableField(value = "search_after_rate")
    private BigDecimal searchAfterRate;

    /**
     * 回搜次数
     */
    @TableField(value = "search_after_pv")
    private Integer searchAfterPv;

    /**
     * 回搜人数
     */
    @TableField(value = "search_after_uv")
    private Integer searchAfterUv;

    /**
     * 看后回搜率
     */
    @TableField(value = "search_after_view_rate")
    private BigDecimal searchAfterViewRate;

    /**
     * 看后回搜次数
     */
    @TableField(value = "search_after_view_pv")
    private Integer searchAfterViewPv;

    /**
     * 看后回搜人数
     */
    @TableField(value = "search_after_view_uv")
    private Integer searchAfterViewUv;

    /**
     * A3增长率
     */
    @TableField(value = "add_rate_a3")
    private BigDecimal addRateA3;

    /**
     * 组件类型
     */
    @TableField(value = "plugin_type")
    private String pluginType;

    /**
     * 组件点击率
     */
    @TableField(value = "plugin_click_rate")
    private BigDecimal pluginClickRate;

    /**
     * 组件展示数
     */
    @TableField(value = "plugin_show_num")
    private Integer pluginShowNum;

    /**
     * 组件点击数
     */
    @TableField(value = "plugin_click_num")
    private Integer pluginClickNum;

    /**
     * 1是爆文
     */
    @TableField(value = "is_hot")
    private Integer isHot;

    /**
     * 1质爆加量爆，2质爆，3量爆
     */
    @TableField(value = "hot_type")
    private Integer hotType;

    /**
     * 入驻星图 0未 1是
     */
    @TableField(value = "state")
    private Integer state;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}