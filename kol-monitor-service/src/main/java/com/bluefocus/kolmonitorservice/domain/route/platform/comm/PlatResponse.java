package com.bluefocus.kolmonitorservice.domain.route.platform.comm;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 18:02
 * @description:
 */
@Data
public class PlatResponse implements Serializable {

    private static final long serialVersionUID = 1110999913013185599L;

    @JSONField(name = "code")
    private String code;

    @JSONField(alternateNames = {"msg", "message"})
    private String msg;

    @JSONField(name = "success")
    private boolean success = false;

    @JSONField(name = "data")
    private Object data;
}
