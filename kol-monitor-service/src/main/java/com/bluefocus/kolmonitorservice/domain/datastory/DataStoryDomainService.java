package com.bluefocus.kolmonitorservice.domain.datastory;

import com.bluefocus.kolmonitorservice.base.util.DataStoryUtils;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.request.*;
import com.bluefocus.kolmonitorservice.domain.datastory.request.job.*;
import com.bluefocus.kolmonitorservice.domain.datastory.response.*;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataStoryDomainService {

    private final DataStoryUtils dataStoryUtils;

    private final DefaultDataStoryClient client;

    private final JobDataStoryClient jobClient;

    /**
     * 创建任务
     */
    public DSMarketJobResponse createJob(DSMarketJobRequest request) throws ApiException {
        request.putHeaderParam("Authorization", this.getLoginToken());
        return jobClient.doExecute(request);
    }

    /**
     * 查询任务状态
     */
    public DSJobDetailResponse queryJobDetail(Long jobId) throws ApiException {
        DSJobDetailRequest request = new DSJobDetailRequest();
        request.setJobId(jobId);
        request.putHeaderParam("Authorization", this.getLoginToken());
        return jobClient.doExecute(request);
    }

    public DSJobStatusDetailResponse queryJobStatus(Long jobId, Integer page) throws ApiException {
        DSJobStatusDetailRequest request = new DSJobStatusDetailRequest();
        request.setJobId(jobId);
        request.setPage(page);
        request.putHeaderParam("Authorization", this.getLoginToken());
        return jobClient.doExecute(request);
    }

    public DSJobRerunResponse jobRerun(Long id) throws ApiException {
        DSJobRerunRequest request = new DSJobRerunRequest();
        request.setId(id);
        request.putHeaderParam("Authorization", this.getLoginToken());
        return jobClient.doExecute(request);
    }

    /**
     * 查询任务-结果计数
     */
    public DSJobCountResponse queryJobCount(Long jobId, String startTime, String endTime) throws ApiException {
        DSJobCountRequest request = new DSJobCountRequest();
        request.setJobId(jobId);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setToken(DataStoryTokenUtil.getToken());
        return jobClient.doExecute(request);
    }

    /**
     * 查询任务-结果明细
     */
    public DSJobSearchResponse queryJobSearch(Long jobId, String startTime, String endTime, String scrollId) throws ApiException {
        DSJobSearchRequest request = new DSJobSearchRequest();
        request.setJobId(jobId);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        request.setScrollId(scrollId);
        request.setToken(DataStoryTokenUtil.getToken());
        return jobClient.doExecute(request);
    }

    /**
     * 总声量统计接口
     *
     * @return 总声量
     */
    public DataStoryTotalVolumeResponse getTotalVolume(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStoryTotalVolumeRequest(request));
    }

    /**
     * 总互动量统计接口
     *
     * @return 总互动
     */
    @SuppressWarnings("unused")
    public DataStoryTotalInteractionResponse getTotalInteraction(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStoryTotalInteractionRequest(request));
    }

    /**
     * 总情感度统计接口
     *
     * @return 总情感
     */
    @SuppressWarnings("unused")
    public DataStoryTotalSentimentsResponse getTotalSentiments(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStoryTotalSentimentsRequest(request));
    }

    /**
     * 声量趋势统计接口
     *
     * @return 声量趋势
     */
    public DataStoryVolumeTreadResponse getVolumeTread(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStoryVolumeTreadRequest(request));
    }

    /**
     * 互动量趋势统计接口
     *
     * @return 互动趋势
     */
    public DataStoryInteractionTreadResponse getInteractionTread(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStoryInteractionTreadRequest(request));
    }

    /**
     * 情感类型统计接口
     *
     * @return 情感
     */
    public DataStorySentimentDistributeResponse getSentimentDistribute(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStorySentimentDistributeRequest(request));
    }

    /**
     * 关键词云统计接口
     *
     * @return 词云
     */
    public DataStoryCloudWordResponse getCloudWord(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStoryCloudWordRequest(request));
    }

    /**
     * 原文内容统计接口
     *
     * @return 原文
     */
    public DataStoryTextResponse getText(DataStoryEntity request) throws ApiException {
        return client.doExecute(new DataStoryTextRequest(request));
    }

    /**
     * 登录token
     *
     * @return 登录token
     */
    public String getLoginToken() {
        try {
            return dataStoryUtils.getToken().getToken();
        } catch (Exception e) {
            log.error("获取登录token失败，e=", e);
        }
        return null;
    }

    /**
     * 刷新token
     */
    public boolean refreshLoginToken() {
        try {
            return null != dataStoryUtils.refreshLogin();
        } catch (ApiException e) {
            log.error("获取登录token失败，e=", e);
        }
        return false;
    }
}
