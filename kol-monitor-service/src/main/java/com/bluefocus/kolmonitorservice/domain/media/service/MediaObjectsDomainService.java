package com.bluefocus.kolmonitorservice.domain.media.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorinterface.dto.media.req.MediaObjectsReq;
import com.bluefocus.kolmonitorservice.base.enums.MediaEditStatusEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaObjectsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【media_objects(媒体任务表)】的数据库操作Service实现
 * @createDate 2024-05-21 17:51:36
 */
@Service
@RequiredArgsConstructor
public class MediaObjectsDomainService extends ServiceImpl<MediaObjectsMapper, MediaObjects> {

    public void saveObjBatch(List<MediaObjectsReq> objList, Long taskId, String img) {
        AtomicInteger i = new AtomicInteger(1);

        objList.forEach(obj -> {
            MediaObjects mediaObjects = new MediaObjects();
            mediaObjects.setCreateTime(LocalDateTime.now());
            mediaObjects.setFilterword(JSON.toJSONString(obj.getFilterword()));
            mediaObjects.setKeyword(mergeKeyword(obj.getOrKeywords(), obj.getAndKeywords()));
            mediaObjects.setAndKeywords(JSON.toJSONString(obj.getAndKeywords()));
            mediaObjects.setOrKeywords(JSON.toJSONString(obj.getOrKeywords()));
            mediaObjects.setMediaTaskId(taskId);
            mediaObjects.setName("分析对象" + i.getAndIncrement());
            mediaObjects.setStartTime(obj.getStartTime());
            mediaObjects.setEndTime(obj.getEndTime());
            mediaObjects.setSources(obj.getSourceCodes());
            mediaObjects.setStatus(MediaEditStatusEnum.CRAWLER.getCode());
            mediaObjects.setImg(img);
            this.save(mediaObjects);
        });
    }

    public String mergeKeyword(List<String> orKeywords, List<List<String>> andKeywords) {
        ArrayList<String> list = new ArrayList<>();

        if (null != andKeywords && andKeywords.size() > 0) {
            for (List<String> andKeyword : andKeywords) {
                StringBuilder stringBuilder = new StringBuilder();
                List<String> andList = andKeyword.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                for (int i = 0; i < andList.size(); i++) {
                    String key = andList.get(i);
                    if (StringUtils.isNotBlank(key)) {
                        stringBuilder.append(key);
                        if (i < andList.size() - 1) {
                            stringBuilder.append("+");
                        }
                    }
                }
                list.add(stringBuilder.toString());
            }

        }
        if (null != orKeywords && orKeywords.size() > 0) {
            list.addAll(orKeywords.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList()));
        }
        return JSON.toJSONString(list);
    }

    public List<MediaObjects> findByTaskId(Long taskId) {
        return this.list(new LambdaQueryWrapper<MediaObjects>().eq(MediaObjects::getMediaTaskId, taskId));
    }

    public void updateMediaObjImg(Long mediaObjectsId, String imgUrl) {
        if (null == mediaObjectsId || null == imgUrl) {
            return;
        }
        this.lambdaUpdate().set(MediaObjects::getImg, imgUrl).eq(MediaObjects::getId, mediaObjectsId).update();
    }

    public void editObj(MediaObjects newObj) {

        this.getBaseMapper().update(null, Wrappers.<MediaObjects>lambdaUpdate()
                .set(MediaObjects::getRateInteraction, newObj.getRateInteraction())
                .set(MediaObjects::getRateSentiments, newObj.getRateSentiments())
                .set(MediaObjects::getRateVolume, newObj.getRateVolume())
                .set(MediaObjects::getTotalInteraction, newObj.getTotalInteraction())
                .set(MediaObjects::getTotalVolume, newObj.getRateVolume())
                .set(MediaObjects::getTotalSentiments, newObj.getTotalSentiments())
                .set(MediaObjects::getFinishTime, newObj.getFinishTime())
                .set(MediaObjects::getMediaTaskId, newObj.getMediaTaskId())
                .set(MediaObjects::getName, newObj.getName())
                .set(MediaObjects::getCreateTime, newObj.getCreateTime())
                .set(MediaObjects::getImg, newObj.getImg())
                .set(MediaObjects::getFilterword, newObj.getFilterword())
                .set(MediaObjects::getKeyword, newObj.getKeyword())
                .set(MediaObjects::getAndKeywords, newObj.getAndKeywords())
                .set(MediaObjects::getOrKeywords, newObj.getOrKeywords())
                .set(MediaObjects::getSources, newObj.getSources())
                .set(MediaObjects::getStartTime, newObj.getStartTime())
                .set(MediaObjects::getEndTime, newObj.getEndTime())
                .set(MediaObjects::getStatus, MediaEditStatusEnum.EDIT_CRAWLER.getCode())
                .eq(MediaObjects::getId, newObj.getId()));
    }

    public List<MediaObjects> findByNotNullAndKeywords() {
        LambdaQueryWrapper<MediaObjects> ne = new LambdaQueryWrapper<MediaObjects>().isNotNull(MediaObjects::getAndKeywords).ne(MediaObjects::getAndKeywords, "[]");
        return this.getBaseMapper().selectList(ne);
    }
}




