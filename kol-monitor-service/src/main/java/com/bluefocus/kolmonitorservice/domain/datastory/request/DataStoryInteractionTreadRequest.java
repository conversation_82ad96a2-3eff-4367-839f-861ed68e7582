package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryInteractionTreadResponse;
import org.springframework.beans.factory.annotation.Value;

/**
 * 数说-互动量趋势统计接口
 */
public class DataStoryInteractionTreadRequest extends DefaultDataStoryRequest<DataStoryInteractionTreadResponse> {
    @Value("${dataStory.api.interactionTread}")
    private String apiMethodName;

    public DataStoryInteractionTreadRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/interactionTread";
    }

    @Override
    public Class<DataStoryInteractionTreadResponse> getResponseClass() {
        return DataStoryInteractionTreadResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSource(), "source");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
