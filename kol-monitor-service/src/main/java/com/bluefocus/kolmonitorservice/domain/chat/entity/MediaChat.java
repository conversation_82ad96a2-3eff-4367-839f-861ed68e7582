package com.bluefocus.kolmonitorservice.domain.chat.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 社媒对话表
 * <AUTHOR>
 * @TableName media_chat
 */
@TableName(value ="media_chat")
@Data
public class MediaChat implements Serializable {
    /**
     * 对话id
     */
    @TableId(value = "chat_id")
    private Long chatId;

    /**
     * 会话id
     */
    @TableField(value = "conversation_id")
    private Long conversationId;

    /**
     * botId
     */
    @TableField(value = "bot_id")
    private String botId;

    /**
     * 消息状态 0未完成 1成功 2失败 3失效(停止)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 完成时间
     */
    @TableField(value = "finish_time")
    private LocalDateTime finishTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}