package com.bluefocus.kolmonitorservice.domain.chat.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 社媒会话表
 * @TableName media_conversation
 */
@TableName(value ="media_conversation")
@Data
public class MediaConversation implements Serializable {
    /**
     * 会话id
     */
    @TableId
    private Long conversationId;

    /**
     * 社媒对象id
     */
    private Long mediaObjId;

    /**
     * 会话状态 1正常 3失效
     */
    private Integer status;

    /**
     * 会话创建人
     */
    private Long creatUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 完成时间
     */
    private LocalDateTime updataTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}