package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * GPT生成图
 *
 * @TableName gpt_picture
 */
@TableName(value = "gpt_picture")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GptPicture implements Serializable {


    public GptPicture(Long mediaObjectsId, Integer imgSource, String img, LocalDateTime createTime) {
        this.mediaObjectsId = mediaObjectsId;
        this.imgSource = imgSource;
        this.img = img;
        this.createTime = createTime;
    }

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 媒体对象ID
     */
    private Long mediaObjectsId;

    /**
     * 用户描述
     */
    private String userDesc;

    /**
     * prompt
     */
    private String prompt;

    /**
     * 轮廓图
     */
    private String img;

    /**
     * 图片来源 1用户上传 2GPT生成 3WordArt生成 4WordCloud生成
     */
    private Integer imgSource;

    /**
     * 宽
     */
    private Integer width;


    /**
     * 高
     */
    private Integer height;


    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}