package com.bluefocus.kolmonitorservice.domain.media.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bluefocus.kolmonitorinterface.dto.media.ImgValue;
import com.bluefocus.kolmonitorservice.base.common.OSSUploadCommon;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.common.TimeOutException;
import com.bluefocus.kolmonitorservice.base.enums.MimeEnum;
import com.bluefocus.kolmonitorservice.base.util.ImageUtils;
import com.bluefocus.kolmonitorservice.base.util.PictureUtils;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.UserCommon;
import com.bluefocus.kolmonitorservice.domain.gptalk.GPTalkDomainService;
import com.bluefocus.kolmonitorservice.domain.gptalk.request.GPTalkImageRequest;
import com.bluefocus.kolmonitorservice.domain.gptalk.response.GPTalkImageResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.GptPicture;
import com.bluefocus.kolmonitorservice.domain.media.mapper.GptPictureMapper;
import com.bluefocus.kolmonitorservice.domain.wordart.WordArtDomainService;
import com.bluefocus.kolmonitorservice.domain.wordart.entity.WordArtWordEntity;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordArtGenerateRequest;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordCloudGenRequest;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordArtGenerateResponse;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordCloudResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【gpt_picture(GPT生成图)】的数据库操作Service实现
 * @createDate 2024-05-21 17:51:36
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GptPictureDomainService {

    private final GptPictureMapper gptPictureMapper;
    private final OSSUploadCommon ossUploadCommon;
    private final GPTalkDomainService gpTalkDomainService;
    private final ThreadPoolExecutor dalle3ThreadPoolExecutor;
    private final RedisUtils redisUtils;
    private final WordArtDomainService wordArtDomainService;
    private final UserCommon userCommon;
    private final Map<String, String> delle3GptMap;
    private final List<String> delle3GptList;

    @Value("${aliyun.media.picture-path}")
    private String picturePath;

    private final ConcurrentHashMap<String, String> defaultImgMap = new ConcurrentHashMap<>();

    private String getFilePath(Long id, String name) {
        return picturePath.replace("{userId}", String.valueOf(id)) + name;
    }

    public void saveUploadPicture(Long mediaObjectsId, String fileUrl) {
        savePicture(new GptPicture(mediaObjectsId, 1, fileUrl, LocalDateTime.now()));
    }

    public String uploadAndSave(MultipartFile file, String name, Long mediaObjectsId) {
        String path = getFilePath(userCommon.getUserId(true), name);
        String fileUrl = ossUploadCommon.compressAndUpload(file, path, name);
        this.saveUploadPicture(mediaObjectsId, fileUrl);
        return fileUrl;
    }

    /**
     * 调取gpt获取图片
     */
    public List<String> genGptPictureList(List<String> promptList, Long mediaObjectsId) {

        long count = getGptCount();
        String serverUrl = delle3GptList.get((int) (count % 3));
        String key = delle3GptMap.get(serverUrl);
        log.info("当前count={},使用gpt地址={}", count, serverUrl);

        List<CompletableFuture<String>> fileUrlList = promptList.stream().map(p -> CompletableFuture.supplyAsync(() -> {
            try {
                com.bluefocus.kolmonitorservice.domain.gptalk.request.GPTalkImageRequest request = new GPTalkImageRequest();
                request.setPrompt(p);
                GPTalkImageResponse image = gpTalkDomainService.imageUrl(request, serverUrl, key);
                String imageUrl = image.getData().get(0).getImage();
                InputStream imageStream = ImageUtils.getImageStream(imageUrl);
                byte[] bytes = ImageUtils.imageInputStreamToBytes(imageStream);
                bytes = PictureUtils.compressPicture(bytes, 0.9);
                String fileName = UUID.randomUUID() + StrUtil.DOT + MimeEnum.JPG.getExt();
                String url = ossUploadCommon.uploadImg2Oss(new ByteArrayInputStream(bytes), getFilePath(mediaObjectsId, fileName), fileName);

                savePictureDelle3(url, mediaObjectsId, p, imageUrl);
                log.info("delle3生图完毕：图片地址={}", url);
                return url;
            } catch (Exception e) {
                log.warn("生成delle3图片异常，地址[{}],e={}", serverUrl, e);
                return "";
            }
        }, dalle3ThreadPoolExecutor)).collect(Collectors.toList());
        CompletableFuture.allOf(fileUrlList.toArray(new CompletableFuture[0]));

        return fileUrlList.stream().map(c -> {
            try {
                return c.get();
            } catch (InterruptedException | ExecutionException e) {
                return null;
            }
        }).collect(Collectors.toList());
    }

    private long getGptCount() {
        RBucket<Object> count = redisUtils.getString(RedisKeyComm.DEFAULT_GPT_COUNT);
        if (!count.isExists()) {
            count.set(1);
        } else {
            long aLong = Long.parseLong(count.get().toString());
            count.set(aLong + 1);
        }
        return Long.parseLong(count.get().toString());
    }

    private void savePictureDelle3(String imgUrl, Long objId, String prompt, String oldUrl) {

        GptPicture gptPicture = new GptPicture();
        gptPicture.setCreateTime(LocalDateTime.now());
        gptPicture.setImg(imgUrl);
        gptPicture.setImgSource(2);
        gptPicture.setMediaObjectsId(objId);
        gptPicture.setUserDesc(oldUrl);
        gptPicture.setPrompt(prompt);
        savePicture(gptPicture);
    }

    public void savePicture(GptPicture gptPicture) {
        gptPictureMapper.insert(gptPicture);
    }

    public String getPromptConstant() {
        return redisUtils.getString(RedisKeyComm.PROMPT_CONSTANT).get().toString();
    }

    public ImgValue genWordArtImg(String img, Map<String, Long> collect, Long objId) {
        if (CollectionUtil.isEmpty(collect)) {
            log.warn("此对象的词云为null,对象={}", objId);
            return null;
        }
        WordArtGenerateRequest request = new WordArtGenerateRequest();
        try {
            request.setImageBase64(defaultImgBase64(img));
            Long maxCount = collect.values().stream().max(Comparator.comparingLong(c -> c)).orElse(null);
            Double multiple = 1.0d;
            if (maxCount != null && maxCount.compareTo(1000L) > 0) {
                multiple = 1000L / maxCount.doubleValue();
            }
            List<WordArtWordEntity> objects = new ArrayList<>();
            for (String key : collect.keySet()) {
                WordArtWordEntity wordArtWordEntity = new WordArtWordEntity();
                wordArtWordEntity.setName(key);
                Long count = Long.valueOf(collect.get(key).toString());
                Long size = (long) (count * multiple);
                if (size <= 0) {
                    size = 1L;
                }
                wordArtWordEntity.setSize(size);
                objects.add(wordArtWordEntity);
            }
            request.setWords(objects);
            return genWordArtImg(request, objId);
        } catch (Exception e) {
            log.warn("转换OSS图片流异常，objId={}，img={}", objId, img);
            return null;
        }
    }

    public ImgValue genWordArtImg(WordArtGenerateRequest request, Long objId) {

        WordArtGenerateResponse execute = null;
        try {
            long start = System.currentTimeMillis();
            log.info("开始生成wordart图片,去调用wordart,objId:{}, wordSize:{}, wordBytes:{}", objId, request.getWords().size(), request.getWords().toString().length());
            execute = wordArtDomainService.generate(request);
            log.info("生成wordart图片完成,时间{} objId:{}, wordSize:{}, wordBytes:{}", System.currentTimeMillis() - start, objId, request.getWords().size(), request.getWords().toString().length());
        } catch (Exception e) {
            log.error("生成wordart图片异常,objId:{},e={}", objId, e);
            if (e instanceof TimeOutException) {
                throw new TimeOutException("wordart超时异常");
            }
            return null;
        }
        return upload2Oss(objId, execute.getBody());
    }

    public ImgValue genWordCloudImg(WordCloudGenRequest request, Long handleId, Long objId) {
        ImgValue imgValue = null;
        WordCloudResponse execute;
        int count = 3;
        try {
            long start = System.currentTimeMillis();
            log.info("开始生成 wordCloud 图片,去调用wordCloud,handleId:{}, wordSize:{}, wordBytes:{}", handleId, request.getUrl(), request.getData().size());
            execute = wordArtDomainService.executeWordCloud(request);

            if ("200".equals(execute.getCode())) {
                while (imgValue == null || count <= 0) {
                    imgValue = upload2Oss(objId, execute.getData());
                    count--;
                }
                log.info("生成 wordCloud 图片完成,时间{},handleId:{}, 请求地址:{}, wordBytes:{}", System.currentTimeMillis() - start, handleId, request.getUrl(), request.getData().size());
            } else {
                log.info("生成 wordCloud 图片异常code={},,handleId:{}, 请求地址:{}, 内容{}", execute.getCode(), handleId, request.getUrl(), request.getData());
                return null;
            }

        } catch (Exception e) {
            log.error("生成 wordCloud 图片异常,handleId:{},e={}", handleId, e);
            return null;
        }
        return imgValue;
    }

    public ImgValue upload2Oss(Long objId, String base64) {
        try {
            byte[] bytes = ImageUtils.base64Decode2Bytes(base64);
            BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            String fileName = UUID.randomUUID() + StrUtil.DOT + MimeEnum.PNG.getExt();
            String url = ossUploadCommon.uploadImg2Oss(inputStream, getFilePath(objId, fileName), fileName);
            return ImgValue.builder()
                    .url(url)
                    .height(image.getHeight())
                    .width(image.getWidth()).build();
        } catch (IOException ioException) {
            log.error("生成wordart图片上传OSS异常:{}, 返回值为：{}", ioException, base64);
            return null;
        }
    }

    public WordArtGenerateResponse genWordArtImgTime(WordArtGenerateRequest request, Long handleId) {
        WordArtGenerateResponse execute;
        try {
            long start = System.currentTimeMillis();
            log.info("用户开始生成wordart图片,handleId:{}", handleId);
            execute = wordArtDomainService.generateTime(request);
            log.info("用户生成wordart图片完成,时间{} handleId:{}", System.currentTimeMillis() - start, handleId);
        } catch (Exception e) {
            log.error("生成wordart图片异常,handleId:{},e={}", handleId, e);
            if (e instanceof TimeOutException) {
                throw new TimeOutException("wordart超时异常");
            }
            return null;
        }
        return execute;
    }

    public void savePictureBatch(ArrayList<GptPicture> gptPictures) {
        gptPictures.forEach(this::savePicture);
    }

    public List<GptPicture> findByObjIds(List<Long> objIds) {

        return gptPictureMapper.selectList(new LambdaQueryWrapper<GptPicture>().in(GptPicture::getMediaObjectsId, objIds));
    }

    public GptPicture genWordArtImgAndSave(String img, Map<String, Long> wordMap, Long objId) {
        ImgValue imgUrl;
        try {
            imgUrl = this.genWordArtImg(img, wordMap, objId);
        } catch (Exception e) {
            return null;
        }
        if (ObjectUtil.isNull(imgUrl)) {
            return null;
        }
        GptPicture gptPicture = new GptPicture();
        gptPicture.setCreateTime(LocalDateTime.now());
        gptPicture.setImg(imgUrl.getUrl());
        gptPicture.setHeight(imgUrl.getHeight());
        gptPicture.setWidth(imgUrl.getWidth());
        gptPicture.setImgSource(3);
        gptPicture.setMediaObjectsId(objId);
        this.savePicture(gptPicture);
        return gptPicture;
    }

    public GptPicture genWordCloudImgAndSave(WordCloudGenRequest request, Long handleId, Long objId) {
        ImgValue imgUrl;
        try {
            imgUrl = this.genWordCloudImg(request, handleId, objId);
            if (ObjectUtil.isNull(imgUrl)) {
                return null;
            }
        } catch (Exception e) {
            return null;
        }
        GptPicture gptPicture = new GptPicture();
        gptPicture.setCreateTime(LocalDateTime.now());
        gptPicture.setImg(imgUrl.getUrl());
        gptPicture.setHeight(imgUrl.getHeight());
        gptPicture.setWidth(imgUrl.getWidth());
        gptPicture.setImgSource(4);
        gptPicture.setMediaObjectsId(objId);
        this.savePicture(gptPicture);
        return gptPicture;
    }

    public String defaultWordImg() {
        return redisUtils.getString(RedisKeyComm.DEFAULT_IMG).get().toString();
    }

    public String defaultImgBase64(String imgUrl) {
        if (imgUrl.equals(defaultWordImg())) {
            String base64 = defaultImgMap.get(imgUrl);
            if (null == base64) {
                base64 = imageBase64(getImageBytes(imgUrl));
                defaultImgMap.putIfAbsent(imgUrl, base64);
            }
            return base64;
        }
        return imageBase64(getImageBytes(imgUrl));
    }

    public String imageBase64(byte[] baos) {
        try {
            baos = PictureUtils.transAlpha(baos);
            baos = PictureUtils.compressPicture(baos, 0.9);
            return ImageUtils.bytesToBase64(baos);
        } catch (Exception e) {
            log.warn("处理图片异常");
            return null;
        }
    }

    public byte[] getImageBytes(String imgUrl) {
        byte[] baos = null;
        try {
            int max = 3;
            for (int i = 0; i < max; i++) {
                baos = ImageUtils.imageUrlToBytes(imgUrl);
                if (baos == null && i == max - 1) {
                    return null;
                }
            }
        } catch (Exception e) {
            log.warn("获取图片异常，img={}", imgUrl);
            return null;
        }
        return baos;
    }
}




