package com.bluefocus.kolmonitorservice.domain.algorithm.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryTextEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


@Data
@EqualsAndHashCode()
public class HotAnalysisRespEntity implements Serializable {

        /**
         * 分析结论
         */
        @J<PERSON>NField(name = "hotContent")
        private String hotContent;

        @J<PERSON>NField(name = "date")
        private String date;

        @JSONField(name = "keyword")
        private List<String> keywords;

        @JSONField(name = "hotNotes")
        private List<HotAnalysisRespNote> hotNotes;

        @JSONField(name = "message")
        private String message;

}
