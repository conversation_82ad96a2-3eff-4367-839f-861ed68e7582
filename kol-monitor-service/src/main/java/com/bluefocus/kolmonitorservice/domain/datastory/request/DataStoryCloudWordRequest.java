package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryCloudWordResponse;
import org.springframework.beans.factory.annotation.Value;

/**
 * 数说-词云接口
 */
public class DataStoryCloudWordRequest extends DefaultDataStoryRequest<DataStoryCloudWordResponse> {
    @Value("${dataStory.api.cloudWord}")
    private String apiMethodName;

    public DataStoryCloudWordRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/cloudWord";
    }

    @Override
    public Class<DataStoryCloudWordResponse> getResponseClass() {
        return DataStoryCloudWordResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSource(), "source");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
