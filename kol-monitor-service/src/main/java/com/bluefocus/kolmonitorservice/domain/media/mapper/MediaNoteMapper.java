package com.bluefocus.kolmonitorservice.domain.media.mapper;

import com.bluefocus.kolmonitorservice.domain.media.entity.MediaNote;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【media_note(媒体笔记表)】的数据库操作Mapper
* @createDate 2024-05-21 17:51:36
* @Entity com.bluefocus.kolmonitorservice.domain.media.entity.MediaNote
*/
public interface MediaNoteMapper extends BaseMapper<MediaNote> {

    @Insert("<script>" +
            "INSERT INTO media_note " +
            "( media_objects_id,handle_id,source_code,\n" +
            "head_img,author,\n" +
            "title,content,note_url,\n" +
            "publish_time,interaction_cnt,like_cnt,\n" +
            "collection_cnt,reposts_cnt,review_cnt,\n" +
            "is_original,search_keyword,cover_ocr_content,\n" +
            "audio_ocr_content,highlight_ocr_content,video_content,\n" +
            "create_time,update_time ) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','>" +
            "(#{item.mediaObjectsId},#{item.handleId}, #{item.sourceCode}, " +
            "#{item.headImg}, #{item.author}, " +
            "#{item.title}, #{item.content}, #{item.noteUrl}, " +
            "#{item.publishTime}, #{item.interactionCnt}, #{item.likeCnt}, " +
            "#{item.collectionCnt}, #{item.repostsCnt}, #{item.reviewCnt}, " +
            "#{item.isOriginal}, #{item.searchKeyword}, #{item.coverOcrContent}, " +
            "#{item.audioOcrContent}, #{item.highlightOcrContent}, #{item.videoContent}, " +
            "#{item.createTime}, #{item.updateTime})" +
            "</foreach>" +
            "</script>")
    int definedInsertBatch(@Param("list") List<MediaNote> mediaNoteList);
}




