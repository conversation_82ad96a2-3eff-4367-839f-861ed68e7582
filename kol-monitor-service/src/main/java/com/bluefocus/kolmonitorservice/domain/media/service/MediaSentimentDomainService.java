package com.bluefocus.kolmonitorservice.domain.media.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaSentiment;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaSentimentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author: yjLiu
 * @date: 0001 2024/7/1 18:53
 * @description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaSentimentDomainService  extends ServiceImpl<MediaSentimentMapper, MediaSentiment> {

    public MediaSentiment findByHandleId(Long handleId) {
        return this.lambdaQuery().eq(MediaSentiment::getHandleId, handleId).one();
    }
}
