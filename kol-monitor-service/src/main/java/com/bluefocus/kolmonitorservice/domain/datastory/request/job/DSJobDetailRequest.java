package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.http.ContentType;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSJobDetailResponse;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 数说-查询任务配置
 */
@Setter
public class DSJobDetailRequest extends JobDataStoryRequest<DSJobDetailResponse> {

    @Value("${dataStory.task.jobDetail}")
    private String apiMethodName;

    private Long jobId;

    @Override
    public String getParams() {
        return null;
    }

    @Override
    public String getApiMethodName() {
        TreeMap<String, Object> treeMap = new TreeMap<>();
        treeMap.put("id", this.jobId);
        return (apiMethodName != null ? apiMethodName : "project/job/detail") + "?" + UrlQuery.of(treeMap);
    }

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
        }
        this.headerMap.put("Content-Type", ContentType.JSON.getValue());
        return this.headerMap;
    }


    @Override
    public Class<DSJobDetailResponse> getResponseClass() {
        return DSJobDetailResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.jobId, "jobId");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
