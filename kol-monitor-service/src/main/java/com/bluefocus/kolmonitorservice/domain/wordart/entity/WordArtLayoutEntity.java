package com.bluefocus.kolmonitorservice.domain.wordart.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@EqualsAndHashCode
public class WordArtLayoutEntity implements Serializable {

    private Integer wordsAmount = 0;
    private Double density = 0.25;
    private List<Integer> angles = Collections.singletonList(0);

}
