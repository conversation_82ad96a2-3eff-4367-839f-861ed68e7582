package com.bluefocus.kolmonitorservice.domain.algorithm.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: yjLiu
 * @date: 0005 2024/6/5 19:58
 * @description:
 */
@Data
@EqualsAndHashCode()
public class HotAnalysisRespNote {
    /**
     * 笔记标题
     */
    @JSONField(name = "title")
    private String title;

    /**
     * 笔记链接
     */
    @JSONField(name = "url")
    private String url;
}
