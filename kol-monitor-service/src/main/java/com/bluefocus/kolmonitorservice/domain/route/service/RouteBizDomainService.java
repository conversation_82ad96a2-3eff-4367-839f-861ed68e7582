package com.bluefocus.kolmonitorservice.domain.route.service;

import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteBiz;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteBizToken;
import com.bluefocus.kolmonitorservice.domain.route.mapper.RouteBizMapper;
import com.bluefocus.kolmonitorservice.domain.route.mapper.RouteBizTokenMapper;
import com.bluefocus.kolmonitorservice.domain.route.mapper.RouteThirdPlatMapper;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RBucket;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 15:55
 * @description:
 */
@Service
@RequiredArgsConstructor
public class RouteBizDomainService {

    private final RouteBizTokenMapper routeBizTokenMapper;
    private final RouteBizMapper routeBizMapper;
    private final RouteThirdPlatMapper routeThirdPlatMapper;
    private final RedisUtils redisUtils;
    private final static int TIME = 72;
    private final static String TOKEN_PRE = "ROUTE:";
    private final static double Q = 0.7d;

    public boolean checkToken(String token) {
        RouteBizToken routeBizToken = routeBizTokenMapper.selectOne(new LambdaQueryWrapper<RouteBizToken>()
                .eq(RouteBizToken::getToken, token));
        if (null == routeBizToken) {
            return false;
        }
        return routeBizToken.getExpireTime().compareTo(LocalDateTime.now()) > 0;
    }

    public RouteBizToken getToken(String bizId) {
        RouteBizToken routeBizToken = routeBizTokenMapper.selectOne(new LambdaQueryWrapper<RouteBizToken>().eq(RouteBizToken::getBizId, bizId)
                .ge(RouteBizToken::getExpireTime, LocalDateTime.now())
                .orderByDesc(RouteBizToken::getCreateTime).last("limit 1"));
        if (routeBizToken == null) {
            return creatNewToken(bizId);
        }

        LocalDateTime createTime = routeBizToken.getCreateTime();
        if (createTime.plusHours(Double.valueOf(TIME * Q).longValue()).compareTo(LocalDateTime.now()) < 0) {
            return creatNewToken(bizId);
        }

        return routeBizToken;
    }

    private RouteBizToken creatNewToken(String bizId) {
        LocalDateTime timeNow = LocalDateTime.now();
        String token = generateToken(bizId, timeNow);
        RBucket<Object> tokenString = getRouteToken(token);
        tokenString.set(bizId, TIME, TimeUnit.HOURS);
        RouteBizToken newPo = new RouteBizToken();
        newPo.setToken(token);
        newPo.setBizId(bizId);
        newPo.setCreateTime(timeNow);
        newPo.setExpireTime(timeNow.plusHours(TIME));
        routeBizTokenMapper.insert(newPo);
        return newPo;
    }

    private RBucket<Object> getRouteToken(String token) {
        return redisUtils.getString(TOKEN_PRE + token);
    }

    public RouteBiz getBiz(String bizId) {
        return routeBizMapper.selectOne(new LambdaQueryWrapper<RouteBiz>().eq(RouteBiz::getBizId, bizId));
    }

    public String generateToken(String bizId, LocalDateTime timeNow) {
        String bizIdWithTimestamp = bizId + Times.toEpochMilli(timeNow);

        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(bizIdWithTimestamp.getBytes());
            return Base64.getEncoder().encodeToString(hash);
        } catch (NoSuchAlgorithmException e) {
            return UUID.randomUUID().toString();
        }
    }

    public RouteBizToken getBizByToken(String token) {
        return routeBizTokenMapper.selectOne(new LambdaQueryWrapper<RouteBizToken>().eq(RouteBizToken::getToken, token));
    }
}
