package com.bluefocus.kolmonitorservice.domain.wordart.request;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordCloudResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Setter
public class WordCloudGenRequest implements Request<WordCloudResponse> {

    @Getter
    @JSONField(name = "url")
    private String url;

    @Getter
    @JSONField(name = "data")
    private Map<String, Long> data;

    @Override
    public String getApiMethodName() {
        return "generate";
    }

    @Override
    public String getParams() {
        JSONObject params = new JSONObject();
        params.put("url", url);
        params.put("data", data);
        return params.toJSONString();
    }

    @Override
    public String getTimestamp() {
        return null;
    }

    @Override
    public Class<WordCloudResponse> getResponseClass() {
        return WordCloudResponse.class;
    }

    @Override
    public Map<String, String> getHeaderMap() {
        return null;
    }


    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.url, "url");
        RequestCheckUtils.checkNotEmpty(this.data, "data");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }
}
