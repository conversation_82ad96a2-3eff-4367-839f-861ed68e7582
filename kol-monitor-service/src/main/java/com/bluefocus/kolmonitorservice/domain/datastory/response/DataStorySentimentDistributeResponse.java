package com.bluefocus.kolmonitorservice.domain.datastory.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashMap;

/**
 * 数说-情感类型统计返回
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DataStorySentimentDistributeResponse extends Response {

    private DataStorySentimentDistributeResponse.ResultDTO data;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JSONField(name = "total")
        private HashMap<Long, String> total;
    }
}
