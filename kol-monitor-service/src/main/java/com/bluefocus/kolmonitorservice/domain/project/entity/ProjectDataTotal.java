package com.bluefocus.kolmonitorservice.domain.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 项目数据汇总
 *
 * <AUTHOR>
 * @TableName project_data_total
 */
@TableName(value = "project_data_total")
@Data
public class ProjectDataTotal implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "project_id")
    private Long projectId;

    /**
     * 共计达人数量
     */
    @TableField(value = "total_kol_num")
    private Integer totalKolNum;

    /**
     * 共计地址数量
     */
    @TableField(value = "total_url_num")
    private Integer totalUrlNum;

    /**
     * 共计平台数量
     */
    @TableField(value = "total_plant_num")
    private Integer totalPlantNum;

    /**
     * 共计阅读数量
     */
    @TableField(value = "total_read_num")
    private Long totalReadNum;

    /**
     * 共计点赞数量
     */
    @TableField(value = "total_like_num")
    private Long totalLikeNum;

    /**
     * 共计评论数量
     */
    @TableField(value = "total_comment_num")
    private Long totalCommentNum;

    /**
     * 共计收藏数量
     */
    @TableField(value = "total_collection_num")
    private Long totalCollectionNum;

    /**
     * 共计互动数量
     */
    @TableField(value = "total_interaction_num")
    private Long totalInteractionNum;

    /**
     * 共计互动率
     */
    @TableField(value = "total_interaction_rate")
    private BigDecimal totalInteractionRate;

    /**
     * 共计关注数量
     */
    @TableField(value = "total_follow_num")
    private Long totalFollowNum;

    /**
     * 共计曝光数量
     */
    @TableField(value = "total_exposure")
    private Long totalExposure;

    /**
     * 共计转发数量
     */
    @TableField(value = "total_share_num")
    private Long totalShareNum;

    /**
     * 共计弹幕数量
     */
    @TableField(value = "total_bullet_num")
    private Long totalBulletNum;

    /**
     * 共计硬币数量
     */
    @TableField(value = "total_coin_num")
    private Long totalCoinNum;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 消费时间
     */
    @TableField(value = "consume_time")
    private Long consumeTime;

    /**
     * 平均阅读
     */
    @TableField(value = "avg_read_num")
    private Long avgReadNum;

    /**
     * 平均曝光
     */
    @TableField(value = "avg_exposure_num")
    private Long avgExposureNum;

    /**
     * 平均点赞
     */
    @TableField(value = "avg_like_num")
    private Long avgLikeNum;

    /**
     * 平均评论
     */
    @TableField(value = "avg_comment_num")
    private Long avgCommentNum;

    /**
     * 平均互动数
     */
    @TableField(value = "avg_interaction_num")
    private Long avgInteractionNum;

    /**
     * 平均收藏
     */
    @TableField(value = "avg_collection_num")
    private Long avgCollectionNum;

    /**
     * 平均转发
     */
    @TableField(value = "avg_share_num")
    private Long avgShareNum;

    /**
     * 平均关注
     */
    @TableField(value = "avg_follow_num")
    private Long avgFollowNum;

    /**
     * 平均弹幕
     */
    @TableField(value = "avg_bullet_num")
    private Long avgBulletNum;

    /**
     * 平均投币
     */
    @TableField(value = "avg_coin_num")
    private Long avgCoinNum;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}