package com.bluefocus.kolmonitorservice.domain.project.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;

/**
* @Descirption 项目管理表
* <AUTHOR>
* @date 2023/3/7 17:37
*/
@Data
public class Project {
    /**
    * 主键
    */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
    * 项目名称
    */
    private String name;

    /**
    * 项目状态 0:待上传 1:待监测 2:监测中 3:监测完成
    */
    private Integer status;

    /**
    * 监测平台
    */
    @TableField(value = "monitor_plat")
    private String monitorPlat;

    /**
     * 监测周期时间
     */
    @TableField(value = "period_time")
    private Integer periodTime;

    /**
    * 监测周期
    */
    @TableField(value = "monitor_period")
    private String monitorPeriod;

    /**
    * 删除状态 0:未删除 1:已删除
    */
    private Integer deleteStatus;

    /**
    * 操作人
    */
    private Long operatorId;

    /**
    * 创建时间
    */
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    private LocalDateTime updateTime;
}