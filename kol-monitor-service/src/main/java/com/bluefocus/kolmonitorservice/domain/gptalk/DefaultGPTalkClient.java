package com.bluefocus.kolmonitorservice.domain.gptalk;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * GPTalk-构造器
 * <AUTHOR>
 */
@Slf4j
@Getter
@Service
public class DefaultGPTalkClient implements Client {


    @Value("${azure.serverUrl}")
    protected String serverUrl;

    @Value("${azure.api-key}")
    protected String apiKey;

    /**
     * 默认连接超时时间为15秒
     */
    protected int connectTimeout = 15000;

    /**
     * 默认响应超时时间为30秒
     */
    protected int readTimeout = 30000;

    /**
     * 是否在客户端校验请求
     */
    @Setter
    protected boolean needCheckRequest = true;

    public DefaultGPTalkClient() {

    }

    public DefaultGPTalkClient(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public DefaultGPTalkClient(String serverUrl, String apiKey) {
        this.serverUrl = serverUrl;
        this.apiKey = apiKey;
    }

    public DefaultGPTalkClient(String serverUrl, int connectTimeout, int readTimeout) {
        this(serverUrl);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public DefaultGPTalkClient(String serverUrl, String apiKey, int connectTimeout, int readTimeout) {
        this(serverUrl, apiKey);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public <T extends Response> T execute(Request<T> request) throws ApiException {
//        T tRsp = ClientCommon.check(this.needCheckRequest, request);
//        if (tRsp != null) {
//            return tRsp;
//        }
//        String query = request.getParams();
//        Map<String, String> headerMap = request.getHeaderMap();
//        headerMap.put("api-key", this.APIKEY);
//
//        try {
//            HttpResponse httpResponse = HttpUtil.createPost(this.serverUrl + request.getApiMethodName())
//                    .setConnectionTimeout(this.connectTimeout)
//                    .setReadTimeout(this.readTimeout).headerMap(headerMap, true).body(query).execute();
//            String body = httpResponse.body();
//            if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
//                tRsp = JSON.parseObject(body, request.getResponseClass());
//            } else {
//                tRsp = request.getResponseClass().newInstance();
//            }
//            tRsp.setBody(body);
//            tRsp.setSuccess(httpResponse.isOk());
//            tRsp.setRequestUrl(this.serverUrl + request.getApiMethodName());
//            tRsp.setHeaderContent(httpResponse.headers());
//        } catch (Exception e) {
//            try {
//                tRsp = request.getResponseClass().newInstance();
//            } catch (Exception xe) {
//                throw new ApiException(xe);
//            }
//            tRsp.setCode("-1");
//            tRsp.setMsg(e.getMessage());
//            return tRsp;
//        }
//        return tRsp;
        return null;
    }

    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {
        return null;
    }

    public <T extends Response> T execute(Request<T> request, String serverUrl, String key) throws ApiException {
        T tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }
        String query = request.getParams();
        Map<String, String> headerMap = request.getHeaderMap();
        headerMap.put("api-key", key);

        try {
            HttpResponse httpResponse = HttpUtil.createPost(serverUrl + request.getApiMethodName())
                    .setConnectionTimeout(this.connectTimeout)
                    .setReadTimeout(this.readTimeout).headerMap(headerMap, true).body(query).execute();
            String body = httpResponse.body();
            if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
                tRsp = JSON.parseObject(body, request.getResponseClass());
            } else {
                tRsp = request.getResponseClass().newInstance();
            }
            tRsp.setBody(body);
            tRsp.setSuccess(httpResponse.isOk());
            tRsp.setRequestUrl(this.serverUrl + request.getApiMethodName());
            tRsp.setHeaderContent(httpResponse.headers());
            log.info("请求delle3完成,结果={}", body);
        } catch (Exception e) {
            log.error("请求delle3异常,e={}", e);
            try {
                tRsp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            tRsp.setCode("-1");
            tRsp.setMsg(e.getMessage());
            return tRsp;
        }
        return tRsp;
    }
}
