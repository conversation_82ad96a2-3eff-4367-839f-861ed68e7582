package com.bluefocus.kolmonitorservice.domain.chat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 社媒对话内容表
 * @TableName media_chat_text
 */
@TableName(value ="media_chat_text")
@Data
public class MediaChatText implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 对话id
     */
    private Long chatId;

    /**
     * 内容
     */
    private String content;

    /**
     * 角色类型:user, system, assistant
     */
    private String role;

    /**
     * 消息类型:question, answer
     */
    private String type;

    /**
     * 消息发送人
     */
    private Long sendUserId;

    /**
     * 消息接受人bot
     */
    private Long receiveId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    private boolean status = false;
}