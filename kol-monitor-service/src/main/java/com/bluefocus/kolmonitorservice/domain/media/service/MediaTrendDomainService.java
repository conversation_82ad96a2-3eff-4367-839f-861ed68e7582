package com.bluefocus.kolmonitorservice.domain.media.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTrend;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaTrendMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【media_trend(媒体趋势表)】的数据库操作Service实现
 * @createDate 2024-05-21 17:51:36
 */
@Service
@RequiredArgsConstructor
public class MediaTrendDomainService extends ServiceImpl<MediaTrendMapper, MediaTrend> {

    public List<MediaTrend> findByHandleIds(List<Long> handleIdList) {
        return list(new LambdaQueryWrapper<MediaTrend>().in(MediaTrend::getHandleId, handleIdList));
    }

    public List<MediaTrend> findByHandleId(Long handleId) {

        return list(new LambdaQueryWrapper<MediaTrend>().eq(MediaTrend::getHandleId, handleId));
    }

    public MediaTrend findByHandleIdAndSource(Long handleId, Integer code) {
        return getOne(new LambdaQueryWrapper<MediaTrend>().eq(MediaTrend::getHandleId, handleId).eq(MediaTrend::getSourceCode, code));
    }

    public void deleteByMediaObjects(Long objId) {
        if (null != objId) {
            this.lambdaUpdate().eq(MediaTrend::getMediaObjectsId, objId).remove();
        }
    }

    public void saveAll(List<MediaTaskHandle> mediaTaskHandleList) {
        for (MediaTaskHandle mediaTaskHandle : mediaTaskHandleList) {
            ArrayList<MediaTrend> trendArrayList = new ArrayList<>();
            Arrays.stream(mediaTaskHandle.getSources().split(","))
                    .collect(Collectors.toSet())
                    .forEach(code -> {
                        MediaTrend mediaTrend = new MediaTrend();
                        mediaTrend.setMediaObjectsId(mediaTaskHandle.getMediaObjectsId());
                        mediaTrend.setHandleId(mediaTaskHandle.getId());
                        mediaTrend.setCreateTime(LocalDateTime.now());
                        mediaTrend.setSourceCode(Integer.valueOf(code));
                        mediaTrend.setIsAlgorithmSuc(FinishStatusEnum.CRAWLER.getCode());
                        trendArrayList.add(mediaTrend);
                    });
            this.saveBatch(trendArrayList);
        }
    }
}




