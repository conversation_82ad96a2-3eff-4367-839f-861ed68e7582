package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 媒体趋势表
 * @TableName media_trend
 */
@TableName(value ="media_trend")
@Data
public class MediaTrend implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 媒体对象ID
     */
    private Long mediaObjectsId;

    /**
     * 处理id
     */
    private Long handleId;

    /**
     * 数据源code 0全部 1小红书 2抖音 3微博
     */
    private Integer sourceCode;

    /**
     * 声量趋势day
     */
    private String volumeTrendDay;

    /**
     * 声量趋势week
     */
    private String volumeTrendWeek;

    /**
     * 声量趋势month
     */
    private String volumeTrendMonth;

    /**
     * 互动量趋势day
     */
    private String interactionTrendDay;

    /**
     * 互动量趋势week
     */
    private String interactionTrendWeek;

    /**
     * 互动量趋势month
     */
    private String interactionTrendMonth;

    /**
     * 总声量
     */
    private Long allVolume;

    /**
     * 总互动量
     */
    private Long allInteraction;

    /**
     * 热点分析
     */
    private String hotConclusions;

    /**
     * 热点日期
     */
    private String hotDay;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 热点分析状态:0-采集中 1-分析中 2-分析完成 3-失败
     *
     */
    private Integer isAlgorithmSuc;

    /**
     * 分析结果:0-成功 1-进行中 2-数据周期过短，无法进行热点分析
     */
    private Integer algStatus;

    /**
     * 算法重试次数
     */
    private Integer algorithmTimes;

    /**
     * hot重试
     */
    private Integer hotDayTimes;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}