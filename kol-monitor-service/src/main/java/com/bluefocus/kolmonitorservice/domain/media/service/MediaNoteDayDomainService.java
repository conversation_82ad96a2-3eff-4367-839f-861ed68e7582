package com.bluefocus.kolmonitorservice.domain.media.service;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryTextEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTextResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaNoteDay;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaNoteDayMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【media_note(媒体笔记表)】的数据库操作Service实现
 * @createDate 2024-05-21 17:51:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaNoteDayDomainService extends ServiceImpl<MediaNoteDayMapper, MediaNoteDay> {


    public int findCountByHandle(Long handleId, Integer sourceCode, String hotDay) {
        return this.lambdaQuery()
                .eq(MediaNoteDay::getHandleId, handleId)
                .eq(MediaNoteDay::getHotDay, hotDay)
                .eq(MediaNoteDay::getSourceCode, sourceCode).count();
    }

    public int findCount(Long objId, Integer sourceCode, String hotDay) {
        return this.lambdaQuery()
                .eq(MediaNoteDay::getMediaObjectsId, objId)
                .eq(MediaNoteDay::getHotDay, hotDay)
                .eq(MediaNoteDay::getSourceCode, sourceCode).count();
    }

    public void saveNotes(DataStoryTextResponse.ResultDTO textList, Long objId, Long handleId, Integer sourceCode, String hotDay) {

        List<MediaNoteDay> mediaNotes = new ArrayList<>(32);
        List<DataStoryTextEntity> total = textList.getTotal();
        int size = total.size();

        for (int i = 0; i < size; i++) {
            DataStoryTextEntity text = total.get(i);
            mediaNotes.add(dataStoryText2MediaNote(text, objId, handleId, sourceCode, hotDay));
            if (mediaNotes.size() > 6 || i == size - 1) {
                try {
                    this.getBaseMapper().definedInsertBatch(mediaNotes);
                    mediaNotes = new ArrayList<>(16);
                } catch (Exception e) {
                    log.error("热点日笔记批量插入数据库异常e=[{}]", e);
                }
            }
        }
    }

    public MediaNoteDay dataStoryText2MediaNote(DataStoryTextEntity text, Long objId, Long handleId, Integer code, String hotDay) {
        MediaNoteDay mediaNote = new MediaNoteDay();
        mediaNote.setAuthor(text.getAuthor());
        mediaNote.setMediaObjectsId(objId);
        mediaNote.setHandleId(handleId);
        mediaNote.setTitle(text.getTitle());
        mediaNote.setSourceCode(code);
        mediaNote.setHotDay(hotDay);
        mediaNote.setHeadImg(text.getHeadUrl());
        mediaNote.setCoverOcrContent(text.getCoverOcrContent());
        mediaNote.setAudioOcrContent(text.getAudioAsrContent());
        mediaNote.setHighlightOcrContent(text.getVideoHighlightContent());
        mediaNote.setVideoContent(text.getVideoContent());
        mediaNote.setInteractionCnt(text.getInteractionCnt());
        if (null != text.getFavCnt()) {
            mediaNote.setCollectionCnt(Integer.valueOf(text.getFavCnt()));
        }
        if (null != text.getLikeCnt()) {
            mediaNote.setLikeCnt(Integer.valueOf(text.getLikeCnt()));
        }
        if (null != text.getRepostsCnt()) {
            mediaNote.setRepostsCnt(Integer.valueOf(text.getRepostsCnt()));
        }
        if (null != text.getReviewCnt()) {
            mediaNote.setReviewCnt(Integer.valueOf(text.getReviewCnt()));
        }
        mediaNote.setNoteUrl(text.getUrl());
        mediaNote.setContent(text.getContent());
        mediaNote.setIsOriginal(text.getIsOriginal());

        mediaNote.setPublishTime(Long.valueOf(text.getPublishTime()));
        mediaNote.setSearchKeyword(text.getSearchKeyword());
        mediaNote.setCreateTime(LocalDateTime.now());
        return mediaNote;
    }

    public List<MediaNoteDay> findHotDayNote(Long handleId, Integer sourceCode, String hotDay) {
        return this.lambdaQuery()
                .eq(MediaNoteDay::getHandleId, handleId)
                .eq(MediaNoteDay::getHotDay, hotDay)
                .eq(MediaNoteDay::getSourceCode, sourceCode).list();
    }

    public List<MediaNoteDay> findHotDayNote(Long handId, String hotDay, Integer sourceCode, Integer size) {
        LambdaQueryChainWrapper<MediaNoteDay> q = this.lambdaQuery();
        q.eq(MediaNoteDay::getHandleId, handId);

        if (hotDay != null) {
            q.eq(MediaNoteDay::getHotDay, hotDay);
        }

        if (sourceCode != null) {
            q.eq(MediaNoteDay::getSourceCode, sourceCode);
        }

        if (size != null) {
            q.last("limit " + size);
        }
        return q.orderByDesc(MediaNoteDay::getInteractionCnt).list();
    }

    public void deleteByMediaObjects(Long objId) {
        this.lambdaUpdate().eq(MediaNoteDay::getMediaObjectsId, objId).remove();
    }
}




