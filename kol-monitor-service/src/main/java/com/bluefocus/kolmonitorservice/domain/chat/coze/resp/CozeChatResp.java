package com.bluefocus.kolmonitorservice.domain.chat.coze.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:10
 * @description:
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class CozeChatResp extends Response {

    @JSONField(name = "data")
    private ChatObject data;

    @NoArgsConstructor
    @Data
    public static class ChatObject {
        @JSONField(name = "id")
        private String id;
        @J<PERSON>NField(name = "conversation_id")
        private String conversationId;
        @JSONField(name = "bot_id")
        private String botId;
        @J<PERSON>NField(name = "status")
        private String status;

        @JSONField(name = "created_at")
        private Integer createdAt;
        @JSONField(name = "completed_at")
        private Integer completedAt;
        @JSONField(name = "failed_at")
        private Integer failedAt;

        @JSONField(name = "usage")
        private CozeUsage usage;

        @JSONField(name = "meta_data")
        private Map<String, String> metaData;
        @JSONField(name = "last_error")
        private LastError lastError;

    }
    @Data
    @NoArgsConstructor
    public static class LastError {

        @JSONField(name = "code")
        private Integer code;

        @JSONField(name = "msg")
        private String msg;
    }
    @Data
    @NoArgsConstructor
    public static class CozeUsage {

        @JSONField(name = "token_count")
        private Integer tokenCount;

        @JSONField(name = "output_count")
        private Integer outputCount;

        @JSONField(name = "input_count")
        private Integer inputCount;
    }
}
