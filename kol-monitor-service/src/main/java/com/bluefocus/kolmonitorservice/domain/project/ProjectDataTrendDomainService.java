package com.bluefocus.kolmonitorservice.domain.project;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.domain.project.entity.ProjectDataTrend;
import com.bluefocus.kolmonitorservice.domain.project.repository.ProjectDataTrendMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/7 17:09
 */
@Service
@RequiredArgsConstructor
public class ProjectDataTrendDomainService extends ServiceImpl<ProjectDataTrendMapper, ProjectDataTrend> {

   private final ProjectDataTrendMapper projectDataTrendMapper;

}
