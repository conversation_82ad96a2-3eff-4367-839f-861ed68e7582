package com.bluefocus.kolmonitorservice.domain.media.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.base.enums.SortEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryTextEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTextResponse;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaNote;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaNoteMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【media_note(媒体笔记表)】的数据库操作Service实现
 * @createDate 2024-05-21 17:51:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MediaNoteDomainService extends ServiceImpl<MediaNoteMapper, MediaNote> {

    public List<MediaNote> findByHandleId(long handleId) {
        return this.list(new LambdaQueryWrapper<MediaNote>().eq(MediaNote::getHandleId, handleId));
    }

    public List<MediaNote> findByHandleIdAndCode(long handleId, Integer code) {
        return this.list(new LambdaQueryWrapper<MediaNote>().eq(MediaNote::getHandleId, handleId)
                .eq(MediaNote::getSourceCode, code).orderByDesc(MediaNote::getInteractionCnt)
                .last("limit 3"));
    }

    public int findCount(Long objId, Integer code) {
        return this.count(new LambdaQueryWrapper<MediaNote>().eq(MediaNote::getMediaObjectsId, objId).eq(MediaNote::getSourceCode, code));
    }

    public int findCountByHandle(Long objId, Long handleId, Integer code) {
        return this.count(new LambdaQueryWrapper<MediaNote>().eq(MediaNote::getMediaObjectsId, objId).eq(MediaNote::getHandleId, handleId).eq(MediaNote::getSourceCode, code));
    }

    public Page<MediaNote> findPage(Long mediaObjectsId, Long handleId, String sort, Integer sortType, Integer sourceCode, Integer page, Integer limit) {
        QueryWrapper<MediaNote> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("media_objects_id", mediaObjectsId)
                .eq("handle_id", handleId)
                .eq("source_code", sourceCode);
        if (sortType.equals(1)) {
            queryWrapper.orderByDesc(SortEnum.getFieldByName(sort));
        } else {
            queryWrapper.orderByAsc(SortEnum.getFieldByName(sort));
        }
        return this.page(new Page<>(page, limit), queryWrapper);
    }

    public void saveNotes(DataStoryTextResponse.ResultDTO textList, Long objId, Long handleId, Integer code) {

        ArrayList<MediaNote> mediaNotes = new ArrayList<>(32);
        List<DataStoryTextEntity> total = textList.getTotal();
        int size = total.size();

        for (int i = 0; i < size; i++) {
            try {
                DataStoryTextEntity text = total.get(i);
                mediaNotes.add(dataStoryText2MediaNote(text, objId, handleId, code));
                if (mediaNotes.size() > 6 || i == size - 1) {
                    this.getBaseMapper().definedInsertBatch(mediaNotes);
                    mediaNotes = new ArrayList<>(16);
                }
            } catch (Exception e) {
                log.error("基本笔记批量插入数据库异常e=[{}]", e);
            }
        }
    }

    public MediaNote dataStoryText2MediaNote(DataStoryTextEntity text, Long objId, Long handleId, Integer code) {
        MediaNote mediaNote = new MediaNote();
        mediaNote.setAuthor(text.getAuthor());
        mediaNote.setMediaObjectsId(objId);
        mediaNote.setHandleId(handleId);
        mediaNote.setTitle(text.getTitle());
        mediaNote.setSourceCode(code);
        mediaNote.setHeadImg(text.getHeadUrl());
        mediaNote.setCoverOcrContent(text.getCoverOcrContent());
        mediaNote.setAudioOcrContent(text.getAudioAsrContent());
        mediaNote.setHighlightOcrContent(text.getVideoHighlightContent());
        mediaNote.setVideoContent(text.getVideoContent());
        mediaNote.setInteractionCnt(text.getInteractionCnt());
        if (null != text.getFavCnt()) {
            mediaNote.setCollectionCnt(Integer.valueOf(text.getFavCnt()));
        }
        if (null != text.getLikeCnt()) {
            mediaNote.setLikeCnt(Integer.valueOf(text.getLikeCnt()));
        }
        if (null != text.getRepostsCnt()) {
            mediaNote.setRepostsCnt(Integer.valueOf(text.getRepostsCnt()));
        }
        if (null != text.getReviewCnt()) {
            mediaNote.setReviewCnt(Integer.valueOf(text.getReviewCnt()));
        }
        mediaNote.setNoteUrl(text.getUrl());
        mediaNote.setContent(text.getContent());
        mediaNote.setIsOriginal(text.getIsOriginal());

        mediaNote.setPublishTime(Long.valueOf(text.getPublishTime()));
        mediaNote.setSearchKeyword(text.getSearchKeyword());
        mediaNote.setCreateTime(LocalDateTime.now());
        return mediaNote;
    }

    public void deleteByMediaObjects(Long objId) {
        if (null != objId) {
            this.lambdaUpdate().eq(MediaNote::getMediaObjectsId, objId).remove();
        }
    }
}




