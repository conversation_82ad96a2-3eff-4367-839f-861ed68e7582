package com.bluefocus.kolmonitorservice.domain.gptalk.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class ChatResponseMessage implements Serializable {
    private String role;

    private String content;

    public ChatResponseMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }

}
