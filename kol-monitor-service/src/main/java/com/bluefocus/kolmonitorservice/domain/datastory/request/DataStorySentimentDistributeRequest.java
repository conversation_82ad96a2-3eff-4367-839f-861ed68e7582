package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStorySentimentDistributeResponse;
import org.springframework.beans.factory.annotation.Value;

/**
 * 数说-情感类型统计接口
 */
public class DataStorySentimentDistributeRequest extends DefaultDataStoryRequest<DataStorySentimentDistributeResponse> {

    @Value("${dataStory.api.sentimentDistribute}")
    private String apiMethodName;

    public DataStorySentimentDistributeRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/sentimentDistribute";
    }

    @Override
    public Class<DataStorySentimentDistributeResponse> getResponseClass() {
        return DataStorySentimentDistributeResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSources(), "sources");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
