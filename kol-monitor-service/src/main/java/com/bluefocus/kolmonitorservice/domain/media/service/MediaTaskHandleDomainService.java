package com.bluefocus.kolmonitorservice.domain.media.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.HandleTypeEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaTaskHandleMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0019 2024/6/19 18:30
 * @description:
 */
@Service
public class MediaTaskHandleDomainService extends ServiceImpl<MediaTaskHandleMapper, MediaTaskHandle> {

    public List<MediaTaskHandle> findListByTaskId(Long taskId) {
        return this.lambdaQuery().eq(MediaTaskHandle::getTaskId, taskId)
                .eq(MediaTaskHandle::getPause, Boolean.TRUE).list();
    }

    /**
     * 查询未完成项目
     */
    public MediaTaskHandle findByTaskIdAndMediaObjectsIdState(Long taskId, Long mediaObjectsId, int state) {
        return this.lambdaQuery().eq(ObjectUtil.isNotEmpty(taskId), MediaTaskHandle::getTaskId, taskId)
                .eq(ObjectUtil.isNotEmpty(mediaObjectsId), MediaTaskHandle::getMediaObjectsId, mediaObjectsId)
                .eq(MediaTaskHandle::getStatus, state).one();
    }

    /**
     * 查询当前所有handle
     */
    public List<MediaTaskHandle> findAllTaskHandle(Long taskId, Long mediaObjectsId) {
        return this.lambdaQuery()
                .eq(MediaTaskHandle::getTaskId, taskId)
                .eq(ObjectUtil.isNotEmpty(mediaObjectsId), MediaTaskHandle::getMediaObjectsId, mediaObjectsId)
                .orderByDesc(MediaTaskHandle::getCreateTime)
                .list();
    }

    /**
     * 查询未完成项目
     */
    public List<MediaTaskHandle> findByTaskIdAndMediaObjectsId(Long taskId, Long mediaObjectsId) {
        LambdaQueryChainWrapper<MediaTaskHandle> q = this.lambdaQuery().notIn(MediaTaskHandle::getStatus, 2, 3);
        if (taskId != null) {
            q.eq(MediaTaskHandle::getTaskId, taskId);
        }
        if (mediaObjectsId != null) {
            q.eq(MediaTaskHandle::getMediaObjectsId, mediaObjectsId);
        }

        return q.list();
    }

    public boolean updateByTaskIdAndMediaObjectsIdAndNotSuccess(MediaTaskHandle mediaTaskHandle) {
        LambdaUpdateWrapper<MediaTaskHandle> q = new LambdaUpdateWrapper<>();
        q.notIn(MediaTaskHandle::getStatus, 2, 3);
        if (mediaTaskHandle.getTaskId() != null) {
            q.eq(MediaTaskHandle::getTaskId, mediaTaskHandle.getTaskId());
        }
        if (mediaTaskHandle.getMediaObjectsId() != null) {
            q.eq(MediaTaskHandle::getMediaObjectsId, mediaTaskHandle.getMediaObjectsId());
        }
        q.isNotNull(MediaTaskHandle::getAnalyTime);
        return this.update(mediaTaskHandle, q);
    }

    public boolean updateByTaskIdAndMediaObjectsId(MediaTaskHandle mediaTaskHandle) {
        LambdaUpdateWrapper<MediaTaskHandle> q = new LambdaUpdateWrapper<>();
        if (mediaTaskHandle.getId() != null) {
            q.eq(MediaTaskHandle::getId, mediaTaskHandle.getId());
        }
        if (mediaTaskHandle.getTaskId() != null) {
            q.eq(MediaTaskHandle::getTaskId, mediaTaskHandle.getTaskId());
        }
        if (mediaTaskHandle.getMediaObjectsId() != null) {
            q.eq(MediaTaskHandle::getMediaObjectsId, mediaTaskHandle.getMediaObjectsId());
        }
        if (mediaTaskHandle.getAnalyTime() != null) {
            q.isNull(MediaTaskHandle::getAnalyTime);
        }

        if (mediaTaskHandle.getArtTime() != null) {
            q.isNull(MediaTaskHandle::getArtTime);
        }

        return this.update(mediaTaskHandle, q);
    }


    public List<MediaTaskHandle> findByStatus() {
        return this.lambdaQuery().notIn(MediaTaskHandle::getStatus, 2, 3).list();
    }

    public void updateByTaskIdAndMediaObjectsId(Long mediaTaskId, Long objId) {
        this.lambdaUpdate()
                .set(MediaTaskHandle::getPause, Boolean.FALSE)
                .eq(MediaTaskHandle::getTaskId, mediaTaskId).eq(MediaTaskHandle::getMediaObjectsId, objId)
                .update();
    }

    public List<MediaTaskHandle> getRunningTaskHandleByTask(Long mediaTaskId, Long objId) {
        return this.lambdaQuery()
                .eq(ObjectUtil.isNotNull(mediaTaskId), MediaTaskHandle::getTaskId, mediaTaskId)
                .eq(ObjectUtil.isNotNull(objId), MediaTaskHandle::getMediaObjectsId, objId)
                .eq(MediaTaskHandle::getPause, Boolean.TRUE)
                .list();
    }

    public MediaTaskHandle getRunningTaskHandleByTaskAndObjId(Long mediaTaskId, Long objId) {
        List<MediaTaskHandle> list = getRunningTaskHandleByTask(mediaTaskId, objId);
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    public List<MediaTaskHandle> initialHandleBatch(List<MediaObjects> mediaObjectsList, HandleTypeEnum handleTypeEnum) {
        return mediaObjectsList.stream().map(obj -> {
            MediaTaskHandle mediaTaskHandle = new MediaTaskHandle();
            mediaTaskHandle.setMediaObjectsId(obj.getId());
            mediaTaskHandle.setTaskId(obj.getMediaTaskId());
            mediaTaskHandle.setSources(obj.getSources());
            mediaTaskHandle.setKeyword(obj.getKeyword());
            mediaTaskHandle.setFilterword(obj.getFilterword());
            mediaTaskHandle.setStartTime(obj.getStartTime());
            mediaTaskHandle.setEndTime(obj.getEndTime());
            mediaTaskHandle.setCreateTime(LocalDateTime.now());
            mediaTaskHandle.setStatus(FinishStatusEnum.CRAWLER.getCode());
            mediaTaskHandle.setType(handleTypeEnum.getType());
            this.save(mediaTaskHandle);
            return mediaTaskHandle;
        }).collect(Collectors.toList());
    }
}
