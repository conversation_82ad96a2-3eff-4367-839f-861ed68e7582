package com.bluefocus.kolmonitorservice.domain.datastory.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryTextEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数说-原文内容统计接口
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DataStoryTextResponse extends Response {

    private DataStoryTextResponse.ResultDTO data;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JSONField(name = "total")
        private List<DataStoryTextEntity> total;
    }

}
