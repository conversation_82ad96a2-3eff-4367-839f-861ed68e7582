package com.bluefocus.kolmonitorservice.domain.route.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorinterface.dto.route.req.RouteTaskReq;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteReqRecord;
import com.bluefocus.kolmonitorservice.domain.route.mapper.RouteReqRecordMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 15:56
 * @description:
 */
@Service
@RequiredArgsConstructor
public class RouteReqRecordDomainService extends ServiceImpl<RouteReqRecordMapper, RouteReqRecord> {


    public RouteReqRecord saveRecord(RouteTaskReq request, String bizId) {
        RouteReqRecord routeReqRecord = new RouteReqRecord();
        routeReqRecord.setBizId(bizId);
        routeReqRecord.setToken(request.getToken());
        routeReqRecord.setApi(request.getApi());
        routeReqRecord.setMethod(request.getMethod());
        routeReqRecord.setType(request.getType());
        routeReqRecord.setPlatform(request.getPlatform());
        routeReqRecord.setReqParam(request.getData().toJSONString());
        routeReqRecord.setCreateTime(LocalDateTime.now());
        save(routeReqRecord);
        return routeReqRecord;
    }
}
