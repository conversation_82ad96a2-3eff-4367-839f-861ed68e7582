package com.bluefocus.kolmonitorservice.domain.project.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDate;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;


/**
 * <p>
 * 项目数据趋势  由模板自动生成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08 18:08:16
 */

@Data
@TableName("project_data_trend")
public class ProjectDataTrend implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @TableId(value = "project_id")
    private Long projectId;

    /**
     * 平台数据汇总
     */
    @TableField(value = "plant_data")
    private String plantData;

    /**
     * 种草数占比
     */
    @TableField(value = "plant_notes_rate")
    private String plantNotesRate;

    /**
     * 阅读量占比
     */
    @TableField(value = "plant_read_rate")
    private String plantReadRate;

    /**
     * 数据写入时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 数据消费时间
     */
    @TableField(value = "consume_time")
    private Long consumeTime;


}


