package com.bluefocus.kolmonitorservice.domain.route;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0003 2024/8/3 17:07
 * @description:
 */
public abstract class RouteDefaultRequest<T extends Response> implements Request<T> {

    protected Map<String, String> headerMap;
    protected HashMap<String, Object> udfParams;

    protected String method;
    protected String api;

    /**
     * 添加URL自定义请求参数。
     */
    public void putOtherTextParam(String key, Object value) {
        if (this.udfParams == null) {
            this.udfParams = new HashMap<>(8);
        }
        this.udfParams.put(key, value);
    }

    @Override
    public String getApiMethodName() {
        return "";
    }

    @Override
    public String getTimestamp() {
        return Times.dateTimeToStringFormat(LocalDateTime.now());
    }

    @Override
    public void check() throws ApiRuleException {
    }
}