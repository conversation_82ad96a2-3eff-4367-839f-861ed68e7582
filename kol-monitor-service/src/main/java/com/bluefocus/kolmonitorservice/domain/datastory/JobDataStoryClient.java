package com.bluefocus.kolmonitorservice.domain.datastory;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.TreeMap;

/**
 * 数说任务-执行端
 * <AUTHOR>
 */
@Getter
@Service
public class JobDataStoryClient implements Client {

    @Value("${dataStory.task.serverUrl}")
    protected String serverUrl;

    @Setter
    @Value("${dataStory.api.serverUrl}")
    protected String apiServerUrl;

    protected int connectTimeout = 15000;
    protected int readTimeout = 30000;

    @Setter
    protected boolean needCheckRequest = true;

    public JobDataStoryClient() {
    }

    public JobDataStoryClient(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public JobDataStoryClient(String serverUrl, int connectTimeout, int readTimeout) {
        this(serverUrl);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {
        W tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }

        String contentType = request.getHeaderMap().get("Content-Type");
        String query = null;
        String params = request.getParams();
        if (ContentType.JSON.getValue().equals(contentType)) {
            query = params;
        } else if (ContentType.FORM_URLENCODED.getValue().equals(contentType)) {
            TreeMap<String, String> treeMap = new TreeMap<>();
            if (params != null && !params.isEmpty()) {
                treeMap.putAll(JSON.parseObject(params, TreeMap.class));
            }
            query = UrlQuery.of(treeMap).build(StandardCharsets.UTF_8);
        }

        try {
            String apiMethodName = request.getApiMethodName();
            String url = this.serverUrl;
            if (apiMethodName.startsWith("count?") || apiMethodName.startsWith("search?")) {
                url = this.apiServerUrl;
            }
            url += apiMethodName;

            HttpRequest httpRequest;
            if (query == null) {
                httpRequest = HttpUtil.createGet(url);
            } else {
                httpRequest = HttpUtil.createPost(url).body(query);
            }
            HttpResponse httpResponse = httpRequest.setConnectionTimeout(this.connectTimeout).setReadTimeout(this.readTimeout).headerMap(request.getHeaderMap(), true).execute();
            String body = httpResponse.body();
            if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
                tRsp = JSON.parseObject(body, request.getResponseClass());
            } else {
                tRsp = request.getResponseClass().newInstance();
            }
            tRsp.setBody(body);
            tRsp.setRequestUrl(url);
            tRsp.setHeaderContent(httpResponse.headers());
        } catch (Exception e) {
            try {
                tRsp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            tRsp.setCode("-1");
            tRsp.setMsg(e.getMessage());
            return tRsp;
        }
        return tRsp;
    }
}
