package com.bluefocus.kolmonitorservice.domain.article.entity;

import lombok.Data;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 博文表  由模板自动生成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08 18:08:16
 */

@Data
@Component
public class ArticleUpload implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 博文链接
     */
    private String articleUrl;

    /**
     * 博文正式链接
     */
    private String articleFormalUrl;

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 平台类型
     */
    private Integer platType;


    /**
     * 删除状态 	0:未删除 1:已删除
     */
    private Integer deleteStatus;

    /**
     * 链接状态 00:待监测,01:监测中,02:监测完成 10:链接识别异常,11:链接失效异常,02:链接监测异常
     */
    private String status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}


