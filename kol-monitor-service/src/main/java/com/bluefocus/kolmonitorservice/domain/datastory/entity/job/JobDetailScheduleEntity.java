package com.bluefocus.kolmonitorservice.domain.datastory.entity.job;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
@EqualsAndHashCode()
public class JobDetailScheduleEntity implements Serializable {

    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "startTime")
    private Long startTime;

    @JSONField(name = "stopTime")
    private Long stopTime;

    @JSONField(name = "emails")
    private String emails;

    @J<PERSON><PERSON>ield(name = "interval")
    private Integer interval;

    @JSONField(name = "unit")
    private String unit;

    @JSONField(name = "isTemp")
    private Integer isTemp;

    @JSONField(name = "jobId")
    private Long jobId;

    @JSONField(name = "delayTime")
    private Integer delayTime;

    @J<PERSON>NField(name = "searchStartTime")
    private Integer searchStartTime;

    @JSONField(name = "searchEndTime")
    private Integer searchEndTime;

    @JSO<PERSON>ield(name = "searchUnit")
    private String searchUnit;

    @JSONField(name = "isRealTime")
    private Integer isRealTime;

    @JSONField(name = "emailMode")
    private Integer emailMode;
}
