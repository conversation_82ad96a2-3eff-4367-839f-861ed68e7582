package com.bluefocus.kolmonitorservice.domain.article;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorinterface.dto.res.ArticleDetailResponse;
import com.bluefocus.kolmonitorservice.domain.article.entity.KolVideoCloud;
import com.bluefocus.kolmonitorservice.domain.article.repository.KolVideoCloudMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: yjLiu
 * @date: 0021 2025/3/21 10:39
 * @description:
 */
@Slf4j
@Service
public class KolVideoCloudDomainService extends ServiceImpl<KolVideoCloudMapper, KolVideoCloud> {

    public void insertBatch(List<KolVideoCloud> kolVideos) {
        try {
            this.baseMapper.insertBatch(kolVideos);
        } catch (Exception e) {
            log.warn("批量插入云图视频数据失败，原因：{}", e.getMessage());
        }
    }

    public List<KolVideoCloud> findByVideoIds(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return this.lambdaQuery().in(KolVideoCloud::getVideoId, ids).list();
    }

    public void covert(ArticleDetailResponse articleDetailResponse, KolVideoCloud kolVideoCloud) {
        if (kolVideoCloud == null || articleDetailResponse == null) {
            return;
        }
        articleDetailResponse.setSearchAfterPv(kolVideoCloud.getSearchAfterPv());
        articleDetailResponse.setSearchAfterUv(kolVideoCloud.getSearchAfterUv());
        articleDetailResponse.setSearchAfterViewPv(kolVideoCloud.getSearchAfterViewPv());
        articleDetailResponse.setSearchAfterViewUv(kolVideoCloud.getSearchAfterViewUv());
        articleDetailResponse.setPluginClickRate(kolVideoCloud.getPluginClickRate());
        articleDetailResponse.setPluginShowNum(kolVideoCloud.getPluginShowNum());
        articleDetailResponse.setPluginClickNum(kolVideoCloud.getPluginClickNum());
    }

    public List<KolVideoCloud> findIdListByKolDyId(String kolDyAccount) {
        return lambdaQuery().eq(KolVideoCloud::getKolDyAccount, kolDyAccount).list();
    }
}
