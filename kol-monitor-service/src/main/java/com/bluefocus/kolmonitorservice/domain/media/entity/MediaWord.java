package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 媒体词云表
 * @TableName media_word
 */
@TableName(value ="media_word")
@Data
public class MediaWord implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 媒体对象ID
     */
    private Long mediaObjectsId;

    /**
     * 处理id
     */
    private Long handleId;

    /**
     * 数据源code 0全部 1小红书 2抖音 3微博
     */
    private Integer sourceCode;

    /**
     * 所有词
     */
    private String allWord;

    /**
     * 生成图
     */
    private String genImg;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 词云状态：0-采集中 1-分析中 2-分析完成 3-失败
     */
    private Integer isInit;

    /**
     * wordart状态：@enum WordArtEnum
     */
    private Integer artStatus;


    /**
     * 异常次数
     */
    private Integer errorTimes;

    /**
     * 
     */
    private String oldImg;

    /**
     * 
     */
    private Long genTime;

}