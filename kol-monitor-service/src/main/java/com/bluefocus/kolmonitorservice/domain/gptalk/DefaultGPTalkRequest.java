package com.bluefocus.kolmonitorservice.domain.gptalk;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.ContentType;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;

import java.util.HashMap;
import java.util.Map;

public abstract class DefaultGPTalkRequest<T extends Response> implements Request<T> {

    // HTTP请求头参数
    protected Map<String, String> headerMap;

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
            this.headerMap.put("Content-Type", ContentType.JSON.getValue());
        }
        return this.headerMap;
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

    public void putHeaderParam(String key, String value) {
        getHeaderMap().put(key, value);
    }

    @Override
    public String getTimestamp() {
        return DateUtil.now();
    }

}