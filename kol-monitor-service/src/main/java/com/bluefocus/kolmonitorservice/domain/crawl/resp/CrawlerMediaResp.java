package com.bluefocus.kolmonitorservice.domain.crawl.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTextResponse;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0003 2024/6/3 16:06
 * @description:
 */
@Getter
@Setter
public class CrawlerMediaResp {

    private Map<String, Map<String, Long>> volumeTread;
    private Map<String, BigDecimal> sentimentDistribute;
    private Map<String, Map<String, Long>> interactionTread;
    private Map<String, Map<String, Long>> cloudWord;
    private Map<String, Map<String, Long>> cloudWordTopic;

    @JSONField(name = "false_list")
    private List<String> failureList;

    private Map<String, DataStoryTextResponse.ResultDTO> yuanwen;

    /**
     * 1获取数据异常 2 登录失败
     */
    @JSONField(name = "state_code")
    private String stateCode;

    /**
     * 请求地址
     */
    @J<PERSON><PERSON>ield(name = "req_url")
    private String reqUrl;

    @J<PERSON><PERSON>ield(name = "error_msg")
    private String errorMsg;
}
