package com.bluefocus.kolmonitorservice.domain.datastory.entity.job;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashMap;

@Data
@EqualsAndHashCode()
public class JobGlobalConfigEntity implements Serializable {

    @JSONField(name = "email")
    private String email = "<EMAIL>";

    @JSONField(name = "emailMode")
    private String emailMode;


    @JSONField(name = "isRealTime")
    private HashMap<String, Object> isRealTime = new HashMap<String, Object>() {{
        put("POST", 0);
    }};
}
