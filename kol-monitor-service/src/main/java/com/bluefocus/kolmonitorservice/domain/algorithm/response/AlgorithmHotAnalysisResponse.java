package com.bluefocus.kolmonitorservice.domain.algorithm.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import com.bluefocus.kolmonitorservice.domain.algorithm.entity.HotAnalysisRespEntity;
import lombok.*;


/**
 * 算法-热点分析返回值
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class AlgorithmHotAnalysisResponse extends Response {

    // code 200 成功
    private AlgorithmHotAnalysisResponse.ResultDTO data;

    @NoArgsConstructor
    @Getter
    @Setter
    @ToString
    public static class ResultDTO {
        @JSONField(name = "media_objects_id")
        private Long mediaObjectsId;

        @JSONField(name = "source")
        private String source;

        @JSONField(name = "hotConclusion")
        HotAnalysisRespEntity hotConclusion;
    }


}
