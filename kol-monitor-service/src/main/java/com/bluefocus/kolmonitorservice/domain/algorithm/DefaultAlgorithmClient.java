package com.bluefocus.kolmonitorservice.domain.algorithm;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * 算法-构造器
 */
@Getter
public class DefaultAlgorithmClient implements Client {
    private final Logger log = LoggerFactory.getLogger(DefaultAlgorithmClient.class);

    protected String serverUrl;

    // 默认连接超时时间为15秒
    protected int connectTimeout = 180000;

    protected int readTimeout = 180000;

    @Setter
    protected boolean needCheckRequest = true;

    public DefaultAlgorithmClient() {
    }

    public DefaultAlgorithmClient(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public DefaultAlgorithmClient(String serverUrl, int connectTimeout, int readTimeout) {
        this(serverUrl);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {
        W tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }
        String query = request.getParams();
        Map<String, String> headerMap = request.getHeaderMap();
        HttpResponse httpResponse = null;
        try {
            httpResponse = HttpUtil.createPost(serverUrl)
                    .setConnectionTimeout(this.connectTimeout).setReadTimeout(this.readTimeout)
                    .headerMap(headerMap, true).body(query).execute();
            String body = httpResponse.body();
            if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
                tRsp = JSON.parseObject(body, request.getResponseClass());
            } else {
                tRsp = request.getResponseClass().newInstance();
            }
            tRsp.setBody(body);
            tRsp.setSuccess(httpResponse.isOk());
            tRsp.setRequestUrl(this.serverUrl + request.getApiMethodName());
        } catch (Exception e) {
            log.error("请求算法分析接口异常，响应体={}, err={}", httpResponse != null ? httpResponse.body() : "无响应", e);
            try {
                tRsp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            tRsp.setCode("-1");
            tRsp.setMsg(e.getMessage());
            return tRsp;
        }
        return tRsp;
    }
}
