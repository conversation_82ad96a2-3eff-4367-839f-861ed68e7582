package com.bluefocus.kolmonitorservice.domain.chat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 社媒对话历史记录表
 * <AUTHOR>
 * @TableName media_chat_history
 */
@TableName(value ="media_chat_history")
@Data
public class MediaChatHistory implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 对话id
     */
    private Long chatId;

    /**
     * 发送内容
     */
    private String sendContent;

    /**
     * 回复内容
     */
    private String recContent;

    /**
     * 消息发送人
     */
    private Long sendUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}