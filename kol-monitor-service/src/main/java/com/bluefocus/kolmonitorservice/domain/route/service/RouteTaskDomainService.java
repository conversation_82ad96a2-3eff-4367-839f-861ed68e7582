package com.bluefocus.kolmonitorservice.domain.route.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.application.service.route.impl.DataStoryPlatform;
import com.bluefocus.kolmonitorservice.base.common.TokenCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.route.DefaultRouteClient;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteTask;
import com.bluefocus.kolmonitorservice.domain.route.entity.RouteToken;
import com.bluefocus.kolmonitorservice.domain.route.mapper.RouteTaskMapper;
import com.bluefocus.kolmonitorservice.domain.route.mapper.RouteTokenMapper;
import com.bluefocus.kolmonitorservice.domain.route.platform.datastory.req.DsJobDefaultRequest;
import com.bluefocus.kolmonitorservice.domain.route.platform.datastory.resp.DsJobDefaultResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: yjLiu
 * @date: 0002 2024/8/2 15:56
 * @description:
 */
@Service
@RequiredArgsConstructor
public class RouteTaskDomainService extends ServiceImpl<RouteTaskMapper, RouteTask> {

    private final DefaultRouteClient defaultRouteClient;
    private final RouteTokenMapper routeTokenMapper;

    public DsJobDefaultResponse runDataStory(DsJobDefaultRequest dsJobDefaultRequest) throws ApiException {
        if (null == dsJobDefaultRequest) {
            return null;
        }
        return defaultRouteClient.doExecute(dsJobDefaultRequest);
    }

    public TokenCommon getToken(String platform) {
        if (DataStoryPlatform.DATA_STORY.equals(platform)) {
            RouteToken routeToken = routeTokenMapper.selectOne(new LambdaQueryWrapper<RouteToken>().orderByDesc(RouteToken::getId).last("limit 1"));
            return new TokenCommon(DataStoryPlatform.DATA_STORY, null, routeToken.getToken());
        }
        return null;
    }

    public Page<RouteTask> findTaskPage(String bizId, Long startTime, Long endTime, int page, int limit) {
        LambdaQueryWrapper<RouteTask> eq = new LambdaQueryWrapper<RouteTask>().eq(RouteTask::getBizId, bizId);

        if (startTime != null) {
            eq.ge(RouteTask::getCreateTime, Times.toLocalDateTime(startTime));
        }
        if (endTime != null) {
            eq.le(RouteTask::getCreateTime, Times.toLocalDateTime(endTime));
        }
        return getBaseMapper().selectPage(new Page<>(page, limit), eq);
    }

    public RouteTask findOldTaskBy(String bizId, String platform, Long jobId) {

        LambdaQueryWrapper<RouteTask> eq = new LambdaQueryWrapper<RouteTask>().eq(RouteTask::getBizId, bizId).eq(RouteTask::getPlatform, platform)
                .eq(RouteTask::getJobId, jobId.toString()).orderByDesc(RouteTask::getCreateTime).last("limit 1");
        return this.getOne(eq);
    }

    public List<RouteTask> findUnFinishList() {

        LambdaQueryWrapper<RouteTask> eq = new LambdaQueryWrapper<RouteTask>().ne(RouteTask::getState, 2);
        return this.list(eq);
    }
}
