package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.http.ContentType;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSJobSearchResponse;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 数说-查询任务-文本
 */
@Setter
public class DSJobSearchRequest extends JobDataStoryRequest<DSJobSearchResponse> {

    @Value("${dataStory.api.search}")
    private String apiMethodName;

    @Value("${dataStory.api.token}")
    private String token;

    private Long jobId;

    /**
     * 开始时间： yyyyMMddHHmmss
     * 20240527000000
     */
    private String startTime;

    /**
     * 结束时间： yyyyMMddHHmmss
     * 20240527235959
     */
    private String endTime;

    /**
     * 翻页唯一标识
     */
    private String scrollId;

    @Override
    public String getParams() {
        return null;
    }

    @Override
    public String getApiMethodName() {
        TreeMap<String, Object> treeMap = new TreeMap<>();
        treeMap.put("token", this.token);
        treeMap.put("jobId", this.jobId);
        treeMap.put("startTime", this.startTime);
        treeMap.put("endTime", this.endTime);
        treeMap.put("scrollId", this.scrollId);
        return (apiMethodName != null ? apiMethodName : "search") + "?" + UrlQuery.of(treeMap);
    }

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
        }
        this.headerMap.put("Content-Type", ContentType.JSON.getValue());
        return this.headerMap;
    }


    @Override
    public Class<DSJobSearchResponse> getResponseClass() {
        return DSJobSearchResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.token, "token");
        RequestCheckUtils.checkNotEmpty(this.jobId, "jobId");
        RequestCheckUtils.checkNotEmpty(this.startTime, "startTime");
        RequestCheckUtils.checkNotEmpty(this.endTime, "endTime");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
