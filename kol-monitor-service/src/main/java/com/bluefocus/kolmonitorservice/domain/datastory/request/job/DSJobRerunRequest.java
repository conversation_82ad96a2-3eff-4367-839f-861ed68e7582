package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSJobRerunResponse;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 数说-重跑子任务
 */
@Setter
public class DSJobRerunRequest extends JobDataStoryRequest<DSJobRerunResponse> {

    @Value("${dataStory.task.reRun}")
    private String apiMethodName;

    private Long id;

    @Override
    public String getParams() {
        TreeMap<String, Object> treeMap = new TreeMap<>();
        treeMap.put("id", this.id);
        return JSON.toJSONString(treeMap);
    }

    @Override
    public String getApiMethodName() {
        return (apiMethodName != null ? apiMethodName : "job/schedule/rerun");
    }

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
        }
        this.headerMap.put("Content-Type", ContentType.FORM_URLENCODED.getValue());
        return this.headerMap;
    }


    @Override
    public Class<DSJobRerunResponse> getResponseClass() {
        return DSJobRerunResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.id, "id");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
