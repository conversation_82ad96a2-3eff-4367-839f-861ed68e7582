package com.bluefocus.kolmonitorservice.domain.article.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

import lombok.Data;


/**
 * <p>
 * 博文详情表  由模板自动生成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08 18:08:16
 */

@Data
@TableName("article_detail")
public class ArticleDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "article_id")
    private Long articleId;

    /**
     * 文章标题
     */
    @TableField(value = "article_title")
    private String articleTitle;

    /**
     * 汇总数据JSON
     */
    @TableField(value = "tend_obj")
    private String tendObj;

    /**
     * 数据写入时间
     */
    @TableField(value = "consume_time")
    private Long consumeTime;

    /**
     * 变更时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}


