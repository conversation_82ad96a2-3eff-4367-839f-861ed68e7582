package com.bluefocus.kolmonitorservice.domain.datastory.entity.job;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode()
public class JobConditionEntity implements Serializable {
    @J<PERSON><PERSON><PERSON>(name = "name")
    private String name = "分析对象1";

    @JSONField(name = "keywords")
    private String keywords;


    @JSONField(name = "filterwords")
    private String filterWords;


}
