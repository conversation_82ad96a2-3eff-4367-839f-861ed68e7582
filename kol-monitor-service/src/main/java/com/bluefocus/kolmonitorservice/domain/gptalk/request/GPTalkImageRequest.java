package com.bluefocus.kolmonitorservice.domain.gptalk.request;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.gptalk.response.GPTalkImageResponse;
import com.bluefocus.kolmonitorservice.domain.gptalk.DefaultGPTalkRequest;
import lombok.Setter;


public class GPTalkImageRequest extends DefaultGPTalkRequest<GPTalkImageResponse> {

    @Setter
    @JSONField(name = "prompt")
    private String prompt;

    @JSONField(name = "n")
    private Integer n = 1;

    @Setter
    @JSONField(name = "size")
    private String size = "1024x1024";

    @Setter
    @JSONField(name = "response_format")
    private String responseFormat = "url";

    @Setter
    @JSONField(name = "quality")
    private String quality = "standard";


    @Override
    public String getApiMethodName() {
        return "Dalle3/images/generations?api-version=2024-04-01-preview";
    }

    @Override
    public String getParams() {
        JSONObject params = new JSONObject();
        params.put("prompt", this.prompt);
        params.put("n", this.n);
        params.put("size", this.size);
        params.put("response_format", this.responseFormat);
        params.put("quality", this.quality);
        return params.toJSONString();
    }

    @Override
    public Class<GPTalkImageResponse> getResponseClass() {
        return GPTalkImageResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkMaxLength(this.prompt,4000, "prompt");
        RequestCheckUtils.checkNotEmpty(this.prompt, "prompt");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }
}
