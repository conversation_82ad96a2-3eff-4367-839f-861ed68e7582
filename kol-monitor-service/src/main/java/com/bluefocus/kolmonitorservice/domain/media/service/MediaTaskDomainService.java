package com.bluefocus.kolmonitorservice.domain.media.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.base.enums.DeleteStatusEnum;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTask;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaTaskMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【media_task(媒体任务表)】的数据库操作Service实现
 * @createDate 2024-05-21 17:51:36
 */
@Service
@RequiredArgsConstructor
public class MediaTaskDomainService extends ServiceImpl<MediaTaskMapper, MediaTask> {

    public Long saveOne(String name, Long userId) {
        MediaTask mediaTask = new MediaTask();

        mediaTask.setCreateTime(LocalDateTime.now());
        mediaTask.setName(name);
        mediaTask.setOperatorId(userId);
        mediaTask.setStatus(DeleteStatusEnum.UNDELETE.getCode());
        this.save(mediaTask);

        return mediaTask.getId();
    }

    public IPage<MediaTask> findMediaTaskPage(String taskName, Long startTime, Long endTime, Long userId, Integer page, Integer limit) {
        LambdaQueryWrapper<MediaTask> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MediaTask::getOperatorId, userId)
                .like(StringUtils.isNotEmpty(taskName), MediaTask::getName, taskName)
                .orderByDesc(MediaTask::getCreateTime);

        if (null != startTime) {
            queryWrapper.ge(MediaTask::getCreateTime, Times.toLocalDateTime(startTime));
        }
        if (null != endTime) {
            queryWrapper.le(MediaTask::getCreateTime, Times.toLocalDateTime(endTime));
        }
        return getBaseMapper().selectPage(new Page<>(page, limit), queryWrapper);
    }

    public void judgeFinishStatus(Long id) {
        this.lambdaUpdate().eq(MediaTask::getId, id).set(MediaTask::getStatus, 1).set(MediaTask::getUpdateTime, LocalDateTime.now()).update();
    }

    public Integer findCountNewDay() {
        return this.lambdaQuery().ge(MediaTask::getCreateTime, LocalDate.now().atTime(LocalTime.MIN)).count();
    }

    public List<MediaTask> findMediaTaskByName(String name, Long userId) {
        return this.lambdaQuery().eq(StringUtils.isNotBlank(name) ,MediaTask::getName, name).eq(MediaTask::getOperatorId, userId).list();
    }
}




