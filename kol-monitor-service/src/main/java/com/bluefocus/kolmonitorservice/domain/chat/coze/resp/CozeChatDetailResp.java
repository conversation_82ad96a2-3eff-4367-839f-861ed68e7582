package com.bluefocus.kolmonitorservice.domain.chat.coze.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:10
 * @description:
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class CozeChatDetailResp extends Response {

    @JSONField(name = "data")
    private List<ChatDetail> data;

    @NoArgsConstructor
    @Data
    public static class ChatDetail {
        // 消息Id
        @J<PERSON><PERSON>ield(name = "id")
        private String id;
        @JSONField(name = "chat_id", alternateNames = {"id"})
        private String chatId;
        @JSONField(name = "conversation_id")
        private String conversationId;
        @JSONField(name = "bot_id")
        private String botId;
        @JSONField(name = "content")
        private String content;
        @J<PERSON><PERSON>ield(name = "content_type")
        private String contentType;
        @J<PERSON><PERSON>ield(name = "role")
        private String role;
        @J<PERSON><PERSON>ield(name = "type")
        private String type;
        @JSONField(name = "status")
        private String status;

    }

}
