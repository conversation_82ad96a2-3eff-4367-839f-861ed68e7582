package com.bluefocus.kolmonitorservice.domain.wordart.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class WordArtOutputEntity implements Serializable {
    private String format = "png";
    private Double margin = 0.05;
    private Double quality = 2.0;
    private Boolean removeBackgroundColor = false;
    private Boolean removeBackgroundImage = false;
}
