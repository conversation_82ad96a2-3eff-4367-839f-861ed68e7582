package com.bluefocus.kolmonitorservice.domain.chat.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorservice.base.enums.ChatStatusEnum;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaConversation;
import com.bluefocus.kolmonitorservice.domain.chat.mapper.MediaConversationMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @author: yjLiu
 * @date: 0016 2024/10/16 10:56
 * @description:
 */
@RequiredArgsConstructor
@Service
public class MediaConversationDomainService extends ServiceImpl<MediaConversationMapper, MediaConversation> {

    public MediaConversation findCurrentConversation(Long mediaObjectsId, Long userId) {
        Assert.isFalse(null == mediaObjectsId && null == userId, "双ID不可同时为null");
        return getOne(new LambdaQueryWrapper<MediaConversation>()
                .eq(null != mediaObjectsId, MediaConversation::getMediaObjId, mediaObjectsId)
                .eq(null != userId, MediaConversation::getCreatUserId, userId)
                .eq(MediaConversation::getStatus, ChatStatusEnum.COMPLETED.getCode())
                .orderByDesc(MediaConversation::getCreateTime)
                .last("limit 1"));
    }

    public Boolean clean(Long mediaObjectsId, Long conversationId) {
        Assert.isFalse(null == mediaObjectsId && null == conversationId, "双ID不可同时为null");
        return update(new LambdaUpdateWrapper<MediaConversation>()
                .eq(null != mediaObjectsId, MediaConversation::getMediaObjId, mediaObjectsId)
                .eq(null != conversationId, MediaConversation::getConversationId, conversationId)
                .set(MediaConversation::getStatus, ChatStatusEnum.CHAT_LOSE.getCode()));
    }

    public MediaConversation create(Long mediaObjectsId, Long conversationId, Long userId) {
        MediaConversation mediaConversation = new MediaConversation();
        mediaConversation.setConversationId(conversationId);
        mediaConversation.setMediaObjId(mediaObjectsId);
        mediaConversation.setCreatUserId(userId);
        mediaConversation.setCreateTime(LocalDateTime.now());
        mediaConversation.setStatus(ChatStatusEnum.COMPLETED.getCode());
        save(mediaConversation);
        return mediaConversation;
    }
}
