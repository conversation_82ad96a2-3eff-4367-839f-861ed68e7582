package com.bluefocus.kolmonitorservice.domain;

import com.bluefocus.basebean.bean.ResponseBean;
import com.bluefocus.common.BaseController;
import com.bluefocus.usercenterinterface.client.UserCenterClient;
import com.bluefocus.usercenterinterface.dto.res.LarkUserRes;
import com.bluefocus.usercenterinterface.dto.res.UserInfoRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 获取用户信息
 * <AUTHOR>
 */
@Slf4j
@Component
public class UserCommon extends BaseController {

    private final UserCenterClient userCenterClient;

    public UserCommon(UserCenterClient userCenterClient) {
        this.userCenterClient = userCenterClient;
    }

    /**
     * 获取用户ID
     *
     * @param required 是否必填
     */
    @Override
    public Long getUserId(boolean required) {
//        return "1234567890";
        return super.getUserId(required);
    }

    /**
     * 获取平台名称
     */
    @Override
    public String getAppName() {
        log.debug("appName 的值为 : {}", super.getAppName());
        return super.getAppName();
    }

    /**
     * 获取用户名称
     */
    public String getUserName(Long userId) {
//        return "测试用户";
        ResponseBean<UserInfoRes> responseBean = userCenterClient.getUserById(userId);
        if (responseBean.getCode() == 0) {
            return responseBean.getData().getNickname();
        }
        return null;
    }

    /**
     * 获取用户部门
     */
    public LarkUserRes getUserDept(Long userId) {

        ResponseBean<LarkUserRes> responseBean = null;
        try {
            log.info("请求用户中心，id={}", userId);
            responseBean = userCenterClient.getLarkDeptById(userId);
            if (responseBean.getCode() == 0) {
                return responseBean.getData();
            }
        } catch (Exception e) {
            log.warn("用户中心异常,结果={}", responseBean);
        }
        return null;
    }
}
