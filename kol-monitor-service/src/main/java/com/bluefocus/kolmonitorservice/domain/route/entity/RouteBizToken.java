package com.bluefocus.kolmonitorservice.domain.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 路由业务组token
 * @TableName route_biz_token
 */
@TableName(value ="route_biz_token")
@Data
public class RouteBizToken implements Serializable {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务组id
     */
    private String bizId;

    /**
     * token
     */
    private String token;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 失效时间
     */
    private LocalDateTime expireTime;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}