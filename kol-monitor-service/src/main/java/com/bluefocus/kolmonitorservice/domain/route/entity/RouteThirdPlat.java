package com.bluefocus.kolmonitorservice.domain.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 路由三方平台
 * @TableName route_third_plat
 */
@TableName(value ="route_third_plat")
@Data
public class RouteThirdPlat implements Serializable {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 平台
     */
    private String platform;

    /**
     * 平台名称
     */
    private String name;

    /**
     * 介绍
     */
    private String content;

    /**
     * 平台地址
     */
    private String url;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}