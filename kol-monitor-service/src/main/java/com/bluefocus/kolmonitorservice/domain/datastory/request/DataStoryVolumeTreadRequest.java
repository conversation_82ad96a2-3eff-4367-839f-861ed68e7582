package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryVolumeTreadResponse;
import org.springframework.beans.factory.annotation.Value;

/**
 * 数说-声量趋势统计接口
 */
public class DataStoryVolumeTreadRequest extends DefaultDataStoryRequest<DataStoryVolumeTreadResponse> {

    @Value("${dataStory.api.volumeTread}")
    private String apiMethodName;

    public DataStoryVolumeTreadRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/volumeTread";
    }

    @Override
    public Class<DataStoryVolumeTreadResponse> getResponseClass() {
        return DataStoryVolumeTreadResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSource(), "source");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
