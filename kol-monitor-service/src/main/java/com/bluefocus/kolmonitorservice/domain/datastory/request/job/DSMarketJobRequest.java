package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.job.*;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSMarketJobResponse;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 数说-数据超市模块-创建任务
 */
@Setter
public class DSMarketJobRequest extends JobDataStoryRequest<DSMarketJobResponse> {

    @Value("${dataStory.task.marketJob}")
    private String apiMethodName;

    private String jobName;
    /**
     * 测试文件夹: 31948
     */
    private Long sheetId = 31948L;

    /**
     * 微博-6
     * 微信-7
     * 抖音-8
     * 豆瓣-9
     * 知乎-10
     * 今日头条-11
     * 快手-12
     * 小红书-13
     * 哔哩哔哩-14
     * 汽车之家-15
     * 太平洋汽车网-16
     * 易车网-17
     * 爱卡汽车网-18
     * 懂车帝-53
     * 得物-71
     * 车质网-72
     * 酷安-74
     * OPPO社区-75
     * 晋江文学城-76
     * 百度贴吧-77
     * NGA论坛-78
     * 虎扑-83
     * lofter-84
     * 好游快爆-85
     * 黑猫投诉-92
     */
    private Integer appId;

    /**
     * 豆瓣小组-1
     * 豆瓣电影-2
     * 豆瓣电视剧-3
     * 微博-4
     * 今日头条-5
     * 快手-6
     * 抖音-7
     * 知乎-8
     * 哔哩哔哩-9
     * 微信-10
     * 小红书-11
     * B站动态-56
     * 汽车之家新闻-51
     * 易车网新闻-52
     * 爱卡汽车网新闻-53
     * 懂车帝pc_新闻-54
     * 太平洋汽车网新闻-55
     * 得物笔记-57
     * 车质网-58
     * 得物商品-60
     * 酷安-61
     * OPPO社区-62
     * 晋江文学城-63
     * 百度贴吧-64
     * NGA论坛-65
     * 虎扑-67
     * lofter-68
     * 好游快爆-71
     * 黑猫投诉-76
     */
    private Integer scopeId;

    /**
     * ETL模板ID
     */
    private Long titTemplateId;

    private String keywords;

    private String filterWords;

    private String type;

    private Long startDataTime;

    private Long endDataTime;

    private JobSourceConfigEntity sourceConfig;

    private JobScheduleConfigEntity scheduleConfig;

    private JobGlobalConfigEntity globalConfig;


    @Override
    public String getParams() {
        JSONObject params = new JSONObject();
        if (ObjectUtil.isNull(sourceConfig)) {
            sourceConfig = new JobSourceConfigEntity();
            JobConditionEntity condition = new JobConditionEntity();
            condition.setKeywords(keywords);
            condition.setFilterWords(filterWords);
            sourceConfig.setCondition(Collections.singletonList(condition));
        }

        if (ObjectUtil.isNull(scheduleConfig)) {
            scheduleConfig = new JobScheduleConfigEntity();
            if (this.type != null) {
                scheduleConfig.setType(this.type);
            }
            scheduleConfig.setStartDataTime(startDataTime);
            scheduleConfig.setEndDataTime(endDataTime);
        }

        JobExportConfigEntity exportConfig = new JobExportConfigEntity();
        String yyyyMMdd = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String indexName = exportConfig.getEs().getIndexName() + yyyyMMdd;
        exportConfig.getEs().setIndexName(indexName);

        if (ObjectUtil.isNull(globalConfig)) {
            globalConfig = new JobGlobalConfigEntity();
        }

        params.put("jobName", this.jobName);
        params.put("sheetId", this.sheetId);
        params.put("appId", this.appId);
        params.put("scopeId", this.scopeId);
        params.put("sourceConfig", this.sourceConfig);
        params.put("scheduleConfig", this.scheduleConfig);
        params.put("exportConfig", exportConfig);
        params.put("globalConfig", this.globalConfig);
        return params.toJSONString();
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "application/market/job/v1/run";
    }

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
        }
        this.headerMap.put("Content-Type", ContentType.JSON.getValue());
        return this.headerMap;
    }


    @Override
    public Class<DSMarketJobResponse> getResponseClass() {
        return DSMarketJobResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.jobName, "jobName");
        RequestCheckUtils.checkNotEmpty(this.appId, "appId");
        RequestCheckUtils.checkNotEmpty(this.scopeId, "scopeId");
        if (ObjectUtil.isNull(this.sourceConfig)) {
            RequestCheckUtils.checkNotEmpty(this.keywords, "keywords");
        } else {
            RequestCheckUtils.checkNotEmpty(this.sourceConfig.getInput(), "sourceConfig.input");
            RequestCheckUtils.checkNotEmpty(this.sourceConfig.getMatchType(), "sourceConfig.matchType");
            RequestCheckUtils.checkNotEmpty(this.sourceConfig.getOutput(), "sourceConfig.output");
            RequestCheckUtils.checkNotEmpty(this.sourceConfig.getCondition(), "sourceConfig.condition");
            RequestCheckUtils.checkNotEmpty(this.sourceConfig.getCondition().get(0).getKeywords(), "sourceConfig.condition.keywords");
        }


        if (ObjectUtil.isNull(this.scheduleConfig)) {
            RequestCheckUtils.checkNotEmpty(this.startDataTime, "startDataTime");
            RequestCheckUtils.checkMinNum(this.startDataTime, 1700000000000L, "startDataTime");
            RequestCheckUtils.checkNotEmpty(this.endDataTime, "endDataTime");
            RequestCheckUtils.checkMinNum(this.endDataTime, 1700000000000L, "startDataTime");
        } else {
            RequestCheckUtils.checkNotEmpty(this.scheduleConfig.getStartDataTime(), "scheduleConfig.startDataTime");
            RequestCheckUtils.checkNotEmpty(this.scheduleConfig.getEndDataTime(), "scheduleConfig.endDataTime");
            RequestCheckUtils.checkNotEmpty(this.scheduleConfig.getType(), "scheduleConfig.type");
            RequestCheckUtils.checkNotEmpty(this.scheduleConfig.getUnit(), "scheduleConfig.unit");
        }

        if (ObjectUtil.isNotNull(this.globalConfig)) {
            RequestCheckUtils.checkNotEmpty(this.globalConfig.getIsRealTime(), "globalConfig.isRealTime");
        }
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
