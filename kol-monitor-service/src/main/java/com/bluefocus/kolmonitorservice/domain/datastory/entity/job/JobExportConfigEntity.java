package com.bluefocus.kolmonitorservice.domain.datastory.entity.job;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.HashMap;

@Data
@EqualsAndHashCode()
public class JobExportConfigEntity implements Serializable {

    @JSONField(name = "local")
    private HashMap<String, String> local = new HashMap<>();

    @JSONField(name = "es")
    private JobExportConfigEntity.EsRTO es = new EsRTO();


    @Data
    @EqualsAndHashCode()
    public static class EsRTO {

        @JSONField(name = "cluster")
        private String cluster = "amkt_es_cluster";

        @JSONField(name = "host")
        private String host = "n1.amkt.es.hdp:9200:9300,n2.amkt.es.hdp:9200:9300";

        /**
         * ds-hermes-bluefocus-index-20240528
         */
        @JSONField(name = "indexName")
        private String indexName = "ds-hermes-bluefocus-index-";

    }
}
