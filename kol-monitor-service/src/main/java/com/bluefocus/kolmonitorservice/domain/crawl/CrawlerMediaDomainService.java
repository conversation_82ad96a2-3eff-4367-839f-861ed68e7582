package com.bluefocus.kolmonitorservice.domain.crawl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.util.HttpUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.crawl.common.CrawlerStateCode;
import com.bluefocus.kolmonitorservice.domain.crawl.req.CrawlerMediaReq;
import com.bluefocus.kolmonitorservice.domain.crawl.resp.CrawlerMediaResp;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaCrawler;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.mapper.MediaCrawlerMapper;
import org.apache.commons.lang.StringUtils;
import org.apache.http.entity.ContentType;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * @author: yjLiu
 * @date: 0003 2024/6/3 16:07
 * @description:
 */
@Service
public class CrawlerMediaDomainService {
    private final static Logger logger = LoggerFactory.getLogger(CrawlerMediaDomainService.class);

    @Value("${dataStory.clawl.serverUrls}")
    private List<String> crawlerUrls;

    @Value("${dataStory.clawl.crawlerRedisKey}")
    private String crawlerRedisKey;
    @Value("${dataStory.clawl.crawlerLoseKey}")
    private String crawlerLoseKey;

    @Resource
    MediaCrawlerMapper mediaCrawlerMapper;

    @Resource
    RedisUtils redisUtils;

    private RBlockingQueue<String> blockQueue;

    @PostConstruct
    public void init() {
        blockQueue = redisUtils.getBlockQueue(crawlerRedisKey);
        for (String crawlerUrl : crawlerUrls) {
            if (!blockQueue.contains(crawlerUrl)) {
                blockQueue.add(crawlerUrl);
            }
        }
    }

    private int getTimeout() {
        RBucket<Object> rBucket = redisUtils.getString(RedisKeyComm.MEDIA_CRAWLER_TIMEOUT_TIME);
        if (!rBucket.isExists()) {
            // 默认 120 * 1000
            rBucket.set(120 * 1000);
        }
        return Integer.parseInt(rBucket.get().toString());
    }

    public CrawlerMediaReq getCrawlerDataParam(MediaTaskHandle handle, List<String> sourceNames) {
        CrawlerMediaReq crawlerMediaReq = new CrawlerMediaReq();
        crawlerMediaReq.setSources(sourceNames);
        crawlerMediaReq.setStartTime(handle.getStartTime());
        crawlerMediaReq.setEndTime(handle.getEndTime());

        List<String> filterWords = JSON.parseArray(handle.getFilterword(), String.class);
        StringBuilder filterWordBuilder = new StringBuilder();
        for (int i = 0; i < filterWords.size(); i++) {
            filterWordBuilder.append(filterWords.get(i));
            if (i < filterWords.size() - 1) {
                filterWordBuilder.append("|");
            }
        }
        crawlerMediaReq.setFilterWord(filterWordBuilder.toString());

        List<String> keywords = JSON.parseArray(handle.getKeyword(), String.class);
        StringBuilder keywordBuilder = new StringBuilder();
        for (int i = 0; i < keywords.size(); i++) {
            keywordBuilder.append(keywords.get(i));
            if (i < keywords.size() - 1) {
                keywordBuilder.append("|");
            }
        }
        crawlerMediaReq.setKeyword(keywordBuilder.toString());
        return crawlerMediaReq;
    }

    @SuppressWarnings("Duplicates")
    public CrawlerMediaResp getCrawlerDataStory(CrawlerMediaReq crawlerMediaReq, Long handleId) {

        String crawlerUrl = null;
        RLock lock = null;
        MD5 md5 = MD5.create();
        Random random = new Random();
        try {
            // 循环30次 每次休眠1s 超过30s获取爬虫URL失败 则返回空
            int lockTimes = 30;
            while (lockTimes >= 0) {
                //添加一个随机数 防止多线程同时抢占
                int randomInt = random.nextInt(1000) + 1;
                Thread.sleep(randomInt);
                crawlerUrl = blockQueue.poll();
                // 判断当前URL是否被锁
                lock = redisUtils.getLock(crawlerRedisKey + ":" + md5.digestHex(crawlerUrl, CharsetUtil.UTF_8));

                if (!lock.isLocked() && !checkLoseUrl(crawlerUrl)) {
                    // URL未被使用则锁定该URL
                    lock.lock(1, TimeUnit.MINUTES);
                    break;
                } else {
                    // 未获得锁 初始化 lock
                    lock = null;
                    if (null != crawlerUrl && !blockQueue.contains(crawlerUrl)) {
                        blockQueue.add(crawlerUrl);
                    }
                }
                // 获得锁失败 则等待1s 继续循环
                crawlerUrl = null;
                Thread.sleep(1000L);
                lockTimes--;
            }
            if (StringUtils.isEmpty(crawlerUrl)) {
                return null;
            }
            // 获取数据
            String res = requestCrawler(crawlerUrl, crawlerMediaReq, handleId, getTimeout());
            if (StringUtils.isEmpty(res)) {
                return null;
            }

            CrawlerMediaResp crawlerMediaResp = JSON.parseObject(res, CrawlerMediaResp.class);

            // 判断数据异常 CrawlerStateCode
            if (StringUtils.isNotEmpty(crawlerMediaResp.getStateCode())) {
                if (crawlerMediaResp.getStateCode().equals(CrawlerStateCode.LOGIN_ERR)) {
                    // 登录失败，间隔5Min再请求此url
                    if (!checkLoseUrl(crawlerUrl)) {
                        addLoseUrl(crawlerUrl);
                    }
                }
                crawlerMediaResp.setReqUrl(crawlerUrl);
                return crawlerMediaResp;
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("调用爬虫数说聚合请求异常失败", e);
            return null;
        } finally {
            if (lock != null) {
                lock.unlock();
            }
            if (StringUtils.isNotEmpty(crawlerUrl)) {
                if (!blockQueue.contains(crawlerUrl)) {
                    blockQueue.add(crawlerUrl);
                }
                // 使用哈希表存储 URL 计数
                RMap<String, Integer> urlCountMap = redisUtils.getMap(RedisKeyComm.DATA_STORY_CRAWLER_REDIS_COUNT_ + LocalDate.now());
                urlCountMap.putIfAbsent(crawlerUrl, 0);
                urlCountMap.addAndGet(crawlerUrl, 1);
            }
        }
    }

    private String requestCrawler(String crawlerUrl, CrawlerMediaReq crawlerMediaReq, Long handleId, int timeout) {
        long start = System.currentTimeMillis();
        String res = HttpUtil.sendPost(crawlerUrl, JSON.toJSONString(crawlerMediaReq), ContentType.APPLICATION_JSON, timeout);

        MediaCrawler mediaCrawler = new MediaCrawler();
        mediaCrawler.setHandleId(handleId);
        mediaCrawler.setReqParam(JSON.toJSONString(crawlerMediaReq));
        mediaCrawler.setCreateTime(LocalDateTime.now());
        mediaCrawler.setRespParam(res);
        mediaCrawler.setUrl(crawlerUrl);
        mediaCrawler.setReqTime(System.currentTimeMillis() - start);
        mediaCrawlerMapper.insert(mediaCrawler);

        return res;
    }

    /**
     * 登录失效爬虫
     */
    private void addLoseUrl(String crawlerUrl) {
        MD5 md5 = MD5.create();
        RBucket<Object> loseKey = redisUtils.getString(String.format(crawlerLoseKey, md5.digestHex(crawlerUrl, CharsetUtil.UTF_8)));
        if (!loseKey.isExists()) {
            loseKey.set(1, 5, TimeUnit.MINUTES);
        }
    }

    private boolean checkLoseUrl(String crawlerUrl) {
        MD5 md5 = MD5.create();
        RBucket<Object> loseKey = redisUtils.getString(String.format(crawlerLoseKey, md5.digestHex(crawlerUrl, CharsetUtil.UTF_8)));
        return loseKey.isExists();
    }
}
