package com.bluefocus.kolmonitorservice.domain.wordart.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@EqualsAndHashCode
public class WordArtWordEntity implements Serializable {

    private String name;
    private Long size;
    private Integer angle;
    private String font;
    private String color;

    public WordArtWordEntity(String name, Long size) {
        this.name = name;
        this.size = size;
    }

}
