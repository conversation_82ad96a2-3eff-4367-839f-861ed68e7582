package com.bluefocus.kolmonitorservice.domain.article.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.bluefocus.kolmonitorinterface.dto.res.PageArticleDetail;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * Mapper接口 由模板自动生成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08 18:08:16
 */
@Mapper
public interface ArticleDetailMapper extends BaseMapper<ArticleDetail> {

    @Select("select " +
            "COUNT(DISTINCT kol_media_id) totalKolNum " +
            ",COUNT(article_url) totalUrlNum " +
            ",SUM(exposure) totalExposure " +
            ",SUM(read_num) totalReadNum " +
            ",SUM(like_num) totalLikeNum " +
            ",SUM(comment_num) totalCommentNum " +
            ",SUM(collection_num) totalCollectionNum " +
            ",SUM(interaction_num) totalInteractionNum " +
            ",SUM(share_num) totalShareNum " +
            ",SUM(follow_num) totalFollowNum " +
            ",SUM(bullet_num) totalBulletNum " +
            ",SUM(coin_num) totalCoinNum " +
            "FROM article" + " ${ew.customSqlSegment} ")
    PageArticleDetail findTotalData(@Param(Constants.WRAPPER) QueryWrapper<Article> queryWrapper);
}