package com.bluefocus.kolmonitorservice.domain.project;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorservice.base.enums.DeleteStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.ProjectStatusEnum;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import com.bluefocus.kolmonitorservice.domain.project.repository.ProjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Descirption
 * @date 2023/3/7 17:09
 */
@Service
@RequiredArgsConstructor
public class ProjectDomainService extends ServiceImpl<ProjectMapper, Project> {
    public Project saveProject(String projectName, Long userId, Integer frequency, String monitorTime, String plats) {
        Project project = new Project();
        project.setName(projectName);
        project.setStatus(ProjectStatusEnum.MONITORING.getIndex());
        project.setDeleteStatus(DeleteStatusEnum.UNDELETE.getCode());
        project.setCreateTime(LocalDateTime.now());
        project.setMonitorPeriod(monitorTime);
        project.setPeriodTime(frequency);
        project.setMonitorPlat(plats);
        project.setOperatorId(userId);
        save(project);
        return project;
    }

    public List<Project> selectByName(String projectName, Long userId) {
        LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Project::getOperatorId, userId);
        queryWrapper.eq(Project::getName, projectName);
        queryWrapper.eq(Project::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode());
        return list(queryWrapper);
    }

    public IPage<Project> findProjectList(Long userId, Integer page, Integer limit) {
        LambdaQueryWrapper<Project> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotEmpty(userId), Project::getOperatorId, userId)
                .eq(Project::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .orderByDesc(Project::getCreateTime);
        return getBaseMapper().selectPage(new Page<>(page, limit), queryWrapper);
    }

    public void deleteProjectById(Long id, Long userId) {
        LambdaUpdateWrapper<Project> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(Project::getId, id)
                .set(Project::getOperatorId, userId)
                .set(Project::getDeleteStatus, DeleteStatusEnum.DELETE.getCode())
                .set(Project::getUpdateTime, LocalDateTime.now());
        getBaseMapper().update(null, lambdaUpdateWrapper);
    }

    public void updatePlat(Project project) {
        getBaseMapper().updateById(project);
    }

    public List<Project> findUnFinish() {
        return this.lambdaQuery().eq(Project::getStatus, ProjectStatusEnum.MONITORING.getIndex()).list();
    }
}
