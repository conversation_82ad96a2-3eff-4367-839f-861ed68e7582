package com.bluefocus.kolmonitorservice.domain.article.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bluefocus.kolmonitorservice.domain.article.entity.KolVideoCloud;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【kol_video_cloud(kol云图视频表)】的数据库操作Mapper
 * @createDate 2025-03-21 10:33:52
 * @Entity generator.domain.KolVideoCloud
 */
public interface KolVideoCloudMapper extends BaseMapper<KolVideoCloud> {

    @Insert("<script>" +
            "INSERT INTO `kol_video_cloud` " +
            "(`video_id`, `kol_dy_id`, `search_after_rate`, `search_after_pv`, `search_after_uv`, " +
            "`search_after_view_rate`, `search_after_view_pv`, `search_after_view_uv`, `add_rate_a3`, " +
            "`plugin_type`, `plugin_click_rate`, `plugin_show_num`, `plugin_click_num`, `burst_qul_num`, " +
            "`burst_all_num`, `state`, `create_time`) " +
            "VALUES " +
            "<foreach collection='list' item='item' index='index' separator=','>" +
            "(#{item.videoId}, #{item.kolDyId}, #{item.searchAfterRate}, #{item.searchAfterPv}, #{item.searchAfterUv}, " +
            "#{item.searchAfterViewRate}, #{item.searchAfterViewPv}, #{item.searchAfterViewUv}, #{item.addRateA3}, " +
            "#{item.pluginType}, #{item.pluginClickRate}, #{item.pluginShowNum}, #{item.pluginClickNum}, #{item.burstQulNum}, " +
            "#{item.burstAllNum}, #{item.state}, #{item.createTime})" +
            "</foreach>" +
            "</script>")
    void insertBatch(@Param("list") List<KolVideoCloud> kolVideos);
}




