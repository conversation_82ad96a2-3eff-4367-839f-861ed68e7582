package com.bluefocus.kolmonitorservice.domain.gptalk.request;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.gptalk.DefaultGPTalkRequest;
import com.bluefocus.kolmonitorservice.domain.gptalk.entity.ChatMessage;
import com.bluefocus.kolmonitorservice.domain.gptalk.response.GPTalkChatMessageResponse;
import lombok.Setter;

import java.util.List;


@Setter
public class GPTalkChatMessageRequest extends DefaultGPTalkRequest<GPTalkChatMessageResponse> {

    @JSONField(name = "temperature")
    protected Double temperature = 0.9D;

    @JSONField(name = "top_p")
    protected Double topP = 1.0D;

    @JSONField(name = "frequency_penalty")
    protected Double frequencyPenalty = 0.8D;

    @JSONField(name = "presence_penalty")
    protected Double presencePenalty = 0D;

    @JSONField(name = "max_tokens")
    protected Integer maxTokens = 4096;

    @JSONField(name = "stop")
    protected List<String> stop = null;

    @JSONField(name = "messages")
    private List<ChatMessage> messages;

    @Override
    public String getApiMethodName() {
        return "gpt-4/chat/completions?api-version=2024-02-15-preview";
    }

    @Override
    public String getParams() {
        JSONObject params = new JSONObject();
        params.put("temperature", this.temperature);
        params.put("top_p", this.topP);
        params.put("frequency_penalty", this.frequencyPenalty);
        params.put("presence_penalty", this.presencePenalty);
        params.put("max_tokens", this.maxTokens);
        params.put("stop", this.stop);
        params.put("messages", this.messages);
        return params.toJSONString();
    }

    @Override
    public Class<GPTalkChatMessageResponse> getResponseClass() {
        return GPTalkChatMessageResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.messages, "messages");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }
}
