package com.bluefocus.kolmonitorservice.domain.route.platform.datastory.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数说-返回
 * <AUTHOR>
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DsJobDefaultResponse extends Response {

    @JSONField(name = "data")
    private Object data;

}
