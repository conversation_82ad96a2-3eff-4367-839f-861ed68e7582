package com.bluefocus.kolmonitorservice.domain.route;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Locale;
import java.util.TreeMap;

/**
 * @author: yjLiu
 * @date: 0003 2024/8/3 16:42
 * @description: 路由转发-默认执行端
 */
@Slf4j
@Getter
@Service
public class DefaultRouteClient implements Client {

    protected int connectTimeout = 600000;
    protected int readTimeout = 600000;

    @Setter
    protected boolean needCheckRequest = true;

    public DefaultRouteClient() {
    }

    public DefaultRouteClient(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {

        W tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }

        String contentType = request.getHeaderMap().get("Content-Type");
        String query = null;
        String params = request.getParams();
        String method = request.getMethod().toLowerCase(Locale.ROOT);
        String url = request.getApi() + request.getApiMethodName();
        TreeMap<String, Object> treeMap = null;
        if (ContentType.JSON.getValue().equals(contentType)) {
            query = params;
        } else if (ContentType.FORM_URLENCODED.getValue().equals(contentType)) {
            treeMap = new TreeMap<>();
            if (StringUtils.isNotEmpty(params)) {
                Type type = new TypeToken<TreeMap<String, String>>() {
                }.getType();
                treeMap.putAll(JSON.parseObject(params, type));
            }
            if ("get".equals(method)) {
                query = UrlQuery.of(treeMap).build(StandardCharsets.UTF_8);
                if (!url.contains("?")) {
                    url = url + "?" + query;
                } else {
                    url = url + query;
                }
            }
        }

        String body = null;
        try {
            HttpRequest httpRequest = null;
            if (ContentType.FORM_URLENCODED.getValue().equals(contentType) && "post".equals(method)) {
                body = com.bluefocus.kolmonitorservice.base.util.HttpUtil.sendPostHeadForm(url
                        , treeMap, request.getHeaderMap(), this.connectTimeout);
            } else {
                if ("get".equals(method)) {
                    httpRequest = HttpUtil.createGet(url);
                } else {
                    HttpRequest post = HttpUtil.createPost(url);
                    httpRequest = post.body(query);
                }
                HttpResponse httpResponse = httpRequest.setConnectionTimeout(this.connectTimeout)
                        .setReadTimeout(this.readTimeout)
                        .headerMap(request.getHeaderMap(), true)
                        .execute();
                body = httpResponse.body();
            }

            if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
                tRsp = JSON.parseObject(body, request.getResponseClass());
            } else {
                tRsp = request.getResponseClass().newInstance();
            }
            tRsp.setBody(body);
            tRsp.setRequestUrl(url);
//            tRsp.setHeaderContent(httpResponse.headers());
//            String body = "{\"code\":\"0\",\"data\":8142253,\"success\":true}";

            log.info("请求三方任务接口结束，url=【{}】,请求参数【{}】,响应提示：【{}】", url, query, tRsp.getMsg());
        } catch (Exception e) {
            throw new ApiException(e);
        }
        return tRsp;
    }


//    public static void main(String[] args) {
//        String body = "{\"code\":\"0\",\"data\":8142253,\"success\":true}";
//
//        Type type1 = new TypeToken<TreeMap<String, Object>>() {
//        }.getType();
//
//        Type type2 = new com.google.gson.reflect.TypeToken<TreeMap<String, Object>>() {
//        }.getType();
//        // 无指定类型时，数字有 int 和 BigDecimal 多类型
//        TreeMap<String, Object> map1 = JSON.parseObject(body, type2);
//        // 无指定类型时，数字默认只有doblue类型
//        TreeMap<String, Object> gson1 = new Gson().fromJson(body, type1);
//
//
//        TreeMap<String, Object> map2 = JSON.parseObject(body, type2);
//        TreeMap<String, Object> gson2 = new Gson().fromJson(body, type2);
//        System.out.println(map1);
//        System.out.println(map2);
//    }
}
