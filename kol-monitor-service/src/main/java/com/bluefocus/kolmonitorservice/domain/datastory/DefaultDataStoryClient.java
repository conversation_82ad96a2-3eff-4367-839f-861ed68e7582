package com.bluefocus.kolmonitorservice.domain.datastory;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.application.service.media.impl.IMediaStart;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RateIntervalUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 数说-构造器
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@Getter
@Service
public class DefaultDataStoryClient implements Client {

    @Value("${dataStory.api.serverUrl}")
    protected String serverUrl;
    @Resource
    RedisUtils redisUtils;

    /**
     * 默认连接超时时间为15秒
     */
    protected int connectTimeout = 120000;
    /**
     * 默认响应超时时间为30秒
     */
    protected int readTimeout = 120000;

    protected boolean limit(String token) {
        return redisUtils.getRateLimiter(IMediaStart.DATASTORY_API_LIMITER + token, 30, 5, RateIntervalUnit.SECONDS).tryAcquire();
    }

    @Setter
    protected boolean needCheckRequest = true;

    public DefaultDataStoryClient(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public DefaultDataStoryClient(String serverUrl, int connectTimeout, int readTimeout) {
        this(serverUrl);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {
        String token = DataStoryTokenUtil.getToken();
//        int i = 30;
//        while (i > 0) {
//            i--;
//            if (limit(token)) {
//                break;
//            } else {
//                try {
//                    Thread.sleep(500);
//                } catch (InterruptedException ignored) {
//                }
//            }
//        }
        W tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }

        JSONObject paramObject = new JSONObject();
        paramObject.put("token", token);
        String params = request.getParams();
        if (params != null && !params.isEmpty()) {
            paramObject.putAll(JSON.parseObject(params));
        }
        String query = paramObject.toJSONString();
        try {
            HttpResponse httpResponse = HttpUtil.createPost(this.serverUrl + request.getApiMethodName())
                    .setConnectionTimeout(this.connectTimeout)
                    .setReadTimeout(this.readTimeout)
                    .headerMap(request.getHeaderMap(), true).body(query).execute();
            String body = httpResponse.body();
            if (StringUtils.isNotEmpty(body) && JSONUtil.isJson(body)) {
                tRsp = JSON.parseObject(body, request.getResponseClass());
            } else {
                tRsp = request.getResponseClass().newInstance();
            }
            tRsp.setBody(body);
            tRsp.setRequestUrl(this.serverUrl + request.getApiMethodName());
            tRsp.setHeaderContent(httpResponse.headers());
        } catch (Exception e) {
            try {
                tRsp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            tRsp.setCode("-1");
            tRsp.setMsg(e.getMessage());
            return tRsp;
        }
        return tRsp;
    }
}
