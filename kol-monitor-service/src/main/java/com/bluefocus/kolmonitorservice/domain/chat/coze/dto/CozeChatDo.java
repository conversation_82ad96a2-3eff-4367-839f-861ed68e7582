package com.bluefocus.kolmonitorservice.domain.chat.coze.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.domain.chat.coze.req.AdditionalMessage;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: yj<PERSON><PERSON>
 * @date: 0021 2024/10/21 16:07
 * @description:
 */
@Data
public class CozeChatDo {

    @JSONField(name = "bot_id")
    private String botId;
    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "additional_messages")
    private List<AdditionalMessage> additionalMessages;

    @JSONField(name = "stream")
    private boolean stream = false;

    @JSONField(name = "auto_save_history")
    private boolean autoAaveHistory = true;

    @JSONField(name = "custom_variables")
    private Map<String, String> customVariables;

    @JSONField(name = "meta_data")
    private Map<String, Object> metaData;

    @JSONField(name = "extra_params")
    private List<String> extraParams;
}
