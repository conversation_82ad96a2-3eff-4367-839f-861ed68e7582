package com.bluefocus.kolmonitorservice.domain.datastory.entity.job;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@NoArgsConstructor
@Data
@EqualsAndHashCode()
public class JobDetailProFiltersListStrEntity implements Serializable {

    @JSONField(name = "id")
    private Long id;

    @JSONField(name = "name")
    private String name;

    @J<PERSON>NField(name = "keywords")
    private String keywords;

    @JSONField(name = "jobId")
    private Long jobId;

    @JSONField(name = "createdTime", format = "MMM d, yyyy h:mm:ss a")
    private Date createdTime;

    @JSONField(name = "updatedTime", format = "MMM d, yyyy h:mm:ss a")
    private Date updatedTime;

    @JSONField(name = "needPic")
    private Boolean needPic;
}
