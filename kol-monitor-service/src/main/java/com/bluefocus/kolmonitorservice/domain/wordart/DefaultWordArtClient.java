package com.bluefocus.kolmonitorservice.domain.wordart;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpStatus;
import cn.hutool.http.HttpUtil;
import com.bluefocus.kolmonitorservice.base.common.TimeOutException;
import com.bluefocus.kolmonitorservice.base.util.ImageUtils;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.client.ClientCommon;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Getter;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

/**
 * word-art-构造器
 * <AUTHOR>
 */
@Getter
@Service
public class DefaultWordArtClient implements Client {
    private final static Logger logger = LoggerFactory.getLogger(DefaultWordArtClient.class);

    @Value("${word-art.serverUrl}")
    protected String serverUrl;

    @Value("${word-art.key}")
    protected String apiKey;

    /**
     * 默认连接超时时间为15秒
     */
    protected int connectTimeout = 15000;

    /**
     * 默认响应超时时间为30秒
     */
    protected int readTimeout = 20000;
    protected int timeout = 30000;

    /**
     * 是否在客户端校验请求
     */
    @Setter
    protected boolean needCheckRequest = true;

    public DefaultWordArtClient() {
    }

    public DefaultWordArtClient(String serverUrl, String apiKey) {
        this.serverUrl = serverUrl;
        this.apiKey = apiKey;
    }

    public DefaultWordArtClient(String serverUrl, String apiKey, int connectTimeout, int readTimeout) {
        this(serverUrl, apiKey);
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    @Override
    public <V extends Request<W>, W extends Response> W doExecute(V request) throws ApiException {
        W tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }
        String data = request.getParams();

        TreeMap<String, String> treeMap = new TreeMap<>();
        treeMap.put("key", this.apiKey);
        treeMap.put("data", data);
        UrlQuery urlQuery = UrlQuery.of(treeMap);
        Map<String, String> headerMap = request.getHeaderMap();

        String body;
        HttpResponse httpResponse;
        try {
            Long s = System.currentTimeMillis();
            httpResponse = HttpUtil.createPost(this.serverUrl + request.getApiMethodName())
                    .setConnectionTimeout(this.connectTimeout)
                    .setReadTimeout(this.readTimeout)
                    .headerMap(headerMap, true)
                    .body(urlQuery.build(StandardCharsets.UTF_8))
                    .execute();

            tRsp = request.getResponseClass().newInstance();
            if (httpResponse.isOk()) {
                body = ImageUtils.imageToBase64(httpResponse.bodyStream());
            } else {
                body = httpResponse.body();
            }
            if (httpResponse.getStatus() == HttpStatus.HTTP_CLIENT_TIMEOUT){
                throw new TimeOutException("超时异常");
            }

            tRsp.setBody(body);
            tRsp.setSuccess(httpResponse.isOk());
            tRsp.setCode("" + httpResponse.getStatus());
            tRsp.setRequestUrl(this.serverUrl + request.getApiMethodName());
            tRsp.setHeaderContent(httpResponse.headers());
            Long e = System.currentTimeMillis();
            logger.info("DefaultWordArtClient:执行耗时：{}ms",e-s);
        } catch (Exception e) {
            if (e instanceof TimeOutException){
                throw new TimeOutException("wordart超时异常");
            }
            try {
                tRsp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            tRsp.setCode("-1");
            tRsp.setMsg(e.getMessage());
            return tRsp;
        }

        return tRsp;
    }

    public <T extends Response> T executeTime(Request<T> request) throws ApiException {
        T tRsp = ClientCommon.check(this.needCheckRequest, request);
        if (tRsp != null) {
            return tRsp;
        }
        String data = request.getParams();
        TreeMap<String, String> treeMap = new TreeMap<>();
        treeMap.put("key", this.apiKey);
        treeMap.put("data", data);
        UrlQuery urlQuery = UrlQuery.of(treeMap);
        Map<String, String> headerMap = request.getHeaderMap();

        String body;
        HttpResponse httpResponse;
        try {
            Long s = System.currentTimeMillis();
//            String s1 = com.bluefocus.kolmonitorservice.base.util.HttpUtil.sendPostHheader(this.serverUrl + request.getApiMethodName()
//                    , urlQuery.build(StandardCharsets.UTF_8), ContentType.APPLICATION_JSON, headerMap, this.timeout, timeout);
            httpResponse = HttpUtil.createPost(this.serverUrl + request.getApiMethodName())
                    .setConnectionTimeout(this.connectTimeout)
                    .setReadTimeout(this.readTimeout)
                    .timeout(timeout)
                    .headerMap(headerMap, true)
                    .body(urlQuery.build(StandardCharsets.UTF_8))
                    .execute();

            tRsp = request.getResponseClass().newInstance();
            if (httpResponse.isOk()) {
                body = ImageUtils.imageToBase64(httpResponse.bodyStream());
            } else {
                body = httpResponse.body();
            }
            if (httpResponse.getStatus() == HttpStatus.HTTP_CLIENT_TIMEOUT){
                throw new TimeOutException("超时异常");
            }

            tRsp.setBody(body);
            tRsp.setSuccess(httpResponse.isOk());
            tRsp.setCode("" + httpResponse.getStatus());
            tRsp.setRequestUrl(this.serverUrl + request.getApiMethodName());
            tRsp.setHeaderContent(httpResponse.headers());
            Long e = System.currentTimeMillis();
            logger.info("DefaultWordArtClient:执行耗时：{}ms",e-s);
        } catch (Exception e) {
            if (e instanceof TimeOutException){
                throw new TimeOutException("wordart超时异常");
            }
            try {
                tRsp = request.getResponseClass().newInstance();
            } catch (Exception xe) {
                throw new ApiException(xe);
            }
            tRsp.setCode("-1");
            tRsp.setMsg(e.getMessage());
            return tRsp;
        }

        return tRsp;
    }

}
