package com.bluefocus.kolmonitorservice.domain.algorithm;

import cn.hutool.core.collection.CollectionUtil;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.algorithm.request.AlgorithmHotAnalysisRequest;
import com.bluefocus.kolmonitorservice.domain.algorithm.response.AlgorithmHotAnalysisResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class AlgorithmDomainService {

    private final DefaultAlgorithmClient client;

    public AlgorithmDomainService(@Value("${dataStory.alg.serverUrl}") String serverUrl) {
        this.client = new DefaultAlgorithmClient(serverUrl);
    }

    /**
     * 热点分析接口
     *
     * @return 热点分析结果
     */
    public AlgorithmHotAnalysisResponse hotAnalysis(AlgorithmHotAnalysisRequest request) throws ApiException {
        if (CollectionUtil.isEmpty(request.getNotes()) || request.getNotes().size() < 3) {
            return null;
        }
        return client.doExecute(request);

    }
}
