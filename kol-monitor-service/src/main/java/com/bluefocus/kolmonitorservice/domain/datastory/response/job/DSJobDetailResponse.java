package com.bluefocus.kolmonitorservice.domain.datastory.response.job;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数说-任务详情-返回
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DSJobDetailResponse extends Response {

    /**
     * 任务详情
     */
    @JSONField(name = "data")
    private DSJobDetailResponse.ResultDTO data;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JSONField(name = "jobId")
        private Long jobId;

        @JSONField(name = "name")
        private String name;

        @JSONField(name = "jobType")
        private String jobType;

        @JSONField(name = "sheetId")
        private Long sheetId;

        @JSONField(name = "sheetId")
        private String sheetName;

        @JSONField(name = "crawlConfig")
        private String crawlConfig;

        @JSONField(name = "etl")
        private String etl;

        @JSONField( name = "schedule")
        private String schedule;

        @JSONField(name = "searchCondition")
        private String searchCondition;

        @JSONField(name = "profiltersListStr")
        private String proFiltersListStr;

        @JSONField(name = "ftp")
        private String ftp;

        @JSONField(name = "es")
        private String es;

        @JSONField(name = "hive")
        private String hive;

        @JSONField(name = "matrix")
        private String matrix;

        @JSONField(name = "oss")
        private String oss;

        @JSONField(name = "yarnQueue")
        private String yarnQueue;

        @JSONField(name = "hdfsFile")
        private String hdfsFile;

        @JSONField(name = "hdfs")
        private String hdfs;

        @JSONField(name = "titTemplateId")
        private String titTemplateId;

        @JSONField(name = "newFtpParam")
        private String newFtpParam;

        @JSONField(name = "local")
        private List<String> local;

        @JSONField(name = "matrixAppend")
        private String matrixAppend;

        @JSONField(name = "isCommon")
        private Integer isCommon;
    }
}
