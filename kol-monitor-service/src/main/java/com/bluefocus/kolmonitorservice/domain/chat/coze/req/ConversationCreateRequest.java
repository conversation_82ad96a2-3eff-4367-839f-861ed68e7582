package com.bluefocus.kolmonitorservice.domain.chat.coze.req;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.domain.chat.coze.DefaultCozeRequest;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.ConversationResp;
import lombok.Builder;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:06
 * @description:
 */
@Builder
public class ConversationCreateRequest extends DefaultCozeRequest<ConversationResp> {

    private List<AdditionalMessage> additional_messages = null;
    private Map<String, Object> meta_data = null;
    @Setter
    private String apiName;
    @Setter
    private String apiMethod;

    @Override
    public String getApiMethodName() {
        return apiName;
    }

    @Override
    public String getMethod() {
        return apiMethod;
    }

    @Override
    public String getParams() {
        return null;
    }

    @Override
    public Class<ConversationResp> getResponseClass() {
        return ConversationResp.class;
    }

    @Override
    public void check() throws ApiRuleException {

    }
}
