package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 媒体情感表
 * @TableName media_sentiment
 */
@TableName(value ="media_sentiment")
@Data
public class MediaSentiment implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 媒体对象ID
     */
    private Long mediaObjectsId;

    /**
     * 任务处理ID
     */
    private Long handleId;

    /**
     * 总情感度
     */
    private BigDecimal totalSentiments;

    /**
     * 情感率
     */
    private String rateSentiments;

    /**
     * 情感采集状态：0-采集中 1-分析中 2-分析完成 3-失败
     */
    private Integer status;

    /**
     * 重试次数
     */
    private Integer retryCount;

    /**
     * 失败原因
     */
    private String errMsg;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}