package com.bluefocus.kolmonitorservice.domain.chat.coze.req;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.domain.chat.coze.DefaultCozeRequest;
import com.bluefocus.kolmonitorservice.domain.chat.coze.dto.CozeChatDo;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.CozeChatResp;
import lombok.*;


/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:06
 * @description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ChatCreateRequest extends DefaultCozeRequest<CozeChatResp> {

    @Setter
    @Builder.Default
    private boolean cancel = false;

    public boolean isCancelled() {
        return cancel;
    }

    @Setter
    CozeChatDo cozeChatDo;
    @Setter
    private String apiName;
    @Setter
    private String apiMethod;

    @Override
    public String getApiMethodName() {
        return apiName;
    }

    @Override
    public String getMethod() {
        return apiMethod;
    }

    @Override
    public String getParams() {
        return JSON.toJSONString(cozeChatDo);
    }

    @Override
    public Class<CozeChatResp> getResponseClass() {
        return CozeChatResp.class;
    }

    @Override
    public void check() throws ApiRuleException {

    }
}
