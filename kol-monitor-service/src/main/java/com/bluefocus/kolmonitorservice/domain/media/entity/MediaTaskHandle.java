package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bluefocus.kolmonitorservice.base.enums.FinishStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 媒体任务处理表
 * @TableName media_task_handle
 */
@TableName(value ="media_task_handle")
@Data
public class MediaTaskHandle implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * task_id
     */
    private Long taskId;

    /**
     * media_objects_id
     */
    private Long mediaObjectsId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 过滤词
     */
    private String filterword;

    /**
     * 分析开始时间
     */
    private Long startTime;

    /**
     * 分析截止时间
     */
    private Long endTime;

    /**
     * 数据源(用,分割) 1小红书 2抖音 3微博
     */
    private String sources;

    /**
     * creat、edit
     */
    private String type;

    /**
     * api重试次数
     */
    private Integer apiRetry;

    /**
     * 爬虫重试次数
     */
    private Integer cralwerRetry;

    /**
     * day重试
     */
    private Integer apiDayRetry;

    /**
     * api时间ms
     */
    private Long apiTime;

    /**
     * 爬虫时间ms
     */
    private Long cralwerTime;

    /**
     * 分析时间ms
     */
    private Long analyTime;

    /**
     * wordart时间ms
     */
    private Long artTime;

    /**
     * 当前状态 0:采集中 1:分析中,2已完成,3失败
     */
    private Integer status;

    /**
     * 失败原因
     */
    private String errMsg;

    /**
     * api失败原因
     */
    private String apiErrMsg;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 采集完成时间
     */
    private LocalDateTime cralwerFinishTime;

    /**
     * 完成时间
     */
    private LocalDateTime finishTime;

    /**
     * 0暂停，1启用
     */
    private Boolean pause;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    public boolean checkStatusFinish() {
        return this.status == FinishStatusEnum.FINISH.getCode() || this.status == FinishStatusEnum.FAIL.getCode();
    }
}