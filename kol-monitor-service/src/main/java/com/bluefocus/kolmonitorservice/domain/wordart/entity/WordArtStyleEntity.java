package com.bluefocus.kolmonitorservice.domain.wordart.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
@EqualsAndHashCode
public class WordArtStyleEntity implements Serializable {
    private List<String> colors = Arrays.asList("afafaf", "999999", "2D4FFF", "F19E1C", "00994E", "00CEC0", "8E12FF", "2D4FFF", "00CEC0", "FF682E", "3AC774", "5074FF");
    private Boolean useShapeColors = true;
    private String backgroundColor = "ffffff";
    private Double backgroundImageAlpha = 0.05;
    private Double colorEmphasis = 0.0D;
}
