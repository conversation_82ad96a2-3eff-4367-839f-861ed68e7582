package com.bluefocus.kolmonitorservice.domain.algorithm;

import cn.hutool.http.ContentType;
import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaNoteDay;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 算法
 */
public abstract class DefaultAlgorithmRequest<T extends Response> implements Request<T> {

    @Setter
    @Getter
    @JSONField(name = "date")
    protected String date;

    @Setter
    @Getter
    @JSONField(name = "keywords")
    protected List<String> keywords;

    @Setter
    @Getter
    @JSONField(name = "notes")
    protected List<MediaNoteDay> notes;

    protected Map<String, String> headerMap;

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
            this.headerMap.put("Content-Type", ContentType.JSON.getValue());
        }
        return this.headerMap;
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

    @Override
    public String getTimestamp() {
        return Times.dateTimeToStringFormat(LocalDateTime.now());
    }

}