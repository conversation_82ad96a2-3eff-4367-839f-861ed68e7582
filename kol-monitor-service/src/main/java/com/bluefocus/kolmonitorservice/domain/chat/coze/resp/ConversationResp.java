package com.bluefocus.kolmonitorservice.domain.chat.coze.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:10
 * @description:
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class ConversationResp extends Response {

    @JSONField(name = "data")
    private ConversationObject data;

    @NoArgsConstructor
    @Data
    public static class ConversationObject {
        @JSONField(name = "id")
        private String id;
        @JSONField(name = "created_at")
        private String createdAt;

        @JSONField(name = "meta_data")
        private Map<String, Object> metaData;
    }
}
