package com.bluefocus.kolmonitorservice.domain.datastory.response.job;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数说-数据超市模块-创建任务-返回
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DSMarketJobResponse extends Response {

    /**
     * 任务ID
     */
    @JSONField(name = "data")
    private Integer jobId;

}
