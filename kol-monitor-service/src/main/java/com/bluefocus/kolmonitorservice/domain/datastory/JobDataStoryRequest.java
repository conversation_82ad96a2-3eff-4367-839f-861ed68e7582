package com.bluefocus.kolmonitorservice.domain.datastory;

import cn.hutool.core.date.DateUtil;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.request.Request;
import com.bluefocus.kolmonitorservice.base.response.Response;

import java.util.HashMap;
import java.util.Map;

public abstract class JobDataStoryRequest<T extends Response> implements Request<T> {

    protected Map<String, String> headerMap; // HTTP请求头参数
    protected HashMap<String, Object> udfParams; // 自定义表单参数

    /**
     * 添加URL自定义请求参数。
     */
    public void putOtherTextParam(String key, Object value) {
        if (this.udfParams == null) {
            this.udfParams = new HashMap<>();
        }
        this.udfParams.put(key, value);
    }

    @Override
    public String getTimestamp() {
        return DateUtil.now();
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

    /**
     * 添加头部自定义请求参数。
     */
    public void putHeaderParam(String key, String value) {
        getHeaderMap().put(key, value);
    }

    public void checkDefaultValue() throws ApiRuleException {
    }

}
