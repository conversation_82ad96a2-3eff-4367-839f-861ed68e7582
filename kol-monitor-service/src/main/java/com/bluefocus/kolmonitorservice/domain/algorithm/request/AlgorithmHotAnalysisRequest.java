package com.bluefocus.kolmonitorservice.domain.algorithm.request;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.domain.algorithm.DefaultAlgorithmRequest;
import com.bluefocus.kolmonitorservice.domain.algorithm.response.AlgorithmHotAnalysisResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * 算法-热点分析接口
 */
public class AlgorithmHotAnalysisRequest extends DefaultAlgorithmRequest<AlgorithmHotAnalysisResponse> {

    @Setter
    @Getter
    @JSONField(name = "media_objects_id")
    protected Long mediaObjectsId;

    @Setter
    @Getter
    @JSONField(name = "source")
    protected String source;

    @Override
    public String getApiMethodName() {
        return null;
    }

    @Override
    public String getParams() {
        JSONObject params = new JSONObject();
        params.put("date", super.date);
        params.put("keywords", super.keywords);
        params.put("notes", super.notes);
        params.put("media_objects_id", this.mediaObjectsId);
        params.put("source", this.source);
        return params.toJSONString();
    }


    @Override
    public Class<AlgorithmHotAnalysisResponse> getResponseClass() {
        return AlgorithmHotAnalysisResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {

    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }
}
