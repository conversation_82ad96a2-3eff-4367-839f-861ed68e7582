package com.bluefocus.kolmonitorservice.domain.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 路由业务组任务
 * <AUTHOR>
 * @TableName route_task
 */
@TableName(value ="route_task")
@Data
public class RouteTask implements Serializable {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务组id
     */
    private String bizId;

    /**
     * 三方平台任务id
     */
    private String jobId;

    /**
     * 三方平台任务名称
     */
    private String jobName;

    /**
     * 三方平台子任务id
     */
    private String subJob;

    /**
     * 三方平台
     */
    private String platform;

    /**
     * 任务数据量
     */
    private Long dataCount;

    /**
     * 任务计费
     */
    private Double dataCost;

    /**
     * 0未运行，1运行中，2运行结束
     */
    private Integer state;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}