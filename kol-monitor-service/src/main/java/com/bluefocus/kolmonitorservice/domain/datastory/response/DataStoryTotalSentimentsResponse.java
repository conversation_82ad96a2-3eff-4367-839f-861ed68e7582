package com.bluefocus.kolmonitorservice.domain.datastory.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数说-总情感度统计返回
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DataStoryTotalSentimentsResponse extends Response {

    private DataStoryTotalSentimentsResponse.ResultDTO data;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JSONField(name = "total")
        private String total;
    }


}
