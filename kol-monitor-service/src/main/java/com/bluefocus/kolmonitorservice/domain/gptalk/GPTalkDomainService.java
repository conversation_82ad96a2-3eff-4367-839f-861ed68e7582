package com.bluefocus.kolmonitorservice.domain.gptalk;

import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.gptalk.request.GPTalkImageRequest;
import com.bluefocus.kolmonitorservice.domain.gptalk.response.GPTalkChatMessageResponse;
import com.bluefocus.kolmonitorservice.domain.gptalk.response.GPTalkImageResponse;
import com.bluefocus.kolmonitorservice.domain.gptalk.request.GPTalkChatMessageRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class GPTalkDomainService {

    private final DefaultGPTalkClient client;

    public GPTalkChatMessageResponse chat(GPTalkChatMessageRequest request) throws ApiException {
        return client.execute(request);
    }

//    public GPTalkImageResponse image(GPTalkImageRequest request) throws ApiException {
//        return client.execute(request);
//    }

    public GPTalkImageResponse imageUrl(GPTalkImageRequest request, String serverUrl, String key) throws ApiException {
        return client.execute(request, serverUrl, key);
    }

}
