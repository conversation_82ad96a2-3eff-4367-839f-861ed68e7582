package com.bluefocus.kolmonitorservice.domain.article.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;


/**
 * <p>
 * 博文表  由模板自动生成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08 18:08:16
 */

@Data
public class Article implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文章平台ID
     */
    @TableField(value = "article_plat_id")
    private String articlePlatId;

    /**
     * 项目ID
     */
    @TableField(value = "project_id")
    private Long projectId;

    /**
     * 博文链接
     */
    @TableField(value = "article_url")
    private String articleUrl;

    /**
     * 博文正式链接
     */
    @TableField(value = "article_formal_url")
    private String articleFormalUrl;

    /**
     * 平台类型：1.小红书，2.B站，3.快手，4.抖音
     */
    @TableField(value = "plat_type")
    private Integer platType;

    /**
     * 达人媒体id
     */
    @TableField(value = "kol_media_id")
    private String kolMediaId;

    /**
     * 达人名称
     */
    @TableField(value = "kol_name")
    private String kolName;

    /**
     * 文章标题
     */
    @TableField(value = "article_title")
    private String articleTitle;

    /**
     * 文章封面
     */
    @TableField(value = "images_cover")
    private String imagesCover;

    /**
     * 作者头像
     */
    @TableField(value = "head_url")
    private String headUrl;

    /**
     * 作者主页
     */
    @TableField(value = "home_url")
    private String homeUrl;

    /**
     * 阅读量
     */
    @TableField(value = "read_num", updateStrategy = FieldStrategy.IGNORED)
    private Integer readNum;

    /**
     * 点赞数
     */
    @TableField(value = "like_num")
    private Integer likeNum;

    /**
     * 评论数
     */
    @TableField(value = "comment_num")
    private Integer commentNum;

    /**
     * share数
     */
    @TableField(value = "share_num")
    private Integer shareNum;

    /**
     * 收藏数
     */
    @TableField(value = "collection_num")
    private Integer collectionNum;

    /**
     * 互动量
     */
    @TableField(value = "interaction_num")
    private Integer interactionNum;

    /**
     * 发布时间
     */
    @TableField(value = "post_time")
    private String postTime;

    /**
     * 曝光量
     */
    @TableField(value = "exposure")
    private Integer exposure;

    /**
     * 粉丝数
     */
    @TableField(value = "fans_num")
    private Integer fansNum;

    /**
     * 关注数
     */
    @TableField(value = "follow_num")
    private Integer followNum;

    /**
     * 互动率
     */
    @TableField(value = "interaction_rate")
    private BigDecimal interactionRate;

    /**
     * 完播率
     */
    @TableField(value = "finish_rate")
    private BigDecimal finishRate;

    /**
     * 弹幕数
     */
    @TableField(value = "bullet_num")
    private Integer bulletNum;

    /**
     * 投币数
     */
    @TableField(value = "coin_num")
    private Integer coinNum;


    /**
     * 删除状态 	0:未删除 1:已删除
     */
    @TableField(value = "delete_status")
    private Integer deleteStatus;

    /**
     * 链接状态 00:待监测,01:监测中,02:监测完成 10:链接识别异常,11:链接监测中异常,12:链接无效,13:数据获取异常
     */
    @TableField(value = "status")
    private String status;

    /**
     * 操作人
     */
    @TableField(value = "operator_id")
    private Long operatorId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 变更时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    /**
     * 数据最近一次更新时间
     */
    @TableField(value = "consume_date")
    private LocalDate consumeDate;

    /**
     * 爬虫状态描述
     */
    @TableField(value = "crawler_state_desc")
    private String crawlerStateDesc;
}


