package com.bluefocus.kolmonitorservice.domain.chat.coze;

import com.alibaba.fastjson.JSON;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorservice.base.common.RedisKeyComm;
import com.bluefocus.kolmonitorservice.base.enums.ChatMsgTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.ChatRoleEnum;
import com.bluefocus.kolmonitorservice.base.enums.ChatStatusEnum;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.chat.coze.dto.CozeChatDo;
import com.bluefocus.kolmonitorservice.domain.chat.coze.dto.CozeCompletedDo;
import com.bluefocus.kolmonitorservice.domain.chat.coze.req.*;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.*;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChat;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChatBot;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChatText;
import com.bluefocus.kolmonitorservice.domain.chat.mapper.MediaChatBotMapper;
import com.bluefocus.kolmonitorservice.domain.chat.service.MediaChatDomainService;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

/**
 * @author: yjLiu
 * @date: 0016 2024/10/16 11:16
 * @description:
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class CozeDomainService {

    private final DefaultCozeClient defaultCozeClient;
    private final MediaChatDomainService mediaChatDomainService;
    private final MediaChatBotMapper mediaChatBotMapper;
    private final RedisUtils redisUtils;
    private final FsRobotUtil fsRobotUtil;
    @Setter
    @Value("${coze.api.botid}")
    private String botId;
    @Value("${coze.api.key-path}")
    private String keyPath;
    @Value("${coze.api.app-id}")
    private String appId;
    @Value("${coze.api.key-public}")
    private String publicKey;

    /**
     * 创建会话
     */
    public ConversationResp.ConversationObject conversationCreate(Long userId) {
        ConversationResp conversationResp = null;
        try {
            ConversationCreateRequest post = ConversationCreateRequest.builder()
                    .apiName("/v1/conversation/create")
                    .apiMethod("POST")
                    .build();
            post.putHeaderParam("Authorization", this.getCozeToken());

            conversationResp = defaultCozeClient.doExecute(post);
        } catch (ApiException e) {
            log.warn("创建coze会话失败:user={},err=", userId, e);
        }
        if (null == conversationResp || !"0".equals(conversationResp.getCode())) {
            log.error("创建coze会话失败:响应内容 ={}", JSON.toJSONString(conversationResp));
            return null;
        }

        return conversationResp.getData();
    }

    /**
     * 查看会话详情
     */
    public ConversationResp.ConversationObject conversationDetail(Long conversationId) {
        ConversationResp conversationResp = null;
        try {
            ConversationCreateRequest get = ConversationCreateRequest.builder()
                    .apiName("/v1/conversation/retrieve?".concat("conversation_id=" + conversationId))
                    .apiMethod("GET")
                    .build();
            get.putHeaderParam("Authorization", this.getCozeToken());
            conversationResp = defaultCozeClient.doExecute(get);
        } catch (ApiException e) {
            log.warn("查看coze会话详情失败:conversationId={},err=", conversationId, e);
        }

        if (conversationResp == null || !"0".equals(conversationResp.getCode())) {
            log.error("查看coze会话详情失败:响应内容 ={}", JSON.toJSONString(conversationResp));
            return null;
        }

        return conversationResp.getData();
    }

    /**
     * 创建对话-流式
     */
    public Flux<CozeChatStreamResp.ChatDetail> chatStream(Long conversationId, CozeChatDo content, Long userId, String role) {
        ChatCreateRequest post = ChatCreateRequest.builder()
                .apiName("/v3/chat?".concat("conversation_id=" + conversationId))
                .apiMethod("POST")
                .cozeChatDo(content)
                .build();
        post.putHeaderParam("Authorization", this.getCozeToken());

        final String[] chatId = {null};

        return Flux.create(sink -> {
            try {
                sink.onCancel(() -> {
                    log.warn("请求已取消，conversationId={}", conversationId);
                    // 可以在这里执行一些清理操作
                    post.setCancel(true);
                    if (null != chatId[0]) {
                        mediaChatDomainService.updateChatStatus(Long.valueOf(chatId[0]), ChatStatusEnum.CHAT_CANCELED.getCode());
                        chatStop(Long.valueOf(chatId[0]), conversationId);
                    }
                    sink.complete();
                });
                defaultCozeClient.doExecute(post, response -> {

                    // created
                    if ("created".equals(response.getStatus())) {
                        if (null == chatId[0] && null != response.getId()) {
                            chatId[0] = response.getId();
                        }
                        MediaChat mediaChat = mediaChatDomainService.chatSave(conversationId, Long.valueOf(chatId[0]), content.getBotId());
                        String question = content.getAdditionalMessages().get(0).getContent();
                        mediaChatDomainService.chatMsgSave(mediaChat.getChatId()
                                , question.startsWith("user:") ? question.replace("user:", "") : question, userId
                                , Long.valueOf(content.getBotId()), role, ChatMsgTypeEnum.QUESTION.getType());
                    }

                    if (!"completed".equals(response.getStatus()) && !"follow_up".equals(response.getType()) && !"verbose".equals(response.getType())
                            && !"failed".equals(response.getStatus())) {
                        response.setRole(ChatRoleEnum.USER.getRole().equals(role) ? ChatRoleEnum.ASSISTANT.getRole() : role);
                        sink.next(response);
                    } else {
                        CozeCompletedDo result = getCompleted(conversationId, Long.valueOf(chatId[0]));
                        MediaChatText chatText = mediaChatDomainService.chatMsgSave(Long.valueOf(chatId[0]), result.getContent(), Long.valueOf(content.getBotId()), userId
                                , ChatRoleEnum.USER.getRole().equals(role) ? ChatRoleEnum.ASSISTANT.getRole() : role, ChatMsgTypeEnum.ANSWER.getType());
                        mediaChatDomainService.updateChatStatus(Long.valueOf(chatId[0]), result.getStatus());
                        if (null == response.getStatus()) {
                            response.setStatus("completed");
                        }
                        response.setChatId(chatId[0]);
                        log.info("提交对话:response={}", JSON.toJSONString(response));
                        if (response.getContent().contains("generate_answer_finish")) {
                            response.setContent("");
                        }
                        response.setRole(chatText.getRole());
                        sink.next(response);

                        Mono.delay(Duration.ofSeconds(1))
                                .doOnError(e -> {
                                    log.error("延迟完成时发生错误:conversationId={}, err=", conversationId, e);
                                    sink.error(e);
                                })
                                .then(Mono.fromRunnable(sink::complete))
                                .subscribe();
                    }

                });

            } catch (Exception e) {
                log.warn("创建对话失败:conversationId={},err=", conversationId, e);
                sink.error(e);
            }
        });
    }

    /**
     * 创建对话-非流式
     */
    public CozeChatResp.ChatObject chatCreate(Long userId, Long conversationId, CozeChatDo content) {
        CozeChatResp cozeChatResp = null;
        try {
            ChatCreateRequest post = ChatCreateRequest.builder()
                    .apiName("/v3/chat?".concat("conversation_id=" + conversationId))
                    .apiMethod("POST")
                    .cozeChatDo(content)
                    .build();
            post.putHeaderParam("Authorization", this.getCozeToken());
            cozeChatResp = defaultCozeClient.doExecute(post);
        } catch (ApiException e) {
            log.warn("创建对话失败:conversationId={},err=", conversationId, e);
        }
        if (null == cozeChatResp || !"0".equals(cozeChatResp.getCode())) {
            log.error("创建对话失败:响应内容 ={}", JSON.toJSONString(cozeChatResp));
            return null;
        }
        return cozeChatResp.getData();
    }

    /**
     * 查看对话状态
     */
    public CozeChatResp.ChatObject chatStatus(Long conversationId, Long chatId) {
        CozeChatResp cozeChatResp = null;
        try {
            ChatCreateRequest get = ChatCreateRequest.builder()
                    .apiName(String.format("/v3/chat/retrieve?conversation_id=%S&chat_id=%S", conversationId, chatId))
                    .apiMethod("GET")
                    .build();
            get.putHeaderParam("Authorization", this.getCozeToken());
            cozeChatResp = defaultCozeClient.doExecute(get);
        } catch (ApiException e) {
            log.warn("创建对话失败:conversationId={},err=", conversationId, e);
        }
        if (null == cozeChatResp || !"0".equals(cozeChatResp.getCode())) {
            log.error("创建对话失败:响应内容 ={}", JSON.toJSONString(cozeChatResp));
            return null;
        }
        return cozeChatResp.getData();
    }


    /**
     * 查看对话内容详情
     */
    public String chatDetail(Long conversationId, Long chatId) {
        CozeChatDetailResp cozeChatResp = null;
        try {
            ChatDetailRequest get = ChatDetailRequest.builder()
                    .apiName(String.format("/v3/chat/message/list?conversation_id=%S&chat_id=%S", conversationId, chatId))
                    .apiMethod("GET")
                    .build();
            get.putHeaderParam("Authorization", this.getCozeToken());
            cozeChatResp = defaultCozeClient.doExecute(get);
        } catch (ApiException e) {
            log.warn("创建对话失败:conversationId={},err=", conversationId, e);
        }
        if (null == cozeChatResp || !"0".equals(cozeChatResp.getCode())) {
            log.error("创建对话失败:响应内容 ={}", JSON.toJSONString(cozeChatResp));
            return null;
        }
        for (CozeChatDetailResp.ChatDetail datum : cozeChatResp.getData()) {
            if (ChatMsgTypeEnum.ANSWER.getType().equals(datum.getType())) {
                return datum.getContent();
            }
        }
        return null;
    }


    /**
     * 停止对话
     * 以下三种不会被停止
     * completed：会话结束。
     * failed：会话失败。
     * requires_action：会话中断。
     */
    public String chatStop(Long chatId, Long conversationId) {
        CozeChatResp cozeChatResp = null;
        try {
            ChatStopRequest post = ChatStopRequest.builder()
                    .apiName("/v3/chat/cancel")
                    .apiMethod("POST")
                    .chatId(String.valueOf(chatId))
                    .conversationId(String.valueOf(conversationId))
                    .build();
            post.putHeaderParam("Authorization", this.getCozeToken());
            cozeChatResp = defaultCozeClient.doExecute(post);
        } catch (ApiException e) {
            log.warn("取消对话失败:conversationId={},err=", conversationId, e);
        }
        if (null == cozeChatResp || !"0".equals(cozeChatResp.getCode())) {
            log.error("取消对话失败:响应内容 ={}", JSON.toJSONString(cozeChatResp));
            return null;
        }
        log.info("取消对话:{}，状态{}", chatId, cozeChatResp.getData().getStatus());
        return cozeChatResp.getData().getStatus();
    }

    public CozeChatDo contentBuild(String p, Long userId, String role) {
        Assert.notEmpty(p, "输入内容为空");
        CozeChatDo cozeChatDo = new CozeChatDo();
        cozeChatDo.setStream(false);
        cozeChatDo.setBotId(botId);
        cozeChatDo.setUserId(userId.toString());
        cozeChatDo.setAutoAaveHistory(true);

        ArrayList<AdditionalMessage> additionalMessages = new ArrayList<>();
        AdditionalMessage additionalMessage = new AdditionalMessage();
        additionalMessage.setRole(role);
        additionalMessage.setContent(p);

        additionalMessage.setContent_type("text");
        additionalMessage.setType(ChatMsgTypeEnum.QUESTION.getType());
        additionalMessages.add(additionalMessage);
        cozeChatDo.setAdditionalMessages(additionalMessages);
        return cozeChatDo;
    }

    public CozeCompletedDo getCompleted(Long conversationId, Long chatId) {
        // chatId 刷新回答/1s
        int count = 30;

        CozeChatResp.ChatObject chatObject = chatStatus(conversationId, chatId);
        String status = chatObject.getStatus();
        if (ChatStatusEnum.FAILURE.getStatus().equals(status)) {
            return CozeCompletedDo.builder()
                    .status(ChatStatusEnum.FAILURE.getCode())
                    .content(chatObject.getLastError().getMsg())
                    .build();
        }
        while (!ChatStatusEnum.FAILURE.getStatus().equals(status)
                && !ChatStatusEnum.COMPLETED.getStatus().equals(status)
                && count > 0) {
            try {
                Thread.sleep(1000L);
                count--;
                chatObject = chatStatus(conversationId, chatId);
                status = chatObject.getStatus();
                if (ChatStatusEnum.FAILURE.getStatus().equals(status)) {
                    break;
                }
            } catch (Exception e) {
                log.warn("请求状态异常：", e);
            }
        }
        if (ChatStatusEnum.COMPLETED.getStatus().equals(status)) {
            return CozeCompletedDo.builder()
                    .content(chatDetail(conversationId, chatId))
                    .status(ChatStatusEnum.COMPLETED.getCode()).build();
        }
        if (ChatStatusEnum.FAILURE.getStatus().equals(status)) {
            return CozeCompletedDo.builder()
                    .status(ChatStatusEnum.FAILURE.getCode())
                    .content(chatObject.getLastError().getMsg())
                    .build();
        }
        return CozeCompletedDo.builder().status(ChatStatusEnum.CREATED.getCode()).build();
    }

    /**
     * cozeToken
     *
     * @param botId 机器人id.
     */
    public CozeTokenResp cozeToken(String botId) {
        CozeTokenResp resp = null;
        try {
            CozeTokenRequest get = CozeTokenRequest.builder()
                    .apiName("/api/permission/oauth2/token")
                    .apiMethod("POST")
                    .build();
            get.putHeaderParam("Authorization", "Bearer " + CozeJwtUtil.createToken(appId, publicKey, keyPath));
            resp = defaultCozeClient.doExecute(get);
            log.info("coze创建token结果:resp={}", resp);
        } catch (ApiException e) {
            log.warn("创建token失败:botId={},err=", botId, e);
        }

        return resp;
    }

    public String getCozeToken() {
        RBucket<Object> tokens = redisUtils.getString(RedisKeyComm.CHAT_BOT_TOKEN + ":" + LocalDate.now());

        String token = null;
        if (tokens.isExists()) {
            token = tokens.get().toString();
        } else {
            CozeTokenResp resp = cozeToken(botId);
            if (null == resp || null != resp.getError()) {
                log.error("创建token失败:响应内容 ={}", JSON.toJSONString(resp));
                String oneCozeToken = getOneCozeToken();
                tokens.set(oneCozeToken, 360, TimeUnit.MINUTES);
                token = oneCozeToken;
            } else {
                long now = Times.toEpochMilli(LocalDateTime.now()) / 1000;
                MediaChatBot bot = new MediaChatBot();
                bot.setToken(resp.getAccessToken());
                bot.setBotId(Long.valueOf(botId));
                bot.setStatus(1);
                Integer expiresIn = resp.getExpiresIn();
                bot.setExpireTime(Times.toLocalDateTime(expiresIn * 1000));
                bot.setCreateTime(LocalDateTime.now());
                mediaChatBotMapper.insert(bot);
                tokens.set(resp.getAccessToken(), (expiresIn - now) / 60, TimeUnit.MINUTES);
                token = resp.getAccessToken();
            }
        }
        return token.startsWith("Bearer ") ? token : "Bearer " + token;
    }

    private String getOneCozeToken() {
        RBucket<Object> tokens = redisUtils.getString(RedisKeyComm.CHAT_BOT_TOKEN);

        if (tokens.isExists()) {
            return tokens.get().toString();
        } else {
            MediaChatBot bot = mediaChatBotMapper.findByBotId(botId);

            String token = bot.getToken();
            if (!bot.getToken().startsWith("Bearer ")) {
                token = "Bearer " + bot.getToken();
            }
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime expireTime = bot.getExpireTime();

            if (expireTime.isBefore(now.plusDays(2L))) {
                // 预警 然后 set 2Day
                long minutes = Math.abs(expireTime.until(now.plusDays(2L), java.time.temporal.ChronoUnit.MINUTES));
                tokens.set(token, minutes, TimeUnit.MINUTES);
                fsRobotUtil.sendRobotMsg(null, String.format("<at user_id=\"all\">所有人</at> coze token 预警: 请及时更新token，当前剩余时间：%s分钟", minutes));
            } else {
                tokens.set(token
                        , Math.abs(expireTime.until(now, java.time.temporal.ChronoUnit.MINUTES)), TimeUnit.MINUTES);
            }
            return token;
        }
    }
}
