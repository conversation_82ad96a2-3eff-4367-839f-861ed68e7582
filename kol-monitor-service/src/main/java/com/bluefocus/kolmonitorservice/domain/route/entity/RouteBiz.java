package com.bluefocus.kolmonitorservice.domain.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 路由业务组
 * @TableName route_biz
 */
@TableName(value ="route_biz")
@Data
public class RouteBiz implements Serializable {
    /**
     * 业务组id
     */
    @TableId
    private String bizId;

    /**
     * 业务组名称
     */
    private String name;

    /**
     * 介绍
     */
    private String content;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}