package com.bluefocus.kolmonitorservice.domain.chat.coze;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CozeApiException extends Exception {

    private static final long serialVersionUID = 563633108485802836L;
    private String errCode;
    private String errMsg;
    private String subErrCode;
    private String subErrMsg;

    public CozeApiException() {
        super();
    }

    public CozeApiException(String message, Throwable cause) {
        super(message, cause);
    }

    public CozeApiException(String message) {
        super(message);
    }

    public CozeApiException(Throwable cause) {
        super(cause);
    }

    public CozeApiException(String errCode, String errMsg) {
        super(errCode + ":" + errMsg);
        this.errCode = errCode;
        this.errMsg = errMsg;
    }
}
