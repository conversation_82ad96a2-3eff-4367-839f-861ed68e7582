package com.bluefocus.kolmonitorservice.domain.chat.mapper;

import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChatBot;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【media_chat_bot(bot-token)】的数据库操作Mapper
* @createDate 2024-11-01 15:07:04
* @Entity com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChatBot
*/
public interface MediaChatBotMapper extends BaseMapper<MediaChatBot> {

    @Select("select * from media_chat_bot where bot_id =#{botId} and status = 0 order by create_time desc limit 1")
    MediaChatBot findByBotId(@Param("botId") String botId);
}




