package com.bluefocus.kolmonitorservice.domain.datastory.response.job;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 数说-任务详情-返回
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DSJobCountResponse extends Response {

    /**
     * 任务详情
     */
    @JSONField(name = "data")
    private DSJobCountResponse.ResultDTO data;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JSONField(name = "total")
        private Integer total;

    }
}
