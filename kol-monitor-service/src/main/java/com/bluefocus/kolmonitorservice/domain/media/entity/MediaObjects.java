package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 媒体任务表
 * @TableName media_objects
 */
@TableName(value ="media_objects")
@Data
public class MediaObjects implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 媒体任务ID
     */
    private Long mediaTaskId;

    /**
     * 对象名称
     */
    private String name;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 过滤词
     */
    private String filterword;

    /**
     * 且关键词
     */
    private String andKeywords;

    /**
     * 或关键词
     */
    private String orKeywords;

    /**
     * 分析开始时间
     */
    private Long startTime;

    /**
     * 分析截止时间
     */
    private Long endTime;

    /**
     * 数据源(用,分割) 1小红书 2抖音
     */
    private String sources;

    /**
     * 总声量
     */
    private Long totalVolume;

    /**
     * 声量占比
     */
    private String rateVolume;

    /**
     * 总互动量
     */
    private Long totalInteraction;

    /**
     * 互动量占比
     */
    private String rateInteraction;

    /**
     * 总情感量
     */
    private BigDecimal totalSentiments;

    /**
     * 情感占比
     */
    private String rateSentiments;

    /**
     * 轮廓图
     */
    private String img;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 完成时间 只有在数据爬完后设置一次！
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private LocalDateTime finishTime;

    /**
     * 分析对象状态：10采集中 11采集成功 12 分析完成 13 失败 20编辑中 21编辑采集成功 22编辑分析完成 23编辑失败
     */
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}