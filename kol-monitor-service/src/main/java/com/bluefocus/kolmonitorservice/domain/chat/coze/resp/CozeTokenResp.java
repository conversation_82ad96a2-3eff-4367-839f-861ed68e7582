package com.bluefocus.kolmonitorservice.domain.chat.coze.resp;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:10
 * @description:
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class CozeTokenResp extends Response {

    @J<PERSON>NField(name = "access_token")
    private String accessToken;
    @J<PERSON>NField(name = "expires_in")
    private Integer expiresIn;
    @JSONField(name = "token_type")
    private String tokenType;

    @JSONField(name = "error")
    private String error;
    @JSONField(name = "error_code")
    private String errorCode;
    @JSONField(name = "error_message")
    private String errorMessage;
}
