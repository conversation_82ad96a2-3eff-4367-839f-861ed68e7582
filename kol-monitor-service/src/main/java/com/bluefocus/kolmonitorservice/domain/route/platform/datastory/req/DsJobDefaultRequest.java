package com.bluefocus.kolmonitorservice.domain.route.platform.datastory.req;

import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.domain.route.RouteDefaultRequest;
import com.bluefocus.kolmonitorservice.domain.route.platform.datastory.resp.DsJobDefaultResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0003 2024/8/3 16:52
 * @description:
 */
public class DsJobDefaultRequest extends RouteDefaultRequest<DsJobDefaultResponse> {

    JSONObject body;
    String contentType;
    String auth;

    public DsJobDefaultRequest(String method, String api, String contentType, String auth, JSONObject body) {
        this.method = method;
        this.api = api;
        this.contentType = contentType;
        this.auth = auth;
        this.body = body;
    }

    @Override
    public String getParams() {
        return body.toJSONString();
    }

    @Override
    public Class<DsJobDefaultResponse> getResponseClass() {
        return DsJobDefaultResponse.class;
    }

    /**
     * 添加头部自定义请求参数。
     */
    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>(4);
        }
        if (this.contentType == null) {
            this.headerMap.put("Content-Type", ContentType.FORM_URLENCODED.getValue());
        } else {
            this.headerMap.put("Content-Type", contentType);
        }
        if (auth != null) {
            this.headerMap.put("Authorization", auth);
        }
        return this.headerMap;
    }

    @Override
    public String getMethod() {
        return method;
    }

    @Override
    public String getApi() {
        return api;
    }

}
