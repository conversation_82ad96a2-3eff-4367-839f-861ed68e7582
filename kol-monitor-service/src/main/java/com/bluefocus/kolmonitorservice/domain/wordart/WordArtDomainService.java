package com.bluefocus.kolmonitorservice.domain.wordart;

import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordArtGenerateRequest;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordCloudGenRequest;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordArtGenerateResponse;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordCloudResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class WordArtDomainService {

    private final DefaultWordArtClient wordArtClient;
    private final DefaultWordCloudClient wordCloudClient;

    public WordArtGenerateResponse generate(WordArtGenerateRequest request) throws ApiException {
        return wordArtClient.doExecute(request);
    }

    public WordArtGenerateResponse generateTime(WordArtGenerateRequest request) throws ApiException {
        return wordArtClient.executeTime(request);
    }

    public WordCloudResponse executeWordCloud(WordCloudGenRequest request) throws ApiException {
        return wordCloudClient.doExecute(request);
    }
}
