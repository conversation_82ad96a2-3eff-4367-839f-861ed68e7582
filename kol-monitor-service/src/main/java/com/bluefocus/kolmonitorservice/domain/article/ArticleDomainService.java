package com.bluefocus.kolmonitorservice.domain.article;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bluefocus.kolmonitorinterface.dto.res.PageArticleDetail;
import com.bluefocus.kolmonitorservice.application.service.plat.PlatResult;
import com.bluefocus.kolmonitorservice.base.enums.DeleteStatusEnum;
import com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum;
import com.bluefocus.kolmonitorservice.domain.article.entity.Article;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleDetail;
import com.bluefocus.kolmonitorservice.domain.article.repository.ArticleDetailMapper;
import com.bluefocus.kolmonitorservice.domain.article.repository.ArticleMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.bluefocus.kolmonitorservice.base.enums.MonitorStatusEnum.*;

/**
 * <p>
 * 博文表  服务实现类  由模板自动生成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08 18:08:16
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class ArticleDomainService extends ServiceImpl<ArticleMapper, Article> {

    private final ArticleDetailMapper articleDetailMapper;

    /**
     * 按照条件分页查询
     *
     * @param projectId 项目id
     */
    public IPage<Article> page(Long projectId, Integer platform, int pageNo, int pageSize) {

        IPage<Article> page = new Page<>();
        page.setCurrent(pageNo);
        page.setSize(pageSize);
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getProjectId, projectId);
        queryWrapper.eq(Article::getPlatType, platform);
        queryWrapper.eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode());
        queryWrapper.in(Article::getStatus, MonitorStatusEnum.MONITORING.getCode(), MonitorStatusEnum.MONITOR_FINISH.getCode());
        return this.page(page, queryWrapper);
    }

    public List<Article> listByPlatform(Long projectId, Integer platform) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getProjectId, projectId);
        queryWrapper.eq(Article::getPlatType, platform);
        queryWrapper.eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode());
        queryWrapper.in(Article::getStatus, MonitorStatusEnum.MONITORING.getCode(), MonitorStatusEnum.MONITOR_FINISH.getCode());
        return this.list(queryWrapper);
    }

    public boolean update(Article entity) {
        if (entity == null || entity.getId() == null) {
            return false;
        }
        return this.updateById(entity);
    }

    public Map<String, String> mapUrl(Long projectId, Long userId) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getOperatorId, userId)
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .select(Article::getArticleUrl, Article::getArticleFormalUrl);
        if (projectId == null) {
            queryWrapper.isNull(Article::getProjectId);
        } else {
            queryWrapper.eq(Article::getProjectId, projectId);
        }
        return this.list(queryWrapper).stream().collect(Collectors.toMap(Article::getArticleUrl
                , a -> ObjectUtil.defaultIfNull(a.getArticleFormalUrl(), "")
                , (existing, replacement) -> existing));
    }

    public ArticleDetail findDetailDataById(Long id) {
        return articleDetailMapper.selectById(id);
    }

    public Article saveArticleUrl(Long projectId, PlatResult platResult, Long userId) {
        Article article = getArticle(platResult);
        article.setCreateTime(LocalDateTime.now());
        article.setProjectId(projectId);
        article.setOperatorId(userId);
        this.save(article);
        return article;
    }

    public int saveBatchArticleUrl(Long projectId, Long userId, List<PlatResult> articleList) {
        List<Article> articles = new ArrayList<>();
        try {
            Map<String, String> mapUrl = mapUrl(projectId, userId);
            Set<String> urlSet = new HashSet<>(mapUrl.keySet());
            Set<String> urValues = new HashSet<>(mapUrl.values());

            for (PlatResult result : articleList) {
                Article article = getArticle(result);
                article.setCreateTime(LocalDateTime.now());
                article.setProjectId(projectId);
                article.setOperatorId(userId);
                if (article.getStatus().equals(PRE_MONITOR.getCode())) {
                    // 短链 正式 都校验
                    article.setStatus(urlSet.add(result.getUrl()) && urValues.add(result.getLongUrl()) ?
                            article.getStatus() : MONITOR_URL_REPEAT.getCode());
                }
                articles.add(article);
            }
        } catch (Exception e) {
            log.warn("批量保存文章失败, articleList =[{}],ERR=", JSON.toJSONString(articleList), e);
            throw e;
        }
        this.saveBatch(articles);
        return articles.size();
    }

    private Article getArticle(PlatResult platResult) {

        Article article = new Article();
        article.setArticleUrl(platResult.getUrl());
        article.setArticleFormalUrl(platResult.getLongUrl());
        article.setStatus(platResult.getStatus().getCode());
        article.setDeleteStatus(DeleteStatusEnum.UNDELETE.getCode());
        article.setPlatType(platResult.getType());

        return article;
    }


    public Article updateArticleUrl(Long articleId, PlatResult platResult) {
        if (ObjectUtil.isNull(articleId)) {
            return null;
        }
        Article article = getById(articleId);
        article.setArticleUrl(platResult.getUrl());
        article.setArticleFormalUrl(platResult.getLongUrl());
        article.setPlatType(platResult.getType());
        article.setStatus(platResult.getStatus().getCode());
        article.setDeleteStatus(DeleteStatusEnum.UNDELETE.getCode());
        article.setUpdateTime(LocalDateTime.now());

        this.update(article);
        return article;
    }

    public void delete(Long id) {
        this.lambdaUpdate().set(Article::getDeleteStatus, DeleteStatusEnum.DELETE.getCode())
                .set(Article::getUpdateTime, LocalDateTime.now())
                .eq(Article::getId, id).update();
    }


    public List<Article> findUnRunListByUser(Long userId) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getOperatorId, userId)
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .eq(Article::getStatus, MonitorStatusEnum.PRE_MONITOR.getCode())
                .isNull(Article::getProjectId);
        return this.list(queryWrapper);
    }

    public List<Article> findListByUser(Long userId, Boolean sort) {
        if (userId == null) {
            return null;
        }
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Article::getOperatorId, userId)
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .isNull(Article::getProjectId);

        if (Boolean.TRUE.equals(sort)) {
            wrapper.orderByDesc(Article::getStatus)
                    .orderByDesc(Article::getCreateTime)
                    .orderByAsc(Article::getPlatType);
        }
        return this.list(wrapper);
    }

    public List<Article> findArticleList(Long projectId, Boolean sort) {
        LambdaQueryWrapper<Article> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Article::getProjectId, projectId)
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode());

        if (Boolean.TRUE.equals(sort)) {
            wrapper.orderByDesc(Article::getStatus)
                    .orderByDesc(Article::getCreateTime)
                    .orderByAsc(Article::getPlatType);
        }
        return this.list(wrapper);
    }

    public void saveArticleDetail(ArticleDetail articleDetail) {
        articleDetailMapper.insert(articleDetail);
    }

    public void deleteArticleDetail(Long articleId) {
        if (ObjectUtil.isNotNull(articleId)) {
            articleDetailMapper.deleteById(articleId);
        }
    }

    public Article findArticleByProjectIdAndUrl(Long projectId, String articleUrl) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getProjectId, projectId)
                .eq(Article::getArticleUrl, articleUrl)
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    public PageArticleDetail findTotalData(Long projectId, Integer platform) {
        QueryWrapper<Article> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.eq("plat_type", platform);
        queryWrapper.eq("delete_status", DeleteStatusEnum.UNDELETE.getCode());
        queryWrapper.in("status", MonitorStatusEnum.MONITORING.getCode(), MonitorStatusEnum.MONITOR_FINISH.getCode());
        return articleDetailMapper.findTotalData(queryWrapper);
    }

    public List<Article> findAllErrDataByProjectId(Set<Long> proSet) {
        if (CollectionUtil.isEmpty(proSet)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .in(Article::getProjectId, proSet)
                .in(Article::getStatus, MonitorStatusEnum.MONITOR_END_ERROR.getCode(), MonitorStatusEnum.MONITOR_ING_ERROR.getCode());
        return this.list(queryWrapper);
    }

    public Article findArticleByUrlIdAndUrl(Long userId, String articleUrl) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<Article>()
                .eq(Article::getOperatorId, userId)
                .eq(Article::getArticleUrl, articleUrl)
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    public void deleteAndMonitor(Long projectId, Long userId) {
        // 删除
        this.lambdaUpdate()
                .eq(Article::getOperatorId, userId)
                .in(Article::getStatus,
                        MONITOR_URL_REPEAT.getCode(), MONITOR_CHECK_ERROR.getCode()
                        , USER_PROFILE_ERROR.getCode(), DYNAMIC_PROFILE_ERROR.getCode())
                .set(Article::getDeleteStatus, DeleteStatusEnum.DELETE.getCode())
                .set(Article::getUpdateTime, LocalDateTime.now())
                .update();

        // 正式监测
        this.lambdaUpdate()
                .set(Article::getProjectId, projectId)
                .set(Article::getStatus, MonitorStatusEnum.MONITORING.getCode())
                .set(Article::getUpdateTime, LocalDateTime.now())
                .eq(Article::getOperatorId, userId)
                .eq(Article::getDeleteStatus, DeleteStatusEnum.UNDELETE.getCode())
                .eq(Article::getStatus, MonitorStatusEnum.PRE_MONITOR.getCode())
                .isNull(Article::getProjectId)
                .update();
    }

    public List<Article> findByProjectTime(Long id, LocalDate atDate) {
        return this.lambdaQuery().eq(Article::getProjectId, id).le(Article::getConsumeDate, atDate).list();
    }

    public List<ArticleDetail> findDetailByProjectId(Long projectId) {

        return articleDetailMapper.selectList(new LambdaQueryWrapper<ArticleDetail>().eq(ArticleDetail::getArticleId, projectId));

    }

    public void updateDetailById(ArticleDetail articleDetail) {
        articleDetailMapper.updateById(articleDetail);
    }
}
