package com.bluefocus.kolmonitorservice.domain.gptalk.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode
public class GPTalkFinetuneEntity implements Serializable {

    @J<PERSON><PERSON>ield(name = "prompt")
    private String prompt;

    @JSONField(name = "messages")
    private List<ChatMessage> messages;

    @J<PERSON><PERSON>ield(name = "temperature")
    private Double temperature = 0.9D;

    @JSONField(name = "top_p")
    private Double topP = 1.0D;

    @JSONField(name = "frequency_penalty")
    private Double frequencyPenalty = 0.8D;

    @JSONField(name = "presence_penalty")
    private Double presencePenalty = 0D;

    @JSONField(name = "max_tokens")
    private Integer maxTokens = 4096;

    @JSONField(name = "stop")
    private List<String> stop = null;

}
