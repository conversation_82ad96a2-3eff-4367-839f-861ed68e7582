package com.bluefocus.kolmonitorservice.domain.chat.service;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.bluefocus.kolmonitorservice.base.enums.ChatMsgTypeEnum;
import com.bluefocus.kolmonitorservice.base.enums.ChatStatusEnum;
import com.bluefocus.kolmonitorservice.domain.chat.entity.ChatCommDo;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChat;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChatText;
import com.bluefocus.kolmonitorservice.domain.chat.mapper.MediaChatMapper;
import com.bluefocus.kolmonitorservice.domain.chat.mapper.MediaChatTextMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: yjLiu
 * @date: 0016 2024/10/16 10:55
 * @description:
 */
@RequiredArgsConstructor
@Service
public class MediaChatDomainService {

    private final MediaChatTextMapper mediaChatTextMapper;
    private final MediaChatMapper mediaChatMapper;

    public List<ChatCommDo> findCurrentChat(Long conversationId) {
        return mediaChatMapper.findCurrentChat(conversationId);
    }

    public void clean(Long chatId) {
        updateChatStatus(chatId, ChatStatusEnum.CHAT_LOSE.getCode());
    }

    public MediaChat chatSave(Long conversationId, Long chatId, String botId) {
        MediaChat mediaChat = new MediaChat();
        mediaChat.setChatId(chatId);
        mediaChat.setConversationId(conversationId);
        mediaChat.setBotId(botId);
        mediaChat.setStatus(ChatStatusEnum.CREATED.getCode());
        mediaChat.setCreateTime(LocalDateTime.now());
        mediaChatMapper.insert(mediaChat);
        return mediaChat;
    }

    public MediaChatText chatMsgSave(Long chatId, String content, Long sendUserId, Long recUserId, String role, String type) {
        MediaChatText mediaChatText = findChatTextByChatId(chatId, type);

        if (null == mediaChatText) {
            mediaChatText = new MediaChatText();
            mediaChatText.setChatId(chatId);
            mediaChatText.setContent(content);
            mediaChatText.setRole(role);
            mediaChatText.setType(type);
            mediaChatText.setCreateTime(LocalDateTime.now());
            mediaChatText.setSendUserId(sendUserId);
            mediaChatText.setReceiveId(recUserId);
            mediaChatTextMapper.insert(mediaChatText);
        } else {
            mediaChatText.setChatId(chatId);
            mediaChatText.setContent(content);
            mediaChatText.setRole(role);
            mediaChatText.setType(type);
            mediaChatText.setSendUserId(sendUserId);
            mediaChatText.setReceiveId(recUserId);
            mediaChatTextMapper.updateById(mediaChatText);
        }

        return mediaChatText;
    }

    public MediaChat findChatById(Long chatId) {
        return mediaChatMapper.selectById(chatId);
    }

    public void updateChatStatus(Long chatId, Integer status) {
        LambdaUpdateWrapper<MediaChat> set = new LambdaUpdateWrapper<MediaChat>().eq(MediaChat::getChatId, chatId)
                .set(MediaChat::getStatus, null == status ? ChatStatusEnum.COMPLETED.getCode() : status);
        mediaChatMapper.update(null, set);
    }

    public MediaChatText findChatTextByChatId(Long chatId, String type) {
        return mediaChatTextMapper.selectOne(new LambdaQueryWrapper<MediaChatText>()
                .eq(MediaChatText::getChatId, chatId)
                .eq(MediaChatText::getType, type));
    }


    public List<MediaChatText> findChatTextByChatId(Long chatId) {
        return mediaChatTextMapper.selectList(new LambdaQueryWrapper<MediaChatText>()
                .eq(MediaChatText::getChatId, chatId));
    }

    public MediaChatText findChatTextQuestion(Long chatId) {
        List<MediaChatText> textList = findChatTextByChatId(chatId);
        for (MediaChatText chatText : textList) {
            if (ObjectUtil.equal(ChatMsgTypeEnum.QUESTION.getType(), chatText.getType())) {
                return chatText;
            }
        }
        return null;
    }

    public MediaChat findChatByConversationId(Long conversationId) {
        LambdaUpdateWrapper<MediaChat> eq = new LambdaUpdateWrapper<MediaChat>().eq(MediaChat::getConversationId, conversationId)
                .orderByDesc(MediaChat::getCreateTime).last("limit 1");
        return mediaChatMapper.selectOne(eq);
    }
}
