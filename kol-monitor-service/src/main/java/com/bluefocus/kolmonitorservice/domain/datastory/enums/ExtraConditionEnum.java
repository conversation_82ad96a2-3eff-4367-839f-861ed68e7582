package com.bluefocus.kolmonitorservice.domain.datastory.enums;

import lombok.Getter;

import java.util.HashMap;

@Getter
public enum ExtraConditionEnum {
    /**
     * 渠道参数枚举
     */
    ALL(0, "全部","all"),
    XIAOHONGSHU(1, "小红书","xiaohongshu", "title,content,cover_ocr_content,audio_asr_content"),

    /**
     * 短视频-抖音
     */
    DOUYIN(2, "抖音", "shortVideo", "title,content,cover_ocr_content,audio_asr_content,commodity_title", "1003583", "video#0,image#0"),
//    KUAISHOU(4, "快手","shortVideo", "title,content,cover_ocr_content,audio_asr_content", "1334510", "video,image"),

    // 内容、源内容、源内容封面、源内容音频、封面、音频
    WEIBO(3, "微博","weibo", "content,src_content,cover_ocr_content,audio_asr_content"),

    BLIBLI(4, "哔哩哔哩","video", "content,title,video_labels,cover_ocr_content","44","video,dynamic,article"),
    ;

    private HashMap<String, Object> map;

    private static final String interactionCntDesc = "interactionCntDesc";

    ExtraConditionEnum(Integer code, String name, String key) {
        this.building(code,name, key, null, null, null, null);
    }

    private ExtraConditionEnum(Integer code, String name, String key, String matchFields) {
        this.building(code,name, key, matchFields, null, null, null);
    }

    private ExtraConditionEnum(Integer code, String name, String key, String matchFields, String siteIds) {
        this.building(code,name, key, matchFields, null, null, null);
    }

    private ExtraConditionEnum(Integer code, String name, String key, String matchFields, String siteIds, String postTypes) {
        this.building(code,name, key, matchFields, siteIds, postTypes, null);
    }

    private ExtraConditionEnum(Integer code, String name, String key, String matchFields, String siteIds, String postTypes, String sort) {
        this.building(code,name, key, matchFields, siteIds, postTypes, sort);
    }

    private void building(Integer code, String name, String key, String matchFields, String siteIds, String postTypes, String sort) {
        this.key = key;
        this.code = code;
        this.name = name;
        this.matchFields = matchFields;
        this.siteIds = siteIds;
        this.postTypes = postTypes;
        this.sort = sort;
        map = new HashMap<>();

        HashMap<String, String> valueMap = new HashMap<>();

        valueMap.put("matchFields", this.matchFields);

        if (siteIds != null) {
            valueMap.put("siteIds", this.siteIds);
        }

        if (postTypes != null) {
            valueMap.put("postTypes", this.postTypes);
        }

        if (sort != null) {
            valueMap.put("sort", this.sort);
        } else {
            valueMap.put("sort", interactionCntDesc);
        }
        map.put(this.key, valueMap);
    }

    public static String codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (ExtraConditionEnum e : ExtraConditionEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getKey();
            }
        }
        return null;
    }

    public static String nameOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (ExtraConditionEnum e : ExtraConditionEnum.values()) {
            if (e.getCode().equals(code)) {
                return e.getName();
            }
        }
        return null;
    }

    public static Integer keyOf(String key) {
        if (key == null) {
            return null;
        }
        for (ExtraConditionEnum e : ExtraConditionEnum.values()) {
            if (e.key.equals(key)) {
                return e.getCode();
            }
        }
        return null;
    }

    private String key;

    private Integer code;
    private String name;

    /**
     * 匹配字段
     * title 标题
     * content 内容
     * cover_ocr_content 封面
     * audio_asr_content 音频
     */
    private String matchFields;

    /**
     * 站点ID
     * 抖音：1003583
     */
    private String siteIds;

    private String postTypes;

    private String sort;
}
