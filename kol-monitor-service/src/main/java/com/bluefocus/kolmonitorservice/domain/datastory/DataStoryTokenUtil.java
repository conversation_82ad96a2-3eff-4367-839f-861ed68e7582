package com.bluefocus.kolmonitorservice.domain.datastory;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author: yj<PERSON><PERSON>
 * @date: 0005 2024/11/5 11:18
 * @description:
 */
public class DataStoryTokenUtil {

    private static final Map<String, String> TOKEN_MAP;
    private static final List<String> EMAIL_LIST;
    private static final AtomicInteger counter = new AtomicInteger(0);

    static {
        TOKEN_MAP = new LinkedHashMap<>();
        TOKEN_MAP.put("<EMAIL>", "a935217ef38df028524f77f3c727166a");
        TOKEN_MAP.put("<EMAIL>", "9a87ac36ca042508cfac56affda519cc");
        TOKEN_MAP.put("<EMAIL>", "e42363cda29ac79ff60f71d7a95ba30d");
        EMAIL_LIST = new ArrayList<>(TOKEN_MAP.keySet());
    }

    public static String getToken() {
        int index = counter.get() % EMAIL_LIST.size();

        String email = EMAIL_LIST.get(index);
        String token = TOKEN_MAP.get(email);

        if (counter.incrementAndGet() >= Integer.MAX_VALUE) {
            counter.set(0);
        }

        return token;
    }

    public static int getCounter() {
        return counter.get();
    }

}
