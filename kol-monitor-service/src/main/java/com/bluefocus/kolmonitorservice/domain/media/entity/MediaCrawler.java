package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 社媒爬虫记录
 * @TableName media_crawler
 */
@TableName(value ="media_crawler")
@Data
public class MediaCrawler implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * handle主键
     */
    private Long handleId;

    /**
     * url
     */
    private String url;

    /**
     * 请求参数
     */
    private String reqParam;

    /**
     * 响应参数
     */
    private String respParam;

    /**
     * 请求时间ms
     */
    private Long reqTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}