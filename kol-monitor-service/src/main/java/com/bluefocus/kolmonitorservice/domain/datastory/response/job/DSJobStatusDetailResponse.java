package com.bluefocus.kolmonitorservice.domain.datastory.response.job;

import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.response.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数说-任务详情-返回
 */
@NoArgsConstructor
@Data
@EqualsAndHashCode(callSuper = true)
public class DSJobStatusDetailResponse extends Response {

    /**
     * 任务详情
     */
    @JSONField(name = "data")
    private DSJobStatusDetailResponse.ResultDTO data;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JSONField(name = "id")
        private Long id;

        @JSONField(name = "name")
        private String name;

        /**
         * 任务状态:
         * 0-未运行
         * 1-运行中
         * 2-运行结束
         */
        @JSONField(name = "status")
        private Integer status;

        @JSONField(name = "createdTime")
        private Long createdTime;

        @JSONField(name = "updatedTime")
        private Long updatedTime;

        /**
         * 1-快速取数
         * 0-全量取数
         */
        @JSONField(name = "isFast")
        private Integer isFast;

        /**
         * 1-普通搜索
         * 0-高级搜索
         */
        @JSONField(name = "isCommon")
        private Integer isCommon;


        @JSONField(name = "jobInstStatusReport")
        private List<DSJobStatusDetailResponse.JobStatusDTO> jobStatus;

    }

    @NoArgsConstructor
    @Data
    public static class JobStatusDTO {
        @JSONField(name = "id")
        private Long id;

        @JSONField(name = "jobId")
        private Long jobId;

        @JSONField(name = "dataCount")
        private Integer dataCount;

        @JSONField(name = "isCrawl")
        private Integer isCrawl;

        @JSONField(name = "startTime")
        private Long startTime;

        @JSONField(name = "endTime")
        private Long endTime;

        @JSONField(name = "status")
        private Integer status;

        @JSONField(name = "schedStart")
        private Long schedStart;

        @JSONField(name = "schedEnd")
        private Long schedEnd;

        @JSONField(name = "errCode")
        private Integer errCode;

        @JSONField(name = "version")
        private Integer version;

    }


}
