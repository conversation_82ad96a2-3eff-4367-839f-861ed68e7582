package com.bluefocus.kolmonitorservice.domain.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 数说路由token
 * @TableName route_token
 */
@TableName(value ="route_token")
@Data
public class RouteToken implements Serializable {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * token
     */
    private String token;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}