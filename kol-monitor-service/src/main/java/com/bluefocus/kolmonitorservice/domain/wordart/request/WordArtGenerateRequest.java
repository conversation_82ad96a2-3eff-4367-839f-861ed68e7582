package com.bluefocus.kolmonitorservice.domain.wordart.request;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.wordart.DefaultWordArtRequest;
import com.bluefocus.kolmonitorservice.domain.wordart.entity.*;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordArtGenerateResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * https://wordart.com/api/test
 */
@Setter
public class WordArtGenerateRequest extends DefaultWordArtRequest<WordArtGenerateResponse> {

    @JsonIgnore
    private String imageBase64;


    @JSONField(name = "words")
    @Getter
    private List<WordArtWordEntity> words;
    @JSONField(name = "layout")
    private WordArtLayoutEntity layout;
    @JSONField(name = "shape")
    private WordArtShapeEntity shape;
    @JSONField(name = "fonts")
    private List<String> fonts = Collections.singletonList("Noto Sans S Chinese Regular");
    @JSONField(name = "style")
    private WordArtStyleEntity style;
    @JSONField(name = "output")
    private WordArtOutputEntity output;


    @Override
    public String getApiMethodName() {
        return "generate";
    }

    @Override
    public String getParams() {
        // 通过请求imageUrl获取结果，并进行base64编码
        if (null == this.layout) {
            layout = new WordArtLayoutEntity();
        }
        if (null == this.shape) {
            shape = new WordArtShapeEntity();
        }
        if (null == this.style) {
            style = new WordArtStyleEntity();
        }
        if (null == this.output) {
            output = new WordArtOutputEntity();
        }
        this.shape.setSrc(this.shape.getSrc() + this.imageBase64);
        // 对words 中的size 进行最大值为1000处理
        if (words.stream().anyMatch(e -> e.getSize() > 1000)) {
            Long maxSize = words.stream().max(Comparator.comparing(WordArtWordEntity::getSize)).get().getSize();
            words.forEach(e -> {
                if (e.getSize().equals(maxSize)) {
                    e.setSize(1000L);
                } else {
                    Long newSize = e.getSize() * 1000 / maxSize;
                    if (newSize == 0) {
                        e.setSize(1L);
                    } else {
                        e.setSize(newSize);
                    }
                }
            });
        }
        JSONObject params = new JSONObject();
        params.put("words", words);
        params.put("layout", layout);
        params.put("shape", shape);
        params.put("fonts", fonts);
        params.put("style", style);
        params.put("output", output);
        return params.toJSONString();
    }


    @Override
    public Class<WordArtGenerateResponse> getResponseClass() {
        return WordArtGenerateResponse.class;
    }


    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.imageBase64, "image");
        RequestCheckUtils.checkNotEmpty(this.words, "words");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }
}
