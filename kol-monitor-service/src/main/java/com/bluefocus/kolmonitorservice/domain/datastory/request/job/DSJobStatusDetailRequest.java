package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import cn.hutool.core.net.url.UrlQuery;
import cn.hutool.http.ContentType;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSJobStatusDetailResponse;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 数说-查询任务及任务实例状态
 */
@Setter
public class DSJobStatusDetailRequest extends JobDataStoryRequest<DSJobStatusDetailResponse> {

    @Value("${dataStory.task.jobStatus}")
    private String apiMethodName;

    private Long jobId;

    private Integer page = 1;

    @Override
    public String getParams() {
        return null;
    }

    @Override
    public String getApiMethodName() {
        TreeMap<String, Object> treeMap = new TreeMap<>();
        treeMap.put("id", this.jobId);
        treeMap.put("page", this.page);
        return (apiMethodName != null ? apiMethodName : "project/job/status/detail") + "?" + UrlQuery.of(treeMap);
    }

    @Override
    public Map<String, String> getHeaderMap() {
        if (this.headerMap == null) {
            this.headerMap = new HashMap<>();
        }
        this.headerMap.put("Content-Type", ContentType.JSON.getValue());
        return this.headerMap;
    }


    @Override
    public Class<DSJobStatusDetailResponse> getResponseClass() {
        return DSJobStatusDetailResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        RequestCheckUtils.checkNotEmpty(this.jobId, "jobId");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
