package com.bluefocus.kolmonitorservice.domain.route.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 路由业务组请求记录
 * @TableName route_req_record
 */
@TableName(value ="route_req_record")
@Data
public class RouteReqRecord implements Serializable {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 业务组id
     */
    private String bizId;

    /**
     * 业务token
     */
    private String token;

    /**
     * 三方平台
     */
    private String platform;

    /**
     * 请求方式
     */
    private String method;

    /**
     * 请求格式
     */
    private String type;

    /**
     * 请求地址
     */
    private String api;

    /**
     * 三方请求参数
     */
    private String reqParam;

    /**
     * 三方响应参数
     */
    private String resqParam;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}