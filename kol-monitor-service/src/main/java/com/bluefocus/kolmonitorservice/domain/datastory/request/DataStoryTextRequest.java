package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.base.util.RequestCheckUtils;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryRequest;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTextResponse;
import org.springframework.beans.factory.annotation.Value;

/**
 * 数说-原文内容统计接口
 */
public class DataStoryTextRequest extends DefaultDataStoryRequest<DataStoryTextResponse> {

    @Value("${dataStory.api.text}")
    private String apiMethodName;

    public DataStoryTextRequest(DataStoryEntity request) {
        super(request);
    }

    @Override
    public String getApiMethodName() {
        return apiMethodName != null ? apiMethodName : "socialmedia/text";
    }

    @Override
    public Class<DataStoryTextResponse> getResponseClass() {
        return DataStoryTextResponse.class;
    }

    @Override
    public void check() throws ApiRuleException {
        super.checkDefaultValue();
        RequestCheckUtils.checkNotEmpty(this.param.getSource(), "source");
    }

    @Override
    public String getMethod() {
        return null;
    }

    @Override
    public String getApi() {
        return null;
    }

}
