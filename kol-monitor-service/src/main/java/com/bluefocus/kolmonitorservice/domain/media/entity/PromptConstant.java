package com.bluefocus.kolmonitorservice.domain.media.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * <AUTHOR>
 * @TableName prompt_constant
 */
@TableName(value ="prompt_constant")
@Data
public class PromptConstant implements Serializable {
    /**
     * 
     */
    private String prompt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        PromptConstant other = (PromptConstant) that;
        return (this.getPrompt() == null ? other.getPrompt() == null : this.getPrompt().equals(other.getPrompt()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getPrompt() == null) ? 0 : getPrompt().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", prompt=").append(prompt);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}