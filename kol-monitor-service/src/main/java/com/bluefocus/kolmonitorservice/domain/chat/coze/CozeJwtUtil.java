package com.bluefocus.kolmonitorservice.domain.chat.coze;

import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.util.Times;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.openssl.PEMParser;
import org.bouncycastle.util.io.pem.PemObject;

import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.LocalDateTime;
import java.util.Base64;

/**
 * @author: yjLiu
 * @date: 0004 2024/11/4 21:24
 * @description:
 */
@Slf4j
public class CozeJwtUtil {

    /**
     * 此为产品经理崔博文的Coze应用ID
     */
    private static final String APP_ID = "1165128219382";
    private static final String COZE_URL = "api.coze.cn";
    private static final String SECRET_KEY = "VAMYpryHBBAisEjuPfEJPA1yVlMiytFk_qn92bYFE_o";

    public static String createToken(String appId, String publicKey, String keyPath) {
        String compact = null;
        try {
            compact = Jwts.builder()
                    .setHeader(generateHeader(publicKey))
                    .setPayload(generatePayload(appId))
                    .signWith(SignatureAlgorithm.RS256, readPrivateKeyFromFile(keyPath))
                    .compact();
        } catch (Exception e) {
            log.error("生成cozetoken异常：", e);
        }
        return compact;
    }

    private static JSONObject generateHeader(String publicKey) {
        JSONObject header = new JSONObject(8);
        header.put("alg", "RS256");
        header.put("typ", "JWT");
        header.put("kid", publicKey);
        return header;
    }

    private static String generatePayload(String appId) {
        JSONObject payload = new JSONObject(8);
        payload.put("iss", appId);
        payload.put("aud", COZE_URL);
        payload.put("iat", Times.toEpochMilli(LocalDateTime.now()) / 1000);
        payload.put("exp", Times.toEpochMilli(LocalDateTime.now().plusHours(24L)) / 1000);
        payload.put("jti", generateRandomString(64));
        return payload.toJSONString();
    }

    private static String encodePayload(String payload) {
        return Base64.getUrlEncoder().withoutPadding()
                .encodeToString(payload.getBytes(StandardCharsets.UTF_8));
    }

    private static PrivateKey readPrivateKeyFromFile(String filePath) throws Exception {
        byte[] pemData = Files.readAllBytes(Paths.get(filePath));
        try (PEMParser pemParser = new PEMParser(new StringReader(new String(pemData)))) {
            PemObject pemObject = pemParser.readPemObject();
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pemObject.getContent());
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            return keyFactory.generatePrivate(keySpec);
        }
    }

    private static String generateRandomString(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be positive");
        }

        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[length];
        random.nextBytes(bytes);

        return Base64.getUrlEncoder().withoutPadding().encodeToString(bytes);
    }

}
