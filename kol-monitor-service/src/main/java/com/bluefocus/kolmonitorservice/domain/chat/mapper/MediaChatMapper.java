package com.bluefocus.kolmonitorservice.domain.chat.mapper;

import com.bluefocus.kolmonitorservice.domain.chat.entity.ChatCommDo;
import com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChat;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【media_chat(社媒对话表)】的数据库操作Mapper
* @createDate 2024-10-16 10:55:04
* @Entity com.bluefocus.kolmonitorservice.domain.chat.entity.MediaChat
*/
public interface MediaChatMapper extends BaseMapper<MediaChat> {

    @Select("SELECT mct.id messageId,mc.chat_id chatId,mct.content,mc.`status`,mct.role,mct.type,mct.create_time chatTime " +
            "FROM media_chat mc LEFT JOIN media_chat_text mct on mc.chat_id=mct.chat_id WHERE " +
            "mc.conversation_id=#{conversationId} and " +
            "mc.`status`in (0,1,2,4)  ORDER BY mct.create_time ASC")
    List<ChatCommDo> findCurrentChat(@Param("conversationId") Long conversationId);
}