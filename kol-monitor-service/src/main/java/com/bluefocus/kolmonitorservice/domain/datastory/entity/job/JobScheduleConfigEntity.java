package com.bluefocus.kolmonitorservice.domain.datastory.entity.job;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode()
public class JobScheduleConfigEntity implements Serializable {

    /**
     * 调度类型
     * TEMP-临时
     * SIMPLE-周期
     */
    @JSONField(name = "type")
    private String type = "SIMPLE";

    @JSONField(name = "startDataTime")
    private Long startDataTime;

    @JSONField(name = "endDataTime")
    private Long endDataTime;

    /**
     * 任务运行周期
     *
     */
    @JSONField(name = "interval")
    private Integer interval = 1;

    /**
     * 任务运行周期单位
     * d-日
     * H-时
     * M-月
     */
    @JSONField(name = "unit")
    private String unit = "d";

}
