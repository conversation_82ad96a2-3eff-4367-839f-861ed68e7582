package com.bluefocus.kolmonitorservice.domain.chat.coze.req;

import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.base.exception.ApiRuleException;
import com.bluefocus.kolmonitorservice.domain.chat.coze.DefaultCozeRequest;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.CozeTokenResp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * @author: yjLiu
 * @date: 0021 2024/10/21 17:06
 * @description:
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CozeTokenRequest extends DefaultCozeRequest<CozeTokenResp> {

    @Setter
    private String apiName;
    @Setter
    private String apiMethod;

    @Override
    public String getApiMethodName() {
        return apiName;
    }

    @Override
    public String getMethod() {
        return apiMethod;
    }

    @Override
    public String getParams() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("duration_seconds", 86399);
        jsonObject.put("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer");
        return jsonObject.toJSONString();
    }

    @Override
    public Class<CozeTokenResp> getResponseClass() {
        return CozeTokenResp.class;
    }

    @Override
    public void check() throws ApiRuleException {

    }
}
