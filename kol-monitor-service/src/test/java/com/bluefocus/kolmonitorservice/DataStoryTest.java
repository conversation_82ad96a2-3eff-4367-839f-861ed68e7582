package com.bluefocus.kolmonitorservice;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import org.junit.Test;

import java.util.*;

//@RunWith(SpringRunner.class)
//@SpringBootTest
//@Slf4j
public class DataStoryTest {

//    @Autowired
//    private DefaultDataStoryClient client;

    private void buildExtraCondition(HashMap<String, Object> extraCondition, HashMap<String, Object> siteMap, String splitKey) {
        siteMap.keySet().forEach(key -> {
            HashMap<String, String> sits = (HashMap<String, String>) siteMap.get(key);
            if (extraCondition.containsKey(key)) {
                HashMap<String, String> oldMap = (HashMap<String, String>) extraCondition.get(key);
                sits.keySet().forEach(k -> {
                    String s = sits.get(k);
                    String oldS = oldMap.get(k);
                    if (oldMap.containsKey(k)) {
                        String[] s1 = oldS.split(splitKey);
                        String[] s2 = s.split(splitKey);
                        List<String> sList = new ArrayList<>();
                        sList.addAll(Arrays.asList(s1));
                        sList.addAll(Arrays.asList(s2));
                        Set<String> uniqueSet = new HashSet<>(sList);
                        oldMap.put(k, String.join(splitKey, uniqueSet));
                    } else {
                        oldMap.put(k, s);
                    }
                });
            } else {
                extraCondition.put(key, sits);
            }
        });
    }

    @Test
    public void reqTest() throws ApiException {
        HashMap<String, Object> extraCondition = new HashMap<>();
        buildExtraCondition(extraCondition, ExtraConditionEnum.DOUYIN.getMap(), ",");
        buildExtraCondition(extraCondition, ExtraConditionEnum.XIAOHONGSHU.getMap(), ",");
        buildExtraCondition(extraCondition, ExtraConditionEnum.WEIBO.getMap(), ",");
//        buildExtraCondition(extraCondition, ExtraConditionEnum.KUAISHOU.getMap(), ",");
        System.out.println(JSON.toJSONString(extraCondition));
//        DefaultDataStoryClient client = new DefaultDataStoryClient("http://api.dc.datastory.com.cn/api/socialmedia/", "1");
//        DataStoryEntity req = new DataStoryEntity();
//        req.setKeyword("华为");
//        req.setStartTime(1677859200000L);
//        req.setEndTime(1680537599499L);
//        req.setSources(Collections.singletonList(ExtraConditionEnum.XIAOHONGSHU.getKey()));
//        DataStoryTotalVolumeRequest request = new DataStoryTotalVolumeRequest(req);
////        req.putOtherTextParam("keyword","华为");
////        req.putOtherTextParam("sources", Collections.singletonList("xiaohongshu"));
////        req.putOtherTextParam("startTime","1677859200000");
////        req.putOtherTextParam("endTime","1680537599499");
//        DataStoryTotalVolumeResponse execute = client.execute(request);
//        System.out.println( JSON.toJSONString(execute));
    }
}
