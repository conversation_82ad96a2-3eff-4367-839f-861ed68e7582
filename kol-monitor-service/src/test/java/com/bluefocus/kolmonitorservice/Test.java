//package com.bluefocus.kolmonitorservice;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.bluefocus.basebean.exception.ResponseException;
//import com.bluefocus.kolmonitorinterface.dto.res.PlatformReportData;
//import lombok.extern.slf4j.Slf4j;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.concurrent.Callable;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.Executor;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.Future;
//
///**
// * <AUTHOR>
// * @Descirption
// * @date 2023/3/28 10:58
// */
//@Slf4j
//public class Test {
//
//
//    public static void main(String[] args) {
//
//        ExecutorService threadPoolExecutor = Executors.newFixedThreadPool(4);
//
//        List<Integer> platformList = Arrays.asList(1, 2, 3, 4);
//
//        CountDownLatch latch = new CountDownLatch(platformList.size());
//        platformList.forEach(platform -> {
//                    Callable<Boolean> callable = () -> {
//                        log.info("latch ----");
////                        latch.countDown();
//
//                        int i = 5 / 0;
//                        log.info("return");
//                        return true;
//                    };
//
//                    log.info("future start");
//                    Future<Boolean> future = threadPoolExecutor.submit(callable);
//                    Boolean result = false;
//                    try {
//                        result = future.get();
//                        log.info("result == {}", result);
//                        log.error("检查异步线程任务是否执行完毕 " + future.isDone());
//                    } catch (Exception e) {
//                        log.error("ReportService.exportReportForPPT 线程处理业务异常：{}");
//                    } finally {
//                        if (ObjectUtil.isNull(result) || !result) {
//                            log.error("业务数据异常，项目id：{}", 111);
//                        }
//                        log.info("finally !!!");
//                    }
//                }
//        );
//
//
//        try {
//            log.info("latch.s");
//            latch.await();
//            log.info("latch.end");
//        } catch (InterruptedException e) {
//            log.error("ReportService.exportReportForPPT 线程中断异常：{}", e);
//        }
//    }
//}
