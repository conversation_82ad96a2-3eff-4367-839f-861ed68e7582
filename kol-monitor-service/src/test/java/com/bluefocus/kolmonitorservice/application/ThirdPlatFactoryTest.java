package com.bluefocus.kolmonitorservice.application;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.application.service.plat.PlatResult;
import com.bluefocus.kolmonitorservice.application.service.plat.ThirdPlatFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

import static org.junit.Assert.assertNotNull;

/**
 * @author: yjLiu
 * @date: 0010 2025/6/10 17:43
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ThirdPlatFactoryTest {


    @Autowired
    private ThirdPlatFactory platFactory;

    @Test
    public void testDouyinShortUrl() {

        ArrayList<String> xhsUrls = new ArrayList<>();
        xhsUrls.add("http://xhslink.com/6EdcOQ");
//        xhsUrls.add("https://www.xiaohongshu.com/explore/640f20bf000000002700035f?xsec_token=ABwMU-hVwnkYD5wB8pFpoxRd3VxlxZXLEkXAz7-JvffXI=&xsec_source=pc_feed");
//        xhsUrls.add("https://www.xiaohongshu.com/user/profile/5be84894954c810001c86698");
//        xhsUrls.add("https://www.xiaohongshu.com/discovery/item/669897790000000025001256");

        ArrayList<String> dyUrls = new ArrayList<>();
//        dyUrls.add(url);
//        dyUrls.add(url);
//        dyUrls.add(url);
//        dyUrls.add(url);
        ArrayList<String> arrayList = new ArrayList<>();
        arrayList.addAll(xhsUrls);
        arrayList.addAll(dyUrls);
        ArrayList<PlatResult> platResults = testUrl(arrayList);
        log.info("抖音短链结果: {}", JSON.toJSONString(platResults));
    }

    private ArrayList<PlatResult> testUrl(ArrayList<String> urls) {
        ArrayList<PlatResult> platResults = new ArrayList<>();
        for (String url : urls) {
            PlatResult result = platFactory.dispatch(url);
            assertNotNull(result);
            platResults.add(result);
        }
        return platResults;
    }

}
