package com.bluefocus.kolmonitorservice.application.service.gptalk;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.gptalk.DefaultGPTalkClient;
import com.bluefocus.kolmonitorservice.domain.gptalk.request.GPTalkImageRequest;
import com.bluefocus.kolmonitorservice.domain.gptalk.response.GPTalkImageResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class GPTalkImageRequestTest {
    private DefaultGPTalkClient client;
    private DefaultGPTalkClient client2;
    private DefaultGPTalkClient client3;

    @Before
    public void setUp() {
        client = new DefaultGPTalkClient("https://xiaoboteopenai.openai.azure.com/openai/deployments/", "45bfa90ad7e446ff85c5d5a50d876e3c");
        client2 = new DefaultGPTalkClient("https://xiaoboteopenai-se.openai.azure.com/openai/deployments/", "c036aac5022046b383ec2f41f512e633");
        client3 = new DefaultGPTalkClient("https://xiaoboteopenai-ae.openai.azure.com/openai/deployments/", "df9e61b786b0439594eb1081680dcc6f");
    }

    @Test
    public void check() throws Exception {
        List<Thread> threads = new ArrayList<>();
        threads.add(new Thread(() -> check1(client)));
        threads.add(new Thread(() -> check2(client2)));
        threads.add(new Thread(() -> check3(client3)));
//        Thread.wait(1000 * 60 * 60 * 5L);
        threads.forEach(Thread::start);
        // 输出结果
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
        log.info("测试完成");
    }

    private void check1(DefaultGPTalkClient client) {
        String cont = "Generate a minimalistic, unadorned silhouette image with a white background. The image should only contain one object, depicted in a cartoon style: ";
        List<Thread> threads = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            int finalI = i;
            threads.add(new Thread(() -> {
                try {
                    log.info("地址一开始生成，count={}", finalI);
                    long s = System.currentTimeMillis();
                    GPTalkImageRequest request = new GPTalkImageRequest();
                    request.setPrompt(cont + "一只蓝色小猫");
                    GPTalkImageResponse execute = client.execute(request, client.getServerUrl(), client.getApiKey());
                    log.info("地址一测试时长{}，生成结果：{}", System.currentTimeMillis() - s, JSON.toJSONString(execute));
                    log.info("地址一测试生成图片：{}", JSON.toJSONString(execute.getData()));

                } catch (ApiException e) {
                    throw new RuntimeException(e);
                }
            }));
        }

        // 检查 是否执行完成
        threads.forEach(Thread::start);
        // 输出结果
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void check2(DefaultGPTalkClient client) {
        String cont = "Generate a minimalistic, unadorned silhouette image with a white background. The image should only contain one object, depicted in a cartoon style: ";
        List<Thread> threads = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            int finalI = i;
            threads.add(new Thread(() -> {
                try {
                    log.info("地址二开始生成，count={}", finalI);
                    long s = System.currentTimeMillis();
                    GPTalkImageRequest request = new GPTalkImageRequest();
                    request.setPrompt(cont + "一只蓝色小狗");
                    GPTalkImageResponse execute = client.execute(request, client.getServerUrl(), client.getApiKey());
                    log.info("地址二测试时长{}，生成结果：{}", System.currentTimeMillis() - s, JSON.toJSONString(execute));
                    log.info("地址二测试生成图片：{}", JSON.toJSONString(execute.getData()));

                } catch (ApiException e) {
                    throw new RuntimeException(e);
                }
            }));
        }

        // 检查 是否执行完成
        threads.forEach(Thread::start);
        // 输出结果
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void check3(DefaultGPTalkClient client) {
        String cont = "Generate a minimalistic, unadorned silhouette image with a white background. The image should only contain one object, depicted in a cartoon style: ";
        List<Thread> threads = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            int finalI = i;
            threads.add(new Thread(() -> {
                try {
                    log.info("地址三开始生成，count={}", finalI);
                    long s = System.currentTimeMillis();
                    GPTalkImageRequest request = new GPTalkImageRequest();
                    request.setPrompt(cont + "一只蓝色小牛");
                    GPTalkImageResponse execute = client.execute(request, client.getServerUrl(), client.getApiKey());
                    log.info("地址三测试时长{}，生成结果：{}", System.currentTimeMillis() - s, JSON.toJSONString(execute));
                    log.info("地址三测试生成图片：{}", JSON.toJSONString(execute.getData()));

                } catch (ApiException e) {
                    throw new RuntimeException(e);
                }
            }));
        }

        // 检查 是否执行完成
        threads.forEach(Thread::start);
        // 输出结果
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        });
    }
}