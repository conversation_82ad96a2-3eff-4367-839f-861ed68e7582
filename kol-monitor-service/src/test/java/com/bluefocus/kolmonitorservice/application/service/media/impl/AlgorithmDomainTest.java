package com.bluefocus.kolmonitorservice.application.service.media.impl;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAlgorithmStart;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAllDataStart;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaWordArtStart;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.algorithm.AlgorithmDomainService;
import com.bluefocus.kolmonitorservice.domain.algorithm.entity.HotAnalysisRespEntity;
import com.bluefocus.kolmonitorservice.domain.algorithm.request.AlgorithmHotAnalysisRequest;
import com.bluefocus.kolmonitorservice.domain.algorithm.response.AlgorithmHotAnalysisResponse;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaNoteDay;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTrend;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaNoteDayDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTrendDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0005 2024/6/5 20:37
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class AlgorithmDomainTest {

    @Resource
    MediaTrendDomainService mediaTrendDomainService;
    @Resource
    MediaTaskHandleDomainService mediaTaskHandleDomainService;
    @Resource
    AlgorithmDomainService algorithmDomainService;
    @Resource
    MediaAllDataStart mediaAllDataStart;
    @Resource
    MediaAlgorithmStart mediaAlgorithmStart;
    @Resource
    MediaNoteDayDomainService mediaNoteDayDomainService;
    @Resource
    MediaWordArtStart mediaWordArtStart;

    /**
     * 测试全部笔记
     */
    @Test
    public void test_allPlatHot() {
        long handleId = 498L;
        MediaTaskHandle handle = getHandle(handleId);
        Boolean aBoolean = mediaAlgorithmStart.executeAlgorithm(handle);
        log.info("测试结束：{}", aBoolean);
    }

    private MediaTaskHandle getHandle(long handleId) {
        return mediaTaskHandleDomainService.getById(handleId);
    }

    @Test
    public void test_singlePlatHot() {
        long handleId = 498L;
        String hotDay = "20240619";
        Integer sourceCode = 2;
        MediaTaskHandle handle = getHandle(handleId);
//        MediaTrend mediaTrend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), sourceCode);
        List<MediaNoteDay> noteList = mediaNoteDayDomainService.findHotDayNote(handle.getId(), sourceCode, hotDay);

        AlgorithmHotAnalysisRequest request = new AlgorithmHotAnalysisRequest();
        request.setMediaObjectsId(handleId);
        request.setDate(hotDay);
        request.setKeywords(JSON.parseArray(handle.getKeyword(), String.class));
        request.setSource(ExtraConditionEnum.codeOf(sourceCode));

        request.setNotes(noteList);
        AlgorithmHotAnalysisResponse response = null;
        try {
            response = algorithmDomainService.hotAnalysis(request);
            HotAnalysisRespEntity hotConclusion = response.getData().getHotConclusion();
            log.info("响应结果={}", JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("响应={}, err={}", JSON.toJSONString(response), e);
        }
        log.info("测试结束");
    }

    private void analyzeAllNoteTrend(List<MediaTrend> mediaTrendList, MediaObjects obj) {
        Long id = obj.getId();
        List<String> keyList = JSON.parseArray(obj.getKeyword(), String.class);
        Set<Integer> codeList = mediaTrendList.stream().map(MediaTrend::getSourceCode).collect(Collectors.toSet());
        mediaTrendList.stream()
                .filter(m -> m.getSourceCode() == 0)
                .forEach(mediaTrend -> {
                    List<KeyValue<String, Long>> keyValues = mediaAllDataStart.kvJsonConvert(mediaTrend.getInteractionTrendDay());
                    keyValues.sort(Comparator.comparingLong(KeyValue::getValue));
                    int size = keyValues.size();
                    ArrayList<String> dateList = new ArrayList<>();
                    int i = 1;

                    // 前3天的
                    while (i <= 1) {
                        dateList.add(keyValues.get(size - i).getDate());
                        i++;
                    }

                    List<HotAnalysisRespEntity> collect = dateList.stream()
                            .map(date -> {

                                AlgorithmHotAnalysisRequest algorithmHotAnalysisRequest = new AlgorithmHotAnalysisRequest();
                                algorithmHotAnalysisRequest.setMediaObjectsId(mediaTrend.getMediaObjectsId());
                                algorithmHotAnalysisRequest.setDate(date);
                                algorithmHotAnalysisRequest.setKeywords(keyList);
                                String sourceName = ExtraConditionEnum.codeOf(mediaTrend.getSourceCode());
                                if (sourceName.equals(ExtraConditionEnum.ALL.getKey())) {
                                    algorithmHotAnalysisRequest.setNotes(mediaAlgorithmStart.syncAllDataStoryNote(id, codeList, date));
                                } else {
                                    algorithmHotAnalysisRequest.setNotes(mediaAlgorithmStart.syncDataStoryNote(id, mediaTrend.getSourceCode(), date));
                                }
                                algorithmHotAnalysisRequest.setSource(ExtraConditionEnum.codeOf(mediaTrend.getSourceCode()));
                                log.info("请求算法分析内容：{}", JSON.toJSONString(algorithmHotAnalysisRequest.getParams()));
                                HotAnalysisRespEntity hotConclusion = new HotAnalysisRespEntity();
                                try {
                                    AlgorithmHotAnalysisResponse response = algorithmDomainService.hotAnalysis(algorithmHotAnalysisRequest);
                                    if (response.isSuccess()) {
                                        hotConclusion = response.getData().getHotConclusion();
                                    }
                                } catch (ApiException e) {
                                    log.error("请求算法热点分析接口失败");
                                }
                                return hotConclusion;
                            }).collect(Collectors.toList());


                    mediaTrend.setHotConclusions(JSON.toJSONString(collect));
                    mediaTrendDomainService.saveOrUpdate(mediaTrend);
                });
    }

}
