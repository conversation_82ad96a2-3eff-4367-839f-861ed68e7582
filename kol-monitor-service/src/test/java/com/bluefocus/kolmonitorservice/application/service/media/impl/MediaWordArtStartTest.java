package com.bluefocus.kolmonitorservice.application.service.media.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.domain.media.entity.GptPicture;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaWord;
import com.bluefocus.kolmonitorservice.domain.media.service.GptPictureDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaWordDomainService;
import com.bluefocus.kolmonitorservice.domain.wordart.entity.WordArtWordEntity;
import com.bluefocus.kolmonitorservice.domain.wordart.request.WordArtGenerateRequest;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class MediaWordArtStartTest {
    private final static Logger logger = LoggerFactory.getLogger(MediaWordArtStartTest.class);
    @Resource
    MediaTaskHandleDomainService mediaTaskHandleDomainService;
    @Resource
    private MediaWordArtStart mediaWordArtStart;
    @Resource
    private MediaWordDomainService mediaWordDomainService;
    @Resource
    private ThreadPoolExecutor wordArtThreadPoolExecutor;

    @Resource
    private GptPictureDomainService gptPictureDomainService;

    private MediaTaskHandle getHandle(long handleId) {
        return mediaTaskHandleDomainService.getById(handleId);
    }

    @Test
    public void test_handle() {
        MediaTaskHandle handle = getHandle(222);
        mediaWordArtStart.handle(handle, null);
    }

    @Test
    public void test_updateArtStatus() {
        String message = "{\"id\":284,\"mediaTaskId\":163,\"times\":null,\"classSource\":\"MediaWordArtStart\",\"handId\":201}";
        MediaMsgBody msgBody = new Gson().fromJson(message, MediaMsgBody.class);
        mediaWordArtStart.updateArtStatus(msgBody);
    }

    @Test
    public void test_oneWordArt() {
        String imageUrl = "https://bluefocus-test.oss-cn-beijing.aliyuncs.com/media/wordimg.png";
        MediaWord byObjIds = mediaWordDomainService.findByHandleIds(Collections.singletonList(248L)).get(0);
        List<KeyValue> keyValues = JSON.parseArray(byObjIds.getAllWord(), KeyValue.class);
        Map<String, Long> wordMap = keyValues.stream().collect(Collectors.toMap(KeyValue::getLabel, keyValue -> Long.valueOf(keyValue.getValue().toString())));
        GptPicture gptPicture = gptPictureDomainService.genWordArtImgAndSave(imageUrl, wordMap, byObjIds.getMediaObjectsId());
        System.out.println(JSON.toJSONString(gptPicture));
    }

    @Test
    public void test_allWordArt() {
        MediaTaskHandle handle = getHandle(507L);
        mediaWordArtStart.handle(handle, null);
        log.info("测试结束");
    }

    @Test
    public void test_allWordCould() {
        MediaTaskHandle handle = getHandle(507L);
        mediaWordArtStart.handle(handle, null);
        log.info("测试结束");
    }

    @Test
    public void test_oneWordCould() throws InterruptedException {
        MediaTaskHandle handle = getHandle(507L);
        String imageUrl = "https://bluefocus-test.oss-cn-beijing.aliyuncs.com/media/wordimg.png";
        List<MediaWord> wordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(handle.getId()));
        GptPicture wordCloudImgReq = mediaWordArtStart.getWordCloudImgReq(handle, wordList.get(0), imageUrl);
        log.info("测试结束:{}", JSON.toJSONString(wordCloudImgReq));
    }

    @Test
    public void test_batchWordCould() throws InterruptedException {
        MediaTaskHandle handle = getHandle(507L);
        String imageUrl = "https://bluefocus-test.oss-cn-beijing.aliyuncs.com/media/wordimg.png";
        List<MediaWord> wordList = mediaWordDomainService.findByHandleIds(Collections.singletonList(handle.getId()));
        CountDownLatch countDownLatch = new CountDownLatch(4);
        CopyOnWriteArrayList<GptPicture> gptPictures = new CopyOnWriteArrayList<>();

        for (MediaWord mediaWord : wordList) {

            new Thread(() -> {
                try {
                    GptPicture wordCloudImgReq = mediaWordArtStart.getWordCloudImgReq(handle, mediaWord, imageUrl);
                    gptPictures.add(wordCloudImgReq);
                } catch (Exception e) {
                    log.error("错误。e={}", e);
                } finally {
                    countDownLatch.countDown();
                }

            }).start();
        }
        countDownLatch.await();
        log.info("测试结束:{}", JSON.toJSONString(gptPictures));
    }
}