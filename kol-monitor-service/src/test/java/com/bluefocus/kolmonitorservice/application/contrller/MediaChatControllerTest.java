package com.bluefocus.kolmonitorservice.application.contrller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bluefocus.kolmonitorinterface.dto.chat.req.ChatReq;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatHistoryResp;
import com.bluefocus.kolmonitorinterface.dto.chat.resq.ChatResp;
import com.bluefocus.kolmonitorservice.application.service.chat.MediaChatService;
import com.bluefocus.kolmonitorservice.base.enums.ChatRoleEnum;
import com.bluefocus.kolmonitorservice.base.enums.ChatStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * @author: yjLiu
 * @date: 0022 2024/10/22 11:50
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class MediaChatControllerTest {

    @Resource
    MediaChatService mediaChatService;
    ExecutorService threadPool = Executors.newFixedThreadPool(10);

    @Test
    public void test_contentBuild() {
//        List<Long> objList = Arrays.asList(418L, 419L, 478L, 477L, 503L, 592L);
//        testChatCompletion(objList);
//        List<Long> testChatList = testChatList(objList);
//        log.info("第一轮对话结束,结果testChatList={}", testChatList);
//        testChatCompletion(testChatList);
    }

    private List<Long> testChatList(List<Long> objList) {
        ArrayList<Long> arrayList = new ArrayList<>();
        for (Long objId : objList) {
            ChatHistoryResp chatHistoryResp = mediaChatService.chatList(objId);
            log.info("对象[{}]数据列表:{}", objId, JSON.toJSONString(chatHistoryResp));
            if (CollectionUtil.isEmpty(chatHistoryResp.getChatRespList())) {
                arrayList.add(objId);
                continue;
            }
            for (ChatResp chatResp : chatHistoryResp.getChatRespList()) {
                if (chatResp.getStatus().equals(ChatStatusEnum.CREATED.getCode())) {
                    arrayList.add(objId);
                }
            }
        }
        return arrayList;
    }

    private void testChatCompletion(List<Long> asList) {

        for (Long objId : asList) {
            threadPool.submit(() -> {
                ChatReq chatReq = new ChatReq();
                chatReq.setMediaObjectsId(objId);
                chatReq.setRole(ChatRoleEnum.USER.getRole());
                ChatResp chatResp = mediaChatService.chatCompletion(chatReq);
                log.info("当前对话{}结果:{},内容:{}", chatResp.getChatId(), ChatStatusEnum.getStatusByCode(chatResp.getStatus()), chatResp.getContent());
            });
        }

        boolean allTasksCompleted;
        try {
            allTasksCompleted = threadPool.awaitTermination(1, TimeUnit.MINUTES);
            if (!allTasksCompleted) {
                log.warn("对话线程有未完成的.");
            } else {
                log.info("对话线程全部结束");
            }
        } catch (InterruptedException e) {
            log.error("线程中断异常：", e);
        }
//        finally {
//            threadPool.shutdown();
//        }
        log.info("测试结束");
    }

}
