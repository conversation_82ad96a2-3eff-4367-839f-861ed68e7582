package com.bluefocus.kolmonitorservice.application.service.gptalk;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.enums.ChatRoleEnum;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.gptalk.DefaultGPTalkClient;
import com.bluefocus.kolmonitorservice.domain.gptalk.entity.ChatMessage;
import com.bluefocus.kolmonitorservice.domain.gptalk.request.GPTalkChatMessageRequest;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class GPTalkChatMessageRequestTest {

    private DefaultGPTalkClient client;
    @Before
    public void setUp() {
        client = new DefaultGPTalkClient("https://xiaoboteopenai-se.openai.azure.com/openai/deployments/","c036aac5022046b383ec2f41f512e633");
    }

    @Test
    public void check() throws ApiException {
        GPTalkChatMessageRequest request = new GPTalkChatMessageRequest();
        request.setMaxTokens(10);
        List<ChatMessage> chatMessages = new ArrayList<>();
        chatMessages.add(new ChatMessage(ChatRoleEnum.SYSTEM.getRole(),"----角色----\nDALL-E-3 prompt 转换器\n----任务----\n理解用户输入的内容,用英文输出符合DALL-E-3的prompt"));
        chatMessages.add(new ChatMessage(ChatRoleEnum.USER.getRole()," 给我一个以黑色为主题的剪影图，不需要复杂的元素，不需要理解图片内容描述的颜色。图片内容描述如下：一只蓝色企鹅"));
        request.setMessages(chatMessages);
        System.out.println(JSON.toJSONString(client.execute(request)));
    }
}