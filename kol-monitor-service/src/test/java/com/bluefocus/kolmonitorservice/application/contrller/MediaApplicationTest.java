package com.bluefocus.kolmonitorservice.application.contrller;

import com.alibaba.fastjson.JSON;
import com.bluefocus.basebean.bean.BaseResponseBean;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorinterface.dto.media.req.MediaObjectsReq;
import com.bluefocus.kolmonitorinterface.dto.media.req.MediaTaskSaveReq;
import com.bluefocus.kolmonitorinterface.dto.media.req.UpdateWordCloudReq;
import com.bluefocus.kolmonitorservice.application.service.media.MediaService;
import com.bluefocus.kolmonitorservice.application.service.media.impl.*;
import com.bluefocus.kolmonitorservice.base.util.Times;
import com.bluefocus.kolmonitorservice.domain.media.entity.*;
import com.bluefocus.kolmonitorservice.domain.media.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author: yjLiu
 * @date: 0023 2024/5/23 10:27
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class MediaApplicationTest {

    @Resource
    MediaService mediaService;
    @Resource
    MediaObjectsDomainService mediaObjectsDomainService;
    @Resource
    MediaTaskHandleDomainService mediaTaskHandleDomainService;
    @Resource
    MediaTrendDomainService mediaTrendDomainService;
    @Resource
    MediaWordDomainService mediaWordDomainService;
    @Resource
    MediaNoteStart mediaNoteStart;
    @Resource
    MediaNoteDomainService mediaNoteDomainService;
    @Resource
    MediaAllDataStart mediaAllDataStart;
    @Resource
    MediaWordCouldStart mediaWordCouldStart;
    @Resource
    MediaVolumeStart mediaVolumeStart;
    @Resource
    MediaInteractionStart mediaInteractionStart;
    @Resource
    MediaSentimentStart mediaSentimentStart;
    @Resource
    MediaNoteDayStart mediaNoteDayStart;
    @Resource
    ApiMediaService apiMediaService;
    @Resource
    StrategyFactory strategyFactory;

    @Test
    public void test_taskSave() {
        MediaTaskSaveReq mediaTaskSaveReq = new MediaTaskSaveReq();
        mediaTaskSaveReq.setName("测试任务1");
        ArrayList<MediaObjectsReq> mediaObjectsReqs = new ArrayList<>();
        MediaObjectsReq mediaObjectsReq = new MediaObjectsReq();
        mediaObjectsReq.setName("分析对象1");

        mediaObjectsReq.setOrKeywords(Arrays.asList("长城", "长城新能源"));
        mediaObjectsReq.setFilterword(Collections.singletonList("长城油车"));
        mediaObjectsReq.setStartTime(Times.toEpochMilli(LocalDateTime.now().minusMonths(2L)));
        mediaObjectsReq.setEndTime(Times.toEpochMilli(LocalDateTime.now().minusMonths(1L)));
        mediaObjectsReq.setSourceCodes("1,2");
        mediaObjectsReqs.add(mediaObjectsReq);
        mediaTaskSaveReq.setObjReqList(mediaObjectsReqs);
        BaseResponseBean save = mediaService.save(mediaTaskSaveReq);
        log.info("测试结束");
    }

    @Test
    public void test_MediaVolumeStart() {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(707L);
        mediaVolumeStart.handle(handle, 1);
//        mediaVolumeStart.getPlatAllVolumeNum(handle, 1);
        log.info("测试结束");
    }

    @Test
    public void test_handleSinglePlatData() {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(464L);
//        mediaVolumeStart.handle(handle, 1);
        apiMediaService.handleSinglePlatData(2, handle);
        log.info("测试结束");
    }

    @Test
    public void test_test_MediaInteractionStart() {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(334L);
        mediaInteractionStart.handle(handle, 1);
        log.info("测试结束");
    }

    @Test
    public void test_mediaSentimentStart() {
        MediaObjects mediaObjects = mediaObjectsDomainService.getById(20L);
//        mediaSentimentStart.handle(mediaObjects);
        log.info("测试结束");
    }

    @Test
    public void test_MediaNoteStart() {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(334L);
        mediaNoteStart.handle(handle, 4);
        List<MediaNote> list = mediaNoteDomainService.findByHandleId(334L);

        log.info("请求结果: \n {}", JSON.toJSONString(list));
    }

    @Test
    public void test_MediaWordCouldStart() {
        MediaObjects mediaObjects = mediaObjectsDomainService.getById(22L);
//        mediaWordCouldStart.handle(mediaObjects);
        log.info("测试结束");
    }

    @Test
    public void test_asyncAllDataStoryCollectData() {
//        mediaService.asyncCollectData(Arrays.asList(10L),9L);
        log.info("测试结束");
    }

    /**
     * 同步并收集数说数据
     */
    @Test
    public void test_MediaAllDataStart() {
        MediaObjects mediaObjects = mediaObjectsDomainService.getById(10L);
//        mediaAllDataStart.handle(mediaObjects);
        log.info("测试结束");
    }

    /**
     * 同步并收集数说数据
     */
    @Test
    public void test_computeAllVolume2Obj() {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(390L);
        List<MediaTrend> mediaTrendList = mediaTrendDomainService.findByHandleIds(Collections.singletonList(handle.getId()));
        mediaAllDataStart.computeAllVolume2Obj(mediaTrendList, handle);
        log.info("测试结束");
    }

    @Test
    public void test_MediaNoteDayStart() {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(707L);
        int code = 1;
        MediaTrend mediaTrend = mediaTrendDomainService.findByHandleIdAndSource(handle.getId(), code);
        List<String> strings = JSON.parseArray(mediaTrend.getHotDay(), String.class);
        AtomicInteger atomicInteger = new AtomicInteger(0);
        mediaNoteDayStart.singleHotNoteDay(mediaTrend, mediaNoteDayStart.getDataStoryFixedParam(handle), atomicInteger, strings.get(0), code);
        log.info("测试结束,atomicInteger={}", atomicInteger.get());
    }

    /**
     * 同步并收集数说数据
     */
    @Test
    public void test_updateWordCloud() {
        Integer code = 0;
        MediaObjects mediaObjects = mediaObjectsDomainService.getById(22L);
        UpdateWordCloudReq updateWordCloudReq = new UpdateWordCloudReq();
        updateWordCloudReq.setImgUrl(mediaObjects.getImg());
        updateWordCloudReq.setMediaObjectsId(mediaObjects.getId());
        updateWordCloudReq.setSourceCode(code);
        MediaWord word = mediaWordDomainService.findByObjIdAndSource(mediaObjects.getId(), code);

        updateWordCloudReq.setWords(JSON.parseArray(word.getAllWord(), KeyValue.class));

//        mediaService.updateWordCloud(updateWordCloudReq);
        log.info("测试结束");
    }

    @Test
    public void test_StrategyFactory() {
        List<MediaTaskHandle> handle = mediaTaskHandleDomainService.getRunningTaskHandleByTask(294L, null);
//        strategyFactory.noticeFs(handle);
        log.info("测试结束");
    }

//    @Test
//    public void test_AndKeywords() {
//        List<MediaObjects> byNotNullAndKeywords = mediaObjectsDomainService.findByNotNullAndKeywords();
//
//        for (MediaObjects mobj : byNotNullAndKeywords) {
//            String andKeywords = mobj.getAndKeywords();
//            if (StringUtils.isNotBlank(andKeywords)) {
//                List<String> andKeywordsList = JSON.parseArray(andKeywords, String.class);
//                List<List<String>> objects = new ArrayList<>();
//                objects.add(andKeywordsList);
//                mobj.setAndKeywords(JSON.toJSONString(objects));
////                mediaObjectsDomainService.lambdaUpdate()
////                        .set(MediaObjects::getAndKeywords, mobj.getAndKeywords())
////                        .eq(MediaObjects::getId, mobj.getId())
////                        .update();
//            }
//        }
//        log.info("测试结束");
//    }
}
