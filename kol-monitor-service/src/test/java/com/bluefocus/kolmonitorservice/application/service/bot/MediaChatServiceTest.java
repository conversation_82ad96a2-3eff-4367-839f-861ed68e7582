package com.bluefocus.kolmonitorservice.application.service.bot;

import com.alibaba.fastjson.JSONArray;
import com.bluefocus.kolmonitorservice.application.service.media.impl.MediaAllDataStart;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @author: yjLiu
 * @date: 0022 2024/10/22 11:50
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class MediaChatServiceTest {

    @Resource
    MediaAllDataStart mediaAllDataStart;

    @Test
    public void test_contentBuild() {
//        testStart1(Arrays.asList(652L));
    }

    private void testStart1(List<Long> asList) {
        for (Long mediaObjectsId : asList) {
            JSONArray allContentBuild = mediaAllDataStart.allContentBuild(mediaObjectsId);
            System.out.println(mediaObjectsId + "结果：" + allContentBuild.toJSONString());
            System.out.println("");
        }
    }

}
