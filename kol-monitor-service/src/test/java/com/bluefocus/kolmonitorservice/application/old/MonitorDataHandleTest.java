package com.bluefocus.kolmonitorservice.application.old;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bluefocus.kolmonitorinterface.dto.excel.ExcelTrendValue;
import com.bluefocus.kolmonitorinterface.dto.media.KeyValue;
import com.bluefocus.kolmonitorservice.domain.article.ArticleDomainService;
import com.bluefocus.kolmonitorservice.domain.article.entity.ArticleDetail;
import com.bluefocus.kolmonitorservice.domain.project.ProjectDomainService;
import com.bluefocus.kolmonitorservice.domain.project.entity.Project;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * @author: yjLiu
 * @date: 0022 2024/10/22 11:50
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class MonitorDataHandleTest {

    @Resource
    ArticleDomainService articleDomainService;
    @Resource
    ProjectDomainService projectDomainService;

    private final ExecutorService threadPool = Executors.newFixedThreadPool(10);

    @Test
    public void test_contentBuild() {
        int pageNum = 1;
        int pageSize = 10;

        while (true) {
            IPage<Project> projectList = projectDomainService.findProjectList(null, pageNum, pageSize);
            List<Project> records = projectList.getRecords();

            if (CollectionUtil.isEmpty(records)) {
                log.info("没有更多数据，退出循环");
                break;
            }

            Set<Long> projectIds = records.stream()
                    .map(Project::getId)
                    .collect(Collectors.toSet());

            // 处理笔记数据
            for (Long projectId : projectIds) {
                List<ArticleDetail> articleDetailList = articleDomainService.findDetailByProjectId(projectId);
                try {
//                    threadPool.submit(() -> {
//                        int fail = handleArticle(articleDetailList);
//                        log.info("笔记处理完成：{},总数：{}, 笔记失败数量:{}", projectId, articleDetailList.size(), fail);
//                    });
                } catch (Exception e) {
                    log.error("处理笔记异常：{},err", projectId, e);
                }
            }

            // todo 处理项目数据
            if (records.size() < pageSize) {
                log.info("最后一页数据处理完成，退出循环");
                break;
            }
            pageNum++;
        }
    }


    private int handleArticle(List<ArticleDetail> articleDetailList) {
        int failNum = 0;
        for (ArticleDetail articleDetail : articleDetailList) {
            List<ExcelTrendValue> excelTrendValues = new ArrayList<>();
            if (StringUtils.isNotEmpty(articleDetail.getTendObj())) {
                try {
                    if (JSON.isValidArray(articleDetail.getTendObj())) {
                        continue;
                    }

                    TrendObjOld trendObjOld = JSON.parseObject(articleDetail.getTendObj(), TrendObjOld.class);
                    List<String> keys = trendObjOld.getKeys().stream().sorted().collect(Collectors.toList());
                    for (TrendObjValueOld trendObjValueOld : trendObjOld.getData_arr()) {
                        String name = trendObjValueOld.getName();
                        ExcelTrendValue excelTrendValue = new ExcelTrendValue();
                        excelTrendValue.setName(name);
                        List<String> values = trendObjValueOld.getValues();
                        ArrayList<KeyValue<String, Number>> keyValues = new ArrayList<>();
                        for (int i = 0; i < keys.size(); i++) {
                            KeyValue<String, Number> keyValue = new KeyValue<>();
                            keyValue.setKey(keys.get(i));
                            if (values.size() > i) {
                                keyValue.setValue(NumberUtils.createNumber(values.get(i)));
                            }
                            keyValues.add(keyValue);
                        }
                        excelTrendValue.setDataArr(keyValues);
                        excelTrendValues.add(excelTrendValue);
                    }
                } catch (Exception e) {
                    log.error("处理笔记异常：{},err", articleDetail.getArticleId(), e);
                    failNum = failNum + 1;
                }
            }
            articleDetail.setTendObj(JSON.toJSONString(excelTrendValues));
            articleDomainService.updateDetailById(articleDetail);
            log.info("笔记处理完成：{}", JSON.toJSONString(excelTrendValues));
        }
        return failNum;
    }

    @Test
    public void test_excel() {
//        List<ArticleDetail> articleDetailList = articleDomainService.findDetailByProjectId(1);
        ArticleDetail detailDataById = articleDomainService.findDetailDataById(441L);
        handleArticle(Collections.singletonList(detailDataById));
    }

}
