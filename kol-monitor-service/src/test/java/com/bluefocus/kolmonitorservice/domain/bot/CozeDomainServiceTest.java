package com.bluefocus.kolmonitorservice.domain.bot;

import com.bluefocus.kolmonitorservice.base.util.FsRobotUtil;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.chat.coze.CozeDomainService;
import com.bluefocus.kolmonitorservice.domain.chat.coze.DefaultCozeClient;
import com.bluefocus.kolmonitorservice.domain.chat.coze.dto.CozeChatDo;
import com.bluefocus.kolmonitorservice.domain.chat.coze.resp.*;
import com.bluefocus.kolmonitorservice.domain.chat.mapper.MediaChatBotMapper;
import com.bluefocus.kolmonitorservice.domain.chat.service.MediaChatDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;


/**
 * @author: yjLiu
 * @date: 0022 2024/10/22 18:19
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class CozeDomainServiceTest {

    @Resource
    private CozeDomainService cozeDomainService;

    private DefaultCozeClient defaultCozeClient;
    private MediaChatBotMapper mediaChatBotMapper;
    private MediaChatDomainService mediaChatDomainService;
    private RedisUtils redisUtils;
    private FsRobotUtil fsRobotUtil;

//    @Before
//    public void setUp() {
//        // 初始化CozeDomainService实例
//        defaultCozeClient = new DefaultCozeClient("https://api.coze.cn");
////        mediaChatDomainService = null;
//        cozeDomainService = new CozeDomainService(defaultCozeClient, mediaChatDomainService, mediaChatBotMapper, redisUtils, fsRobotUtil);
//        cozeDomainService.setBotId("7427038543463055386");
////        cozeDomainService.setToken("Bearer pat_oBv3RgYzPeE1EePork3Ifl6wqd4UHQJtaZPTpBgjCd8SIvZRFO5xFRSFCngwTvf6");
//    }

    @Test
    public void testConversationCreate_Success() {
        ConversationResp.ConversationObject result = cozeDomainService.conversationCreate(1L);
        log.info("测试结果：{}", result);
    }

    @Test
    public void testConversationDetail_Success() {
        ConversationResp.ConversationObject result = cozeDomainService.conversationDetail(1L);
        log.info("测试结果：{}", result);

    }

    @Test
    public void test_chatStream() {

//        CozeChatDo content = cozeDomainService.contentBuild("你是谁", 1L, "user");
//
//        content.setStream(true);
//        // Act
//        Flux<CozeChatStreamResp.ChatDetail> chatObjectFlux = cozeDomainService.chatStream(7428548628614266932L, content);
//        log.info("测试结果：{}", chatObjectFlux);
////        StepVerifier
//        CozeChatStreamResp.ChatDetail chatObject = chatObjectFlux.blockLast();
//
//        log.info("测试结果：{}", chatObject);
    }

    @Test
    public void testChatCreate_Success() {

        CozeChatDo content = cozeDomainService.contentBuild("test-content", 1L, "user");

        // Act
        CozeChatResp.ChatObject result = cozeDomainService.chatCreate(1L, 7428548628614266932L, content);
        log.info("测试结果：{}", result);
    }

    @Test
    public void testChatStatus_Success() {

        // Act
        CozeChatResp.ChatObject result = cozeDomainService.chatStatus(7428548628614266932L, 7428548771769892864L);
        log.info("测试结果：{}", result);
    }

    @Test
    public void testChatDetail_Success() {
        // Act
        String result = cozeDomainService.chatDetail(7428548628614266932L, 7428548771769892864L);
        log.info("测试结果：{}", result);
    }

    @Test
    public void testChatStop_Success() {

        String result = cozeDomainService.chatStop(1L, 1L);
        log.info("测试结果：{}", result);
    }

    @Test
    public void test_cozeToken() {

        String result = cozeDomainService.getCozeToken();
        log.info("测试结果：{}", result);
    }

}