package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryClient;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryVolumeTreadResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

@Slf4j
public class DataStoryVolumeTreadRequestTest {
    private Client client;

    @Before
    public void init() {
        client = new DefaultDataStoryClient("http://api.dc.datastory.com.cn/api/");
    }

    @Test
    public void check() throws ApiException {
        DataStoryEntity req = new DataStoryEntity();
        req.setKeyword("凡人修仙传");
        req.setStartTime(1719244800000L);
        req.setEndTime(1719547199000L);
        req.setSource(ExtraConditionEnum.BLIBLI.getKey());
        DataStoryVolumeTreadResponse execute = client.doExecute(new DataStoryVolumeTreadRequest(req));
        log.info("测试结果={}", JSON.toJSONString(execute));
    }
}