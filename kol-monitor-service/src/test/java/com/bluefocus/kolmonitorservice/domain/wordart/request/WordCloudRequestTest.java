package com.bluefocus.kolmonitorservice.domain.wordart.request;

import com.bluefocus.kolmonitorservice.base.util.ImageUtils;
import com.bluefocus.kolmonitorservice.domain.wordart.DefaultWordCloudClient;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordCloudResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.util.HashMap;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * URL：http://***********:1034/generate
 * Request Headers
 * Content-Type: application/json
 * User-Agent: PostmanRuntime/7.39.0
 * Host: ***********:1034
 * Accept-Encoding: gzip, deflate, br
 * Connection: keep-alive
 * Content-Length: 1443
 * 请求方式：post
 * 参数为包含  url、data 字段的JSON格式的数据
 * 返回值为：
 * {'code':401,'data':'图片获取失败'}
 * {'code':400,'data':'词云图生成异常'}
 * {'code':200,'data':'xxxx' } 表示成功
 */
@Slf4j
public class WordCloudRequestTest {

    private DefaultWordCloudClient client;

    @Before
    public void setUp() throws Exception {
        client = new DefaultWordCloudClient("http://***********:1034/");
    }

    @Test
    public void test_WordCloudRequest() {

//        String s = FileUtil.readString("D://wordcloud字节流.txt", StandardCharsets.UTF_8);
//        JSONObject jsonObject = JSON.parseObject(s);
//        byte[] bytes = ImageUtils.base64Decode2Bytes(jsonObject.getString("data"));
//        ImageUtils.saveByteArrayToLocalFile(bytes, String.format("./%d.png", finalI));

        int threadCount = 48;
        String defaultImg = "https://bluefocus-test.oss-cn-beijing.aliyuncs.com/media/wordimg.png";
        WordCloudGenRequest request = getWordCloudGenRequest(defaultImg);
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

        for (int i = 1; i <= threadCount; i++) {
            int finalI = i;
            executorService.submit(() -> {
                log.info("第{}次请求wordCloud", finalI);
                WordCloudResponse execute;
                try {
                    long s = System.currentTimeMillis();
                    execute = client.doExecute(request);
                    if (execute.getCode().equals("200")) {
                        byte[] bytes = ImageUtils.base64Decode2Bytes(execute.getData());
                        BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
                        log.info("第{}次，请求成功，宽{}，高{}，执行时间：{}s", finalI, image.getWidth(), image.getHeight(), (System.currentTimeMillis() - s) / 1000);
                        ImageUtils.saveByteArrayToLocalFile(bytes, String.format("./%d.png", finalI));
                    } else {
                        log.warn("第{}次，请求异常，code={}，时间{}s", finalI, execute.getCode(), (System.currentTimeMillis() - s) / 1000);
                    }
                } catch (Exception e) {
                    log.error("第{}次，请求失败：{}", finalI, e);
                }
            });
        }
        try {
            boolean allTasksCompleted = executorService.awaitTermination(1, TimeUnit.MINUTES);
            if (!allTasksCompleted) {
                log.warn("Not all tasks completed in the specified time.");
            } else {
                log.info("All tasks completed successfully.");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 恢复中断
            log.error("Thread was interrupted while waiting for task completion.", e);
        } finally {
            executorService.shutdown();
        }
        log.info("测试结束");

    }

    private WordCloudGenRequest getWordCloudGenRequest(String defaultImg) {
        WordCloudGenRequest request = new WordCloudGenRequest();
        request.setUrl(defaultImg);
        request.setData(buildWords());
        return request;
    }

    private static Map<String, Long> buildWords() {
        Map<String, Long> objects = new HashMap<>();
        objects.put("牛奶", 0L);
        objects.put("手串", 0L);
//        objects.put("减脂", 22918L);
//        objects.put("体重", 22861L);
//        objects.put("早餐", 21292L);
//        objects.put("打卡", 17313L);
//        objects.put("饮食", 17131L);
//        objects.put("纯牛奶", 17106L);
//        objects.put("美食", 14731L);
//        objects.put("小红书", 14000L);
//        objects.put("咖啡", 13211L);
//        objects.put("好喝", 10925L);
//        objects.put("面包", 10636L);
//        objects.put("鲜牛奶", 10092L);
//        objects.put("鸡蛋", 9596L);
//        objects.put("营养", 8897L);
//        objects.put("零食", 8752L);
//        objects.put("奶粉", 8603L);
//        objects.put("食物", 8020L);
//        objects.put("鲜奶", 7839L);
//        objects.put("酸奶", 7281L);
//        objects.put("运动", 7129L);
//        objects.put("口感", 6994L);
//        objects.put("吐司", 6670L);
//        objects.put("饮品", 5591L);
//        objects.put("奶茶", 5524L);
//        objects.put("探店", 5460L);
//        objects.put("日记", 5362L);
//        objects.put("水牛奶", 5284L);
//        objects.put("黑咖啡", 5221L);
//        objects.put("加餐", 5176L);
//        objects.put("燕麦", 5054L);
//        objects.put("黄油", 5004L);
//        objects.put("拿铁", 4958L);
//        objects.put("水果", 4801L);
//        objects.put("平价", 4730L);
//        objects.put("配料表", 4682L);
//        objects.put("巧克力", 4519L);
//        objects.put("碳水", 4320L);
//        objects.put("儿童", 4285L);
//        objects.put("皮肤", 4244L);
        return objects;
    }


    @Test
    public void getUrl() {
    }
}