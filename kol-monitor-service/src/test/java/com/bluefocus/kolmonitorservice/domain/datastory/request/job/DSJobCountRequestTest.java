package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryClient;
import org.junit.Before;
import org.junit.Test;


public class DSJobCountRequestTest {
    private JobDataStoryClient client;

    @Before
    public void setUp() throws Exception {
        client = new JobDataStoryClient("https://dc.datastory.com.cn/");
        client.setApiServerUrl("http://api.dc.datastory.com.cn/api/");
    }


    @Test
    public void check() throws ApiException {
        DSJobCountRequest request = new DSJobCountRequest();
        request.setToken("a935217ef38df028524f77f3c727166a");
        request.setJobId(7352146L);
        request.setStartTime("20240527000000");
        request.setEndTime("20240527235959");
//        System.out.println(JSON.toJSONString(client.doExecute(request)));
    }
}