package com.bluefocus.kolmonitorservice.domain.datastory.request;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.client.Client;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.datastory.DefaultDataStoryClient;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.DataStoryEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.DataStoryTotalVolumeResponse;
import org.junit.Before;
import org.junit.Test;

import java.util.Collections;

public class DataStoryTotalVolumeRequestTest {
    private Client client;

    @Before
    public void init(){
        client = new DefaultDataStoryClient("http://api.dc.datastory.com.cn/api/socialmedia/");
    }

    @Test
    public void check() throws ApiException {
        DataStoryEntity req = new DataStoryEntity();
        req.setKeyword("华为");
        req.setStartTime(1677859200000L);
        req.setEndTime(1680537599499L);
        req.setFilterWord("手表");
        req.setSources(Collections.singletonList(ExtraConditionEnum.XIAOHONGSHU.getKey()));
        DataStoryTotalVolumeResponse execute = client.doExecute(new DataStoryTotalVolumeRequest(req));
        System.out.println( JSON.toJSONString(execute));
    }
}