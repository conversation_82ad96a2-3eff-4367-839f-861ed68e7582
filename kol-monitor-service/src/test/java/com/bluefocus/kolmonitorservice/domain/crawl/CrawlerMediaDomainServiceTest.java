package com.bluefocus.kolmonitorservice.domain.crawl;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.application.service.media.impl.CrawlerMediaService;
import com.bluefocus.kolmonitorservice.base.util.RedisUtils;
import com.bluefocus.kolmonitorservice.domain.crawl.common.CrawlerCount;
import com.bluefocus.kolmonitorservice.domain.crawl.req.CrawlerMediaReq;
import com.bluefocus.kolmonitorservice.domain.crawl.resp.CrawlerMediaResp;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.ExtraConditionEnum;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.Redisson;
import org.redisson.api.RBlockingQueue;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class CrawlerMediaDomainServiceTest {

    @Resource
    MediaObjectsDomainService mediaObjectsDomainService;
    @Resource
    MediaTaskHandleDomainService mediaTaskHandleDomainService;

    @Resource
    private CrawlerMediaDomainService service;

    @Resource
    RedisUtils redisUtils;

    @Value("${dataStory.clawl.serverUrls}")
    private List<String> crawlerUrls;

    private String crawlerRedisKey = "DATA:STORY:CRAWLER:REDIS:KEY";

    @Test
    public void getCrawlerDataStory() {
        List<Thread> threads = new ArrayList<>();
        // 3个线程调用
        Arrays.asList(11L, 18L, 21L, 28L, 27L).forEach(id -> {
            threads.add(new Thread(() -> {
                MediaTaskHandle handle = mediaTaskHandleDomainService.getById(id);
                List<String> sourceNames = Arrays.stream(handle.getSources().split(",")).map(k -> ExtraConditionEnum.codeOf(Integer.valueOf(k))).collect(Collectors.toList());
                CrawlerMediaReq dataParam = service.getCrawlerDataParam(handle, sourceNames);
                CrawlerMediaResp crawlerDataStory = service.getCrawlerDataStory(dataParam, handle.getId());
                System.out.printf("ThreadID: %s now: %s id: %d request: %s, result: %s %n", Thread.currentThread().getId(), new Date()
                        , id, JSON.toJSONString(handle), JSON.toJSONString(crawlerDataStory));
            }));
        });
        // 等待Thread 跑完
        threads.forEach(Thread::start);
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });

    }

    @Test
    public void redisTest() throws Exception {
        Config config = new Config();
        config.useSingleServer().setAddress("redis://**********:6379").setPassword("blue@test2021").setDatabase(10);
        RedissonClient redisUtils = Redisson.create(config);
        List<Thread> threads = new ArrayList<>();
        List<String> crawlerUrls = Arrays.asList("http://***********:9979/juheMain", "http://***********:9980/juheMain", "http://***********:9978/juheMain");

        for (int j = 0; j < 3000L; j++) {
            new Thread(() -> {
                int size = crawlerUrls.size();
                //CrawlerCount.increment() 对size 取余，保证每个url被访问到
                int v = CrawlerCount.increment();
                int i = v % size;
                System.out.printf("Thread: %s value : %d url: %s %n", Thread.currentThread().getId(), v, crawlerUrls.get(i));
            }).start();
//            threads.add(new Thread(() -> {
            //                try {
//                    MD5 md5 = MD5.create();
//                    int lockTimes = 30;
//                    RLock lock = null;
//                    String crawlerUrl = null;
//                    while (lock == null && lockTimes >= 0) {
//                        int randomInt = new Random().nextInt(1000) + 1;
//                        Thread.sleep(randomInt);
//                        for (int i = 0; i < crawlerUrls.size(); i++) {
//                            crawlerUrl = crawlerUrls.get(i);
//                            lock = redisUtils.getLock(crawlerRedisKey + ":" + md5.digestHex(crawlerUrls.get(i), CharsetUtil.UTF_8));
//                            if (!lock.isLocked()) {
//                                lock.lock(1, TimeUnit.MINUTES);
//                                break;
//                            } else {
//                                lock = null;
//                                crawlerUrl = null;
//                            }
//                        }
//                        if (lock == null) {
//                            Thread.sleep(1000L);
//                            lockTimes--;
//                        }
//                        System.out.printf("final Thread:%s now: %s ; isLocked : %s result: %s; %n", Thread.currentThread().getId(), new Date(), lock, crawlerUrl);
//                    }
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//            }));

        }

        // 等待Thread 跑完
        threads.forEach(Thread::start);
        threads.forEach(thread -> {
            try {
                thread.join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });


//        if (lock != null) {
//            lock.unlock();
//        }

//        RBlockingQueue<String> blockQueue = redisUtils.getBlockingQueue(crawlerRedisKey);
////        blockQueue.addAll(crawlerUrls);
//
//        System.out.printf("KEY:%s value: %s%n", crawlerRedisKey, blockQueue.poll(30, TimeUnit.SECONDS));
//        System.out.printf("KEY:%s value: %s%n", crawlerRedisKey, blockQueue.poll(30, TimeUnit.SECONDS));
//        System.out.printf("KEY:%s value: %s%n", crawlerRedisKey, blockQueue.poll(30, TimeUnit.SECONDS));
//        System.out.printf("KEY:%s value: %s%n", crawlerRedisKey, blockQueue.poll(30, TimeUnit.SECONDS));
    }

    @Resource
    private CrawlerMediaService crawlerMediaService;

    @Test
    public void handleTest() {
//        crawlerMediaService.handle(mediaObjectsDomainService.getById(317L));
    }
}