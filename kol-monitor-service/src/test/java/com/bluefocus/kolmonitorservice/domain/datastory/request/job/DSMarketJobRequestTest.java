package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryClient;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.MarketJobScopeEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.enums.TitTemplateIdEnum;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSMarketJobResponse;
import org.junit.Before;
import org.junit.Test;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DSMarketJobRequestTest {
    private JobDataStoryClient client;

    @Before
    public void setUp() throws Exception {
        client = new JobDataStoryClient("https://dc.datastory.com.cn/");
    }

    @Test
    public void check() throws Exception {
        DSMarketJobRequest request = new DSMarketJobRequest();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        request.setJobName("测试任务-" + sdf.format(new Date()));
        request.setKeywords("红旗|红旗汽车");
        request.setAppId(MarketJobScopeEnum.XHS.getAppId());
        request.setScopeId(MarketJobScopeEnum.XHS.getScopeId());
        request.setTitTemplateId(TitTemplateIdEnum.Interaction_LT_1.getValue());
        request.setStartDataTime(1716739200000L);
        request.setEndDataTime(1717171199000L);
//        request.putHeaderParam("Authorization", "");
//        request.putHeaderParam("Content-Type", ContentType.JSON.getValue());
//        DSMarketJobResponse execute = client.doExecute(request);
//        System.out.println(JSON.toJSONString(execute));
    }
}