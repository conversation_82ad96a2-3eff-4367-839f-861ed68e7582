package com.bluefocus.kolmonitorservice.domain.wordart.request;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bluefocus.common.Assert;
import com.bluefocus.kolmonitorservice.base.common.OSSUploadCommon;
import com.bluefocus.kolmonitorservice.base.util.ImageUtils;
import com.bluefocus.kolmonitorservice.base.util.PictureUtils;
import com.bluefocus.kolmonitorservice.domain.wordart.DefaultWordArtClient;
import com.bluefocus.kolmonitorservice.domain.wordart.entity.WordArtWordEntity;
import com.bluefocus.kolmonitorservice.domain.wordart.response.WordArtGenerateResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

//@RunWith(SpringRunner.class)
//@SpringBootTest
//@Slf4j
//@RequiredArgsConstructor
@Slf4j
public class WordArtGenerateRequestTest {

    //    @Autowired
    private OSSUploadCommon ossUploadCommon;

    private DefaultWordArtClient client;

    private String filePath = "media/1/1640972-20201020130944263-443219511.png";
    private String fileName = "1640972-20201020130944263-443219511.png";

    @Before
    public void setUp() throws Exception {
        client = new DefaultWordArtClient("https://wordart.com/api/", "K:zlmooQSQO#~%V&s*~?m1GTd5e=^E");
    }

    @Test
    public void check() throws Exception {
        Long S = System.currentTimeMillis();
        // 纯紫色
        String imageUrl = "https://bluefocus-test.oss-cn-beijing.aliyuncs.com/media/wordimg.png";
        // 纯绿色
//        String imageUrl = "https://bluefocus-test.oss-cn-beijing.aliyuncs.com/media/100040817/6ab907a4-b2c8-4839-82de-a28cb45eb898.jpg?Expires=2035543223&OSSAccessKeyId=LTAI4GCx3e8N5Ffnh7WfwhwN&Signature=3NqIBh%2FdExuPWkWK6SdKUQVvgbk%3D";

//        String imageUrl = "./5.png";
//        String imageUrl = "https://dalleprodsec.blob.core.windows.net/private/images/d7dec2f4-30cf-42a8-99cd-0012374945f4/generated_00.png?se=2024-06-24T02%3A39%3A15Z&sig=CiRIJC9aeC9G5XwgDNM5Cp4MBuXfcm9Mj09Pd%2Blz84c%3D&ske=2024-06-28T09%3A44%3A30Z&skoid=e52d5ed7-0657-4f62-bc12-7e5dbb260a96&sks=b&skt=2024-06-21T09%3A44%3A30Z&sktid=33e01921-4d64-4f8c-a055-5bdaffd5e33d&skv=2020-10-02&sp=r&spr=https&sr=b&sv=2020-10-02";
        byte[] baos = ImageUtils.imageUrlToBytes(imageUrl);
//        File file = new File(imageUrl);
//        FileInputStream fileInputStream = new FileInputStream(imageUrl);

        // 保存图片
        ImageUtils.saveByteArrayToLocalFile(baos, "./10.png");
        ImageIO.read(new ByteArrayInputStream(baos));
        baos = PictureUtils.transAlpha(baos);
        baos = PictureUtils.compressPicture(baos, 0.9);

        WordArtGenerateRequest request = new WordArtGenerateRequest();
        request.setImageBase64(ImageUtils.bytesToBase64(baos));
        request.setWords(buildWords());

        log.info("请求参数：{}", JSON.toJSONString(request.getParams()));
        WordArtGenerateResponse execute = client.doExecute(request);
//        log.info("响应参数：{}", JSON.toJSONString(execute));
        byte[] bytes = ImageUtils.base64Decode2Bytes(execute.getBody());
//        String s = FileUtil.readString("D://wordcloud字节流.txt", StandardCharsets.UTF_8);
//        JSONObject jsonObject = JSON.parseObject(s);
//        byte[] bytes = ImageUtils.base64Decode2Bytes(jsonObject.getString("data"));

        BufferedImage image = ImageIO.read(new ByteArrayInputStream(bytes));
        log.info("长度：{}", image.getWidth(),image.getHeight());

        ImageUtils.saveByteArrayToLocalFile(bytes, "./11.png");

//        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
//        String s = ossUploadCommon.uploadImg2Oss(inputStream, filePath, fileName);
//        System.out.println(s);
        Long e = System.currentTimeMillis();
        System.out.println("执行时间" + (double) (e - S) / 1000 / 60 + "s");
    }

    public static void main(String[] args) {
        List<WordArtWordEntity> wordArtWordEntities = buildWords();
        System.out.println(JSON.toJSONString(wordArtWordEntities));
    }

    private static List<WordArtWordEntity> buildWords() {
        List<WordArtWordEntity> objects = new ArrayList<>();
        objects.add(new WordArtWordEntity("牛奶", 97516L));
        objects.add(new WordArtWordEntity("手串", 97516L));
        objects.add(new WordArtWordEntity("减脂", 22918L));
        objects.add(new WordArtWordEntity("体重", 22861L));
        objects.add(new WordArtWordEntity("早餐", 21292L));
        objects.add(new WordArtWordEntity("打卡", 17313L));
        objects.add(new WordArtWordEntity("饮食", 17131L));
        objects.add(new WordArtWordEntity("纯牛奶", 17106L));
        objects.add(new WordArtWordEntity("美食", 14731L));
        objects.add(new WordArtWordEntity("小红书", 14000L));
        objects.add(new WordArtWordEntity("咖啡", 13211L));
        objects.add(new WordArtWordEntity("好喝", 10925L));
        objects.add(new WordArtWordEntity("面包", 10636L));
        objects.add(new WordArtWordEntity("鲜牛奶", 10092L));
        objects.add(new WordArtWordEntity("鸡蛋", 9596L));
        objects.add(new WordArtWordEntity("营养", 8897L));
        objects.add(new WordArtWordEntity("零食", 8752L));
        objects.add(new WordArtWordEntity("奶粉", 8603L));
        objects.add(new WordArtWordEntity("食物", 8020L));
        objects.add(new WordArtWordEntity("鲜奶", 7839L));
        objects.add(new WordArtWordEntity("酸奶", 7281L));
        objects.add(new WordArtWordEntity("运动", 7129L));
        objects.add(new WordArtWordEntity("口感", 6994L));
        objects.add(new WordArtWordEntity("吐司", 6670L));
        objects.add(new WordArtWordEntity("饮品", 5591L));
        objects.add(new WordArtWordEntity("奶茶", 5524L));
        objects.add(new WordArtWordEntity("探店", 5460L));
        objects.add(new WordArtWordEntity("日记", 5362L));
        objects.add(new WordArtWordEntity("水牛奶", 5284L));
        objects.add(new WordArtWordEntity("黑咖啡", 5221L));
        objects.add(new WordArtWordEntity("加餐", 5176L));
        objects.add(new WordArtWordEntity("燕麦", 5054L));
        objects.add(new WordArtWordEntity("黄油", 5004L));
        objects.add(new WordArtWordEntity("拿铁", 4958L));
        objects.add(new WordArtWordEntity("水果", 4801L));
        objects.add(new WordArtWordEntity("平价", 4730L));
        objects.add(new WordArtWordEntity("配料表", 4682L));
        objects.add(new WordArtWordEntity("巧克力", 4519L));
        objects.add(new WordArtWordEntity("碳水", 4320L));
        objects.add(new WordArtWordEntity("儿童", 4285L));
        objects.add(new WordArtWordEntity("皮肤", 4244L));
        return objects;
    }


    @Test
    public void getUrl() {
    }
}