package com.bluefocus.kolmonitorservice.domain.crawl;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.application.service.media.MediaService;
import com.bluefocus.kolmonitorservice.application.service.media.impl.CrawlerMediaService;
import com.bluefocus.kolmonitorservice.base.util.HttpUtil;
import com.bluefocus.kolmonitorservice.domain.crawl.req.CrawlerMediaReq;
import com.bluefocus.kolmonitorservice.domain.crawl.resp.CrawlerMediaResp;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;

/**
 * @author: yjLiu
 * @date: 0005 2024/6/5 20:33
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class CrawlDataTest {
    @Resource
    MediaObjectsDomainService mediaObjectsDomainService;
    @Resource
    CrawlerMediaService crawlerMediaService;
    @Resource
    MediaTaskHandleDomainService mediaTaskHandleDomainService;


    public static void main(String[] args) {
        CrawlerMediaReq crawlerMediaReq = new CrawlerMediaReq();

        crawlerMediaReq.setSources(Arrays.asList("xiaohongshu"));
        crawlerMediaReq.setStartTime(1714492800000L);
        crawlerMediaReq.setEndTime(1717084800000L);
        crawlerMediaReq.setFilterWord("荣耀");
        crawlerMediaReq.setKeyword("华为手机|任正非");
//        String res = HttpUtil.sendPost("http://***********:9978/juheMain", JSON.toJSONString(crawlerMediaReq), ContentType.APPLICATION_JSON);
        String res = HttpUtil.sendPost("http://***********:9980/juheMain", "{\"endTime\":1727711999000,\"filterword\":\"自制薯片|代言人|微博|转发\",\"keyword\":\"薯片+原味|薯片原味\",\"sources\":[\"xiaohongshu\",\"shortVideo\",\"weibo\",\"video\"],\"startTime\":1719763200000}", ContentType.APPLICATION_JSON);

        CrawlerMediaResp crawlerMediaResp = JSON.parseObject(res, CrawlerMediaResp.class);
        System.out.println(crawlerMediaResp);
    }

    @Test
    public void test_CrawlData() {
        MediaObjects byId = mediaObjectsDomainService.getById(11L);
//        crawlerMediaService.handle(byId);
        log.info("测试结束");
    }

    @Test
    public void test_doHandle() {
        MediaTaskHandle byId = mediaTaskHandleDomainService.getById(6909L);
        MediaObjects obj = mediaObjectsDomainService.getById(byId.getMediaObjectsId());
        crawlerMediaService.doHandle(obj, byId);
//        crawlerMediaService.handle(byId);
        log.info("测试结束");
    }
}
