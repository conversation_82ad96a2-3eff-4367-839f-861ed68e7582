package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryClient;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSJobStatusDetailResponse;
import org.junit.Before;
import org.junit.Test;

public class DSJobStatusDetailRequestTest {
    private JobDataStoryClient client;

    @Before
    public void setUp() throws Exception {
        client = new JobDataStoryClient("https://dc.datastory.com.cn/");
    }

    @Test
    public void check() throws ApiException {
        DSJobStatusDetailRequest request = new DSJobStatusDetailRequest();
        request.setJobId(7354159L);
        request.putHeaderParam("Authorization", "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************.6gdpjdWFHPeEIV40dun2cLuajNjQ-buQGwQKJF4U9L1w1RDJMqbhZ0ui3bwfUuhO61K_pWw4mBdmIX0zJMlr1A");
        DSJobStatusDetailResponse execute = client.doExecute(request);

    }
}