package com.bluefocus.kolmonitorservice.domain.datastory.request.job;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.exception.ApiException;
import com.bluefocus.kolmonitorservice.domain.datastory.JobDataStoryClient;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.job.JobDetailProFiltersListStrEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.entity.job.JobDetailScheduleEntity;
import com.bluefocus.kolmonitorservice.domain.datastory.response.job.DSJobDetailResponse;
import org.junit.Before;
import org.junit.Test;

import java.util.List;

public class DSJobDetailRequestTest {
    private JobDataStoryClient client;

    @Before
    public void setUp() throws Exception {
        client = new JobDataStoryClient("https://dc.datastory.com.cn/");
    }

    @Test
    public void check() throws ApiException {
        DSJobDetailRequest request = new DSJobDetailRequest();
        request.setJobId(7354159L);
        request.putHeaderParam("Authorization", "eyJhbGciOiJIUzUxMiJ9.*******************************************************************************************************************************************************************.6gdpjdWFHPeEIV40dun2cLuajNjQ-buQGwQKJF4U9L1w1RDJMqbhZ0ui3bwfUuhO61K_pWw4mBdmIX0zJMlr1A");
        DSJobDetailResponse response = client.doExecute(request);
        DSJobDetailResponse.ResultDTO data = response.getData();
        JobDetailScheduleEntity scheduleEntity = JSON.parseObject(data.getSchedule(), JobDetailScheduleEntity.class);
        System.out.println("schedule:" + JSON.toJSONString(scheduleEntity));

        String proFiltersListStr = data.getProFiltersListStr();
        System.out.println("proFiltersListStr: " + proFiltersListStr);

        List<JobDetailProFiltersListStrEntity> proFiltersListStrEntities = JSON.parseArray(proFiltersListStr, JobDetailProFiltersListStrEntity.class);
        System.out.println("proFiltersListStr: " + JSON.toJSONString(proFiltersListStrEntities));

        System.out.println(JSON.toJSONString(response));
    }
}