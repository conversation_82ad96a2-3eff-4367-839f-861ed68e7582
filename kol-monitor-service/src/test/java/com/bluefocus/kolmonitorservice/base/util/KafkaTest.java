package com.bluefocus.kolmonitorservice.base.util;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.common.MediaMsgBody;
import com.bluefocus.kolmonitorservice.base.listener.media.*;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaObjects;
import com.bluefocus.kolmonitorservice.domain.media.entity.MediaTaskHandle;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaObjectsDomainService;
import com.bluefocus.kolmonitorservice.domain.media.service.MediaTaskHandleDomainService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * @author: yjLiu
 * @date: 0012 2024/6/12 18:34
 * @description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class KafkaTest {

    @Resource
    KafkaSender kafkaSender;
    @Resource
    MediaTaskHandleDomainService mediaTaskHandleDomainService;
    @Resource
    MediaHandleApiListener mediaHandleApiListener;
    @Resource
    MediaHandleWordCloudListener mediaHandleWordCloudListener;
    @Resource
    MediaHandleCrawlerListener mediaHandleCrawlerListener;
    @Resource
    MediaHandleTaskListener mediaHandleTaskListener;
    @Resource
    MediaHandleAnalyzeListener mediaHandleAnalyzeListener;
    @Resource
    MediaHandleNoteDayListener mediaHandleNoteDayListener;

    @Test
    public void test_KafkaSender() {
        Long handleId = 1966L;
        MediaMsgBody mediaMsgBody = getMediaMsgBody(handleId);

//        handleMq(mediaMsgBody, MediaHandleTaskListener.TOPIC);
        handleMq(mediaMsgBody, MediaHandleApiListener.TOPIC);
//        handleMq(mediaMsgBody, MediaHandleCrawlerListener.TOPIC);

        log.info("测试结束");
    }

    private MediaMsgBody getMediaMsgBody(Long handleId) {
        MediaTaskHandle handle = mediaTaskHandleDomainService.getById(handleId);
        return new MediaMsgBody(handle.getMediaObjectsId(), handle.getTaskId(), "KafkaTest", handle.getId());
    }

    public void handleMq(MediaMsgBody mediaMsgBody, String topic) {
        kafkaSender.sendByKey(topic, String.valueOf(mediaMsgBody.getMediaTaskId()), mediaMsgBody);
    }

    @Test
    public void test_KafkaListener() {
        Long handleId = 522L;
        MediaMsgBody mediaMsgBody = getMediaMsgBody(handleId);

//        mediaHandleApiListener.consumptionData(JSON.toJSONString(mediaMsgBody), null);
//        mediaHandleWordCloudListener.consumptionData(JSON.toJSONString(mediaMsgBody), null);
        mediaHandleCrawlerListener.consumptionData(JSON.toJSONString(mediaMsgBody), null);
//        mediaHandleTaskListener.consumptionData(JSON.toJSONString(mediaMsgBody), null);
//        mediaHandleAnalyzeListener.consumptionData(JSON.toJSONString(mediaMsgBody), null);
//        mediaHandleNoteDayListener.consumptionData(JSON.toJSONString(mediaMsgBody), null);

        log.info("测试结束");
    }


}
