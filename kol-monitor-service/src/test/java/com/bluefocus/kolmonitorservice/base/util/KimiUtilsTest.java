package com.bluefocus.kolmonitorservice.base.util;

import com.alibaba.fastjson.JSON;
import com.bluefocus.kolmonitorservice.base.enums.ChatRoleEnum;
import com.bluefocus.kolmonitorservice.domain.gptalk.entity.ChatMessage;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class KimiUtilsTest {
    private  KimiUtils kimiUtils ;
    @Before
    public void setUp() throws Exception {
        kimiUtils = new KimiUtils("sk-DaDSDAjip65didwgObe6s8WTsVYZxwI7ATHyytSqwt9ONKAr");
    }
    @Test
    public void callKimiAPI() {
        List<ChatMessage> chatMessages = new ArrayList<>();
        chatMessages.add(new ChatMessage(ChatRoleEnum.SYSTEM.getRole(),"----角色----\nDALL-E-3 prompt 转换器\n----任务----\n理解用户输入的内容,用英文输出符合DALL-E-3的prompt"));
        chatMessages.add(new ChatMessage(ChatRoleEnum.USER.getRole()," 给我一个以黑色为主题的剪影图，不需要复杂的元素，不需要理解图片内容描述的颜色。图片内容描述如下：一只蓝色企鹅"));
        kimiUtils.setMessages(chatMessages);
        kimiUtils.setN(5);
        System.out.println(JSON.parseObject(kimiUtils.callKimiAPI()));;
    }
}