package com.bluefocus.kolmonitorservice.base.util;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import lombok.Data;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @author: yjLiu
 * @date: 0016 2024/8/16 16:15
 * @description:
 */
public class ExcelTest {

    public static void main(String[] args) {
//        // 模拟从数据库中读取的数
        String s = FileUtil.readString("D:\\11111.txt", StandardCharsets.UTF_8);
        List<Trend> dataList = JSON.parseArray(s, Trend.class);
        long l = System.currentTimeMillis() / 60000;
        EasyExcel.write("D://output"+l+".xlsx", Trend.class).sheet("数据表声量").doWrite(dataList);

//        String word1 = FileUtil.readString("D:\\11111.txt", StandardCharsets.UTF_8);
//        EasyExcel.write("D://output"+ l +".xlsx", TrendWord.class).sheet("数据表word").doWrite(JSON.parseArray(word1, TrendWord.class));
    }

    @Data
    static class Trend{
        String date;
        String value;
    }

    @Data
    static class TrendWord{
        String label;
        String value;
    }
}
