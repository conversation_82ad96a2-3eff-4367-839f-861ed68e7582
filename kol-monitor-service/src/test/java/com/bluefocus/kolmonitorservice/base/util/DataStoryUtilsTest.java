package com.bluefocus.kolmonitorservice.base.util;

import com.bluefocus.kolmonitorservice.base.common.TokenCommon;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest()
@Slf4j
public class DataStoryUtilsTest {
    @Resource
    private DataStoryUtils dsu;

    private String serverUrl = "https://dc.datastory.com.cn/auth/obtain";
    private String username = "<EMAIL>";
    private String password = "xinhua.zhang1123456";

    @Before
    public void setUp() throws Exception {
//        dsu = new DataStoryUtils(username, password, serverUrl);
    }


    @Test
    public void login() {
        TokenCommon token = dsu.getToken();
        log.info("token:{}", token);
    }
}