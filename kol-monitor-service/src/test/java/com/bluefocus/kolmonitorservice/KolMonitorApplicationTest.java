package com.bluefocus.kolmonitorservice;

import cn.hutool.core.io.FileUtil;
import com.bluefocus.kolmonitorinterface.dto.req.ArticleUrlRequest;
import com.bluefocus.kolmonitorservice.application.service.ReportService;
import com.bluefocus.kolmonitorservice.application.service.plat.PlatResult;
import com.bluefocus.kolmonitorservice.application.service.plat.ThirdPlatFactory;
import com.bluefocus.kolmonitorservice.application.service.task.DataStoryTask;
import com.bluefocus.kolmonitorservice.application.service.task.EmailTask;
import com.bluefocus.kolmonitorservice.base.listener.FsNoteInfoListener;
import com.bluefocus.kolmonitorservice.base.listener.FsNotesListener;
import com.bluefocus.kolmonitorservice.base.listener.FsReportListener;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @Auther: yjLiu
 * @Date: 2023/3/14 11:28
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class KolMonitorApplicationTest {

    @Resource
    FsNoteInfoListener fsNoteInfoListener;
    @Resource
    FsNotesListener fsNotesListener;
    @Resource
    EmailTask emailTask;
    @Resource
    DataStoryTask dataStoryTask;
    @Resource
    FsReportListener fsReportListener;
    @Resource
    ReportService reportService;
    @Resource
    ThirdPlatFactory thirdPlatFactory;

    @Test
    public void testPlat() throws IOException {
        String msg1 = FileUtil.readString("D:\\1232.txt", StandardCharsets.UTF_8);
        fsNotesListener.consumptionData(msg1,null);
//        fsNoteInfoListener.consumptionData(msg2,null);
//        fsReportListener.consumptionData(msg3,null);
    }

    @Test
    public void test_Article() {
        ArticleUrlRequest articleUrlRequest = new ArticleUrlRequest();
        articleUrlRequest.setArticleUrl("https://v.kuaishou.com/LdB4Z5");

//        articleService.save(articleUrlRequest);
//
//        log.info("111");

    }

    @Test
    public void test_EmailTask() {
//        emailTask.emailTask();
    }

//    @Test
//    public void test_dataStoryTask() {
//        dataStoryTask.dataStoryTask();
//    }

}
