package com.bluefocus.kolmonitorservice.route;

import com.alibaba.fastjson.JSONObject;
import com.bluefocus.kolmonitorservice.domain.route.DefaultRouteClient;
import com.bluefocus.kolmonitorservice.domain.route.platform.datastory.req.DsJobDefaultRequest;
import com.bluefocus.kolmonitorservice.domain.route.platform.datastory.resp.DsJobDefaultResponse;
import org.junit.Before;
import org.junit.Test;

/**
 * @author: yjLiu
 * @date: 0003 2024/8/3 18:59
 * @description:
 */
public class DsJobDefaultRequestTest {

    DefaultRouteClient defaultRouteClient;
    @Before
    public void init(){
        defaultRouteClient = new DefaultRouteClient();
    }
    @Test
    public void check() throws Exception {
//        JSONObject jsonObject = new JSONObject();
//        DsJobDefaultRequest dsJobDefaultRequest = new DsJobDefaultRequest("get","123","json","token",jsonObject);
//        DsJobDefaultResponse dsJobDefaultResponse = defaultRouteClient.doExecute(dsJobDefaultRequest);

    }
}
